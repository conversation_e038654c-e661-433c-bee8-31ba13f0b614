# SRNR IT Solutions Dashboard - Authentication Setup Guide

This guide will help you set up the comprehensive authentication system with Supabase and role-based access control.

## Prerequisites

1. A Supabase account and project
2. Node.js and npm/yarn installed
3. The SRNR Dashboard project

## Step 1: Supabase Project Setup

### 1.1 Create a Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be fully initialized

### 1.2 Get Your Project Credentials
1. Go to your project settings
2. Navigate to "API" section
3. Copy the following:
   - Project URL
   - Anon public key
   - Service role key (keep this secret)

## Step 2: Environment Configuration

### 2.1 Update Environment Variables
Edit the `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_actual_supabase_service_role_key
```

**Important:** Replace the placeholder values with your actual Supabase credentials.

## Step 3: Database Schema Setup

### 3.1 Run the Database Schema
1. Open your Supabase project dashboard
2. Go to the "SQL Editor"
3. Copy the contents of `supabase-schema.sql` file
4. Paste and execute the SQL commands

This will create:
- `profiles` table with user information
- Row Level Security (RLS) policies
- User role enum (`admin`, `emp`)
- Automatic profile creation triggers
- Proper permissions

### 3.2 Verify Schema Creation
Check that the following were created:
- `public.profiles` table
- `user_role` enum type
- RLS policies
- Database functions and triggers

## Step 4: Authentication Configuration

### 4.1 Configure Supabase Auth Settings
In your Supabase dashboard:

1. Go to "Authentication" → "Settings"
2. Configure the following:

**Site URL:** `http://localhost:3000` (for development)

**Redirect URLs:** Add these URLs:
- `http://localhost:3000/auth/callback`
- `http://localhost:3000`
- Your production domain when deploying

**Email Templates:** Customize if needed

### 4.2 Disable Email Confirmation (Optional)
For development, you may want to disable email confirmation:
1. Go to "Authentication" → "Settings"
2. Turn off "Enable email confirmations"

**Note:** Re-enable this for production!

## Step 5: Create Your First Admin User

### 5.1 Method 1: Through Signup Form
1. Start your development server: `npm run dev`
2. Navigate to `http://localhost:3000/auth/signup`
3. Create a user account with your admin email
4. Go to Supabase dashboard → "Authentication" → "Users"
5. Find your user and note the User ID

### 5.2 Method 2: Update Role via SQL
1. In Supabase SQL Editor, run:
```sql
UPDATE public.profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## Step 6: Test the Authentication System

### 6.1 Test User Registration
1. Go to `/auth/signup`
2. Create a test employee account
3. Verify the user appears in Supabase dashboard

### 6.2 Test Role-Based Access
1. Login as admin → should redirect to `/admin`
2. Login as employee → should redirect to `/emp`
3. Try accessing admin routes as employee → should be blocked

### 6.3 Test User Management (Admin Only)
1. Login as admin
2. Navigate to "User Management" in sidebar
3. Create a new user
4. Change user roles
5. Verify changes in database

## Step 7: Features Overview

### Authentication Features
- ✅ Secure login/signup with Supabase
- ✅ Role-based access control (admin/employee)
- ✅ Protected routes with middleware
- ✅ Automatic redirects based on user role
- ✅ Session management

### User Interface Features
- ✅ User profile display in sidebars
- ��� Avatar support with fallback initials
- ✅ Logout functionality
- ✅ Consistent design with existing UI components

### Admin Features
- ✅ User management interface
- ✅ Create new users without email verification
- ✅ Change user roles
- ✅ View all users with search and filtering
- ✅ Role-based permissions

### Security Features
- ✅ Row Level Security (RLS) policies
- ✅ Protected API routes
- ✅ Secure password handling
- ✅ JWT-based authentication

## Step 8: Deployment Considerations

### 8.1 Environment Variables
Ensure all environment variables are set in your production environment.

### 8.2 Supabase Settings
Update your Supabase project settings:
- Add production URLs to redirect URLs
- Enable email confirmations
- Configure proper CORS settings

### 8.3 Database Policies
Review and test all RLS policies in production environment.

## Troubleshooting

### Common Issues

**1. "Invalid JWT" errors**
- Check that environment variables are correctly set
- Verify Supabase project URL and keys

**2. Users not being created**
- Check the database trigger is working
- Verify the `handle_new_user()` function exists

**3. Role-based access not working**
- Ensure RLS policies are enabled
- Check that user profiles have correct roles

**4. Middleware redirect loops**
- Verify middleware configuration
- Check that auth routes are in public routes list

### Getting Help

1. Check Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
2. Review the browser console for errors
3. Check Supabase dashboard logs
4. Verify database policies and permissions

## Security Best Practices

1. **Never expose service role key** in client-side code
2. **Use environment variables** for all sensitive data
3. **Enable email confirmation** in production
4. **Regularly review** user permissions and roles
5. **Monitor authentication logs** in Supabase dashboard
6. **Use HTTPS** in production
7. **Implement proper CORS** settings

## File Structure

```
├── app/
│   ├── auth/
│   │   ├── login/page.tsx          # Login page
│   │   └── signup/page.tsx         # Signup page
│   ├── admin/
│   │   └── user-management/page.tsx # Admin user management
│   ├── layout.tsx                  # Root layout with AuthProvider
│   └── page.tsx                    # Root page with role-based redirect
├── lib/
│   ├── auth-context.tsx            # Authentication context
│   ├── supabase.ts                 # Client-side Supabase client
│   └── supabase-server.ts          # Server-side Supabase client
├── middleware.ts                   # Route protection middleware
├── supabase-schema.sql             # Database schema
└── .env.local                      # Environment variables
```

This authentication system provides a solid foundation for your dashboard application with proper security, user management, and role-based access control.