# Vercel Deployment Guide

## Environment Variables Setup

To deploy this application on Vercel, you need to configure the following environment variables in your Vercel project settings:

### Required Environment Variables

1. **NEXT_PUBLIC_SUPABASE_URL**
   - Your Supabase project URL
   - Example: `https://abcdefghijklmnop.supabase.co`

2. **NEXT_PUBLIC_SUPABASE_ANON_KEY**
   - Your Supabase anonymous/public key
   - Example: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

3. **SUPABASE_SERVICE_ROLE_KEY** (Optional)
   - Your Supabase service role key for admin operations
   - Example: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### How to Add Environment Variables in Vercel

1. Go to your Vercel dashboard
2. Select your project
3. Go to Settings → Environment Variables
4. Add each variable with the appropriate values
5. Make sure to set them for all environments (Production, Preview, Development)

### Finding Your Supabase Keys

1. Go to your Supabase dashboard: https://supabase.com/dashboard
2. Select your project
3. Go to Settings → API
4. Copy the Project URL and anon/public key

## Build Configuration

The application has been configured to handle missing environment variables during the build process gracefully. The following changes were made:

- Modified Supabase client creation to return null during build time when environment variables are missing
- Updated middleware to skip authentication checks during build
- Added proper error handling in components that use Supabase

## Deployment Steps

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Configure the environment variables as described above
4. Deploy the application

The build should now complete successfully on Vercel without the Supabase environment variable errors.