"use client"

import { useState, useMemo } from "react"
import { motion } from "framer-motion"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  CalendarIcon,
  Search,
  Filter,
  Eye,
  Edit,
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Users,
  UserX
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Mock daily attendance data
const dailyAttendanceData = [
  {
    id: "EMP001",
    name: "John Smith",
    department: "Engineering",
    firstIn: "9:00 AM",
    lastOut: "6:30 PM",
    session1In: "9:00 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "6:30 PM",
    totalHours: "8h 30m",
    flags: ["on-time"],
    allSwipes: ["9:00 AM", "1:00 PM", "2:00 PM", "6:30 PM"]
  },
  {
    id: "EMP002",
    name: "Sarah Johnson",
    department: "Marketing",
    firstIn: "9:30 AM",
    lastOut: "6:00 PM",
    session1In: "9:30 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "6:00 PM",
    totalHours: "8h 00m",
    flags: ["late"],
    allSwipes: ["9:30 AM", "1:00 PM", "2:00 PM", "6:00 PM"]
  },
  {
    id: "EMP003",
    name: "Mike Wilson",
    department: "Sales",
    firstIn: "8:45 AM",
    lastOut: "5:30 PM",
    session1In: "8:45 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "5:30 PM",
    totalHours: "8h 00m",
    flags: ["early-logout"],
    allSwipes: ["8:45 AM", "1:00 PM", "2:00 PM", "5:30 PM"]
  },
  {
    id: "EMP004",
    name: "Emily Davis",
    department: "HR",
    firstIn: "9:15 AM",
    lastOut: "1:00 PM",
    session1In: "9:15 AM",
    session1Out: "1:00 PM",
    session2In: "-",
    session2Out: "-",
    totalHours: "3h 45m",
    flags: ["half-day"],
    allSwipes: ["9:15 AM", "1:00 PM"]
  },
  {
    id: "EMP005",
    name: "David Brown",
    department: "Finance",
    firstIn: "-",
    lastOut: "-",
    session1In: "-",
    session1Out: "-",
    session2In: "-",
    session2Out: "-",
    totalHours: "0h 00m",
    flags: ["absent"],
    allSwipes: []
  },
  {
    id: "EMP006",
    name: "Lisa Anderson",
    department: "Engineering",
    firstIn: "9:10 AM",
    lastOut: "-",
    session1In: "9:10 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "-",
    totalHours: "6h 50m",
    flags: ["missed-swipe"],
    allSwipes: ["9:10 AM", "1:00 PM", "2:00 PM"]
  }
]

interface DailyAttendancePageProps {
  params: { date: string }
}

export default function DailyAttendancePage({ params }: DailyAttendancePageProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date(params.date))
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedSwipes, setSelectedSwipes] = useState<string[]>([])
  const [swipeModalOpen, setSwipeModalOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState("")

  // Filter and search logic
  const filteredData = useMemo(() => {
    return dailyAttendanceData.filter(employee => {
      const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           employee.id.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesDepartment = selectedDepartment === "all" || 
                               employee.department.toLowerCase() === selectedDepartment.toLowerCase()
      return matchesSearch && matchesDepartment
    })
  }, [searchTerm, selectedDepartment])

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const present = filteredData.filter(emp => !emp.flags.includes("absent")).length
    const absent = filteredData.filter(emp => emp.flags.includes("absent")).length
    const late = filteredData.filter(emp => emp.flags.includes("late")).length
    const earlyOut = filteredData.filter(emp => emp.flags.includes("early-logout")).length
    
    return { present, absent, late, earlyOut }
  }, [filteredData])

  const getFlagBadge = (flags: string[]) => {
    if (flags.includes("absent")) {
      return <Badge variant="destructive" className="text-xs">Absent</Badge>
    }
    if (flags.includes("late")) {
      return <Badge variant="destructive" className="text-xs">Late</Badge>
    }
    if (flags.includes("early-logout")) {
      return <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-800">Early Out</Badge>
    }
    if (flags.includes("half-day")) {
      return <Badge variant="outline" className="text-xs">Half Day</Badge>
    }
    if (flags.includes("missed-swipe")) {
      return <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">Missed Swipe</Badge>
    }
    return <Badge variant="default" className="text-xs bg-green-100 text-green-800">On Time</Badge>
  }

  const getRowClassName = (flags: string[]) => {
    if (flags.includes("absent")) return "bg-red-50"
    if (flags.includes("late")) return "bg-red-50"
    if (flags.includes("early-logout")) return "bg-amber-50"
    if (flags.includes("missed-swipe")) return "bg-orange-50"
    return ""
  }

  const viewSwipes = (swipes: string[], employeeName: string) => {
    setSelectedSwipes(swipes)
    setSelectedEmployee(employeeName)
    setSwipeModalOpen(true)
  }

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Daily Attendance - {format(selectedDate, "MMMM dd, yyyy")}
            </h1>
            <p className="text-muted-foreground mt-1">Review attendance for all employees</p>
          </div>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[200px]">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(selectedDate, "MMM dd, yyyy")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-emerald-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Present</p>
                  <p className="text-3xl font-bold text-green-700">{summaryStats.present}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-red-50 to-rose-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600">Absent</p>
                  <p className="text-3xl font-bold text-red-700">{summaryStats.absent}</p>
                </div>
                <UserX className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-amber-50 to-yellow-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-amber-600">Late Arrivals</p>
                  <p className="text-3xl font-bold text-amber-700">{summaryStats.late}</p>
                </div>
                <Clock className="w-8 h-8 text-amber-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-orange-50 to-red-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">Early Departures</p>
                  <p className="text-3xl font-bold text-orange-700">{summaryStats.earlyOut}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-col sm:flex-row gap-4"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search by name or employee ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-[200px]">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="engineering">Engineering</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="sales">Sales</SelectItem>
              <SelectItem value="hr">HR</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Attendance Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader className="sticky top-0 bg-white z-10 border-b">
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Employee Attendance Records ({filteredData.length} employees)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-gray-50 z-10">
                    <TableRow>
                      <TableHead className="w-[120px]">Employee</TableHead>
                      <TableHead className="w-[100px]">Department</TableHead>
                      <TableHead className="w-[120px]">First In / Last Out</TableHead>
                      <TableHead className="w-[140px]">Session 1</TableHead>
                      <TableHead className="w-[140px]">Session 2</TableHead>
                      <TableHead className="w-[100px]">Total Hours</TableHead>
                      <TableHead className="w-[100px]">Status</TableHead>
                      <TableHead className="w-[120px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.map((employee) => (
                      <TableRow key={employee.id} className={getRowClassName(employee.flags)}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{employee.name}</div>
                            <div className="text-xs text-muted-foreground">{employee.id}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {employee.department}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div className={employee.flags.includes("late") ? "text-red-600 font-medium" : ""}>
                              In: {employee.firstIn}
                            </div>
                            <div className={employee.flags.includes("early-logout") ? "text-amber-600 font-medium" : ""}>
                              Out: {employee.lastOut}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div>In: {employee.session1In}</div>
                            <div>Out: {employee.session1Out}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div>In: {employee.session2In}</div>
                            <div>Out: {employee.session2Out}</div>
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {employee.totalHours}
                        </TableCell>
                        <TableCell>
                          {getFlagBadge(employee.flags)}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => viewSwipes(employee.allSwipes, employee.name)}
                                    disabled={employee.allSwipes.length === 0}
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>View all swipes</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Edit / Normalize</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Swipe Log Modal */}
        <Dialog open={swipeModalOpen} onOpenChange={setSwipeModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Swipe Records - {selectedEmployee}</DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              {selectedSwipes.length > 0 ? (
                selectedSwipes.map((swipe, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">Swipe {index + 1}</span>
                    <span className="text-muted-foreground">{swipe}</span>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No swipe records found for this employee
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  )
}