"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  CalendarIcon,
  Clock,
  User,
  Building,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Calendar as CalendarDays
} from "lucide-react"
import { format, addDays, subDays } from "date-fns"
import { cn } from "@/lib/utils"
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip
} from "recharts"

// Mock employee data
const employeeData = {
  id: "EMP001",
  name: "John Smith",
  avatar: "/placeholder-user.jpg",
  department: "Engineering",
  role: "Senior Developer",
  avgLoginTime: "9:15 AM",
  avgLogoutTime: "6:30 PM",
  avgWorkDuration: "8h 45m",
  lateLogins: 8,
  earlyLogouts: 3,
  missedPunches: 2,
  extraHours: "24h 30m"
}

// Mock attendance data for the month
const attendanceData = [
  {
    date: "2024-01-01",
    session1In: "9:00 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "6:00 PM",
    totalHours: "8h 00m",
    flags: ["on-time"],
    allSwipes: ["9:00 AM", "1:00 PM", "2:00 PM", "6:00 PM"]
  },
  {
    date: "2024-01-02",
    session1In: "9:30 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "6:30 PM",
    totalHours: "8h 00m",
    flags: ["late"],
    allSwipes: ["9:30 AM", "1:00 PM", "2:00 PM", "6:30 PM"]
  },
  {
    date: "2024-01-03",
    session1In: "9:00 AM",
    session1Out: "1:00 PM",
    session2In: "2:00 PM",
    session2Out: "5:30 PM",
    totalHours: "7h 30m",
    flags: ["early-logout"],
    allSwipes: ["9:00 AM", "1:00 PM", "2:00 PM", "5:30 PM"]
  },
  {
    date: "2024-01-04",
    session1In: "9:15 AM",
    session1Out: "1:00 PM",
    session2In: "-",
    session2Out: "-",
    totalHours: "3h 45m",
    flags: ["half-day"],
    allSwipes: ["9:15 AM", "1:00 PM"]
  }
]

// Mock login time trends
const loginTrends = [
  { date: "Jan 1", time: 9.0 },
  { date: "Jan 2", time: 9.5 },
  { date: "Jan 3", time: 9.0 },
  { date: "Jan 4", time: 9.25 },
  { date: "Jan 5", time: 8.75 },
  { date: "Jan 6", time: 9.1 },
  { date: "Jan 7", time: 9.3 }
]

interface EmployeeAttendancePageProps {
  params: { id: string }
}

export default function EmployeeAttendancePage({ params }: EmployeeAttendancePageProps) {
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date())
  const [selectedSwipes, setSelectedSwipes] = useState<string[]>([])
  const [swipeModalOpen, setSwipeModalOpen] = useState(false)

  const getFlagBadge = (flags: string[]) => {
    if (flags.includes("late")) {
      return <Badge variant="destructive" className="text-xs">Late</Badge>
    }
    if (flags.includes("early-logout")) {
      return <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-800">Early Out</Badge>
    }
    if (flags.includes("half-day")) {
      return <Badge variant="outline" className="text-xs">Half Day</Badge>
    }
    return <Badge variant="default" className="text-xs bg-green-100 text-green-800">On Time</Badge>
  }

  const viewSwipes = (swipes: string[]) => {
    setSelectedSwipes(swipes)
    setSwipeModalOpen(true)
  }

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Employee Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 shadow-lg"
        >
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            <Avatar className="w-20 h-20">
              <AvatarImage src={employeeData.avatar} alt={employeeData.name} />
              <AvatarFallback className="text-2xl font-bold bg-blue-500 text-white">
                {employeeData.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900">{employeeData.name}</h1>
              <div className="flex flex-wrap gap-4 mt-2 text-muted-foreground">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span>{employeeData.id}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4" />
                  <span>{employeeData.department}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-4 h-4 bg-blue-500 rounded-full"></span>
                  <span>{employeeData.role}</span>
                </div>
              </div>
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[200px]">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(selectedMonth, "MMMM yyyy")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedMonth}
                  onSelect={(date) => date && setSelectedMonth(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </motion.div>

        {/* Summary Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-6"
        >
          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <Clock className="w-8 h-8 mx-auto mb-2 text-blue-500" />
              <p className="text-sm text-muted-foreground">Avg Login</p>
              <p className="text-xl font-bold">{employeeData.avgLoginTime}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <Clock className="w-8 h-8 mx-auto mb-2 text-green-500" />
              <p className="text-sm text-muted-foreground">Avg Logout</p>
              <p className="text-xl font-bold">{employeeData.avgLogoutTime}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <TrendingUp className="w-8 h-8 mx-auto mb-2 text-purple-500" />
              <p className="text-sm text-muted-foreground">Avg Duration</p>
              <p className="text-xl font-bold">{employeeData.avgWorkDuration}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-red-500" />
              <p className="text-sm text-muted-foreground">Late Logins</p>
              <p className="text-xl font-bold">{employeeData.lateLogins}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <XCircle className="w-8 h-8 mx-auto mb-2 text-amber-500" />
              <p className="text-sm text-muted-foreground">Early Logouts</p>
              <p className="text-xl font-bold">{employeeData.earlyLogouts}</p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6 text-center">
              <CheckCircle className="w-8 h-8 mx-auto mb-2 text-indigo-500" />
              <p className="text-sm text-muted-foreground">Extra Hours</p>
              <p className="text-xl font-bold">{employeeData.extraHours}</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Charts and Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6"
        >
          {/* Login Time Trends */}
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Login Time Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={250}>
                <LineChart data={loginTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis domain={[8, 10]} />
                  <RechartsTooltip />
                  <Line
                    type="monotone"
                    dataKey="time"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Attendance Table */}
          <Card className="lg:col-span-2 shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Monthly Attendance Record
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Session 1</TableHead>
                      <TableHead>Session 2</TableHead>
                      <TableHead>Total Hours</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {attendanceData.map((record, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {format(new Date(record.date), "MMM dd")}
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div>In: {record.session1In}</div>
                            <div>Out: {record.session1Out}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div>In: {record.session2In}</div>
                            <div>Out: {record.session2Out}</div>
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {record.totalHours}
                        </TableCell>
                        <TableCell>
                          {getFlagBadge(record.flags)}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => viewSwipes(record.allSwipes)}
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>View all swipes</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Normalize day</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Swipe Log Modal */}
        <Dialog open={swipeModalOpen} onOpenChange={setSwipeModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>All Swipe Records</DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              {selectedSwipes.map((swipe, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">Swipe {index + 1}</span>
                  <span className="text-muted-foreground">{swipe}</span>
                </div>
              ))}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  )
}