"use client";

import { useState, use<PERSON><PERSON><PERSON> } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import {
  CalendarIcon,
  Search,
  Filter,
  Eye,
  Edit,
  AlertTriangle,
  Clock,
  UserX,
  CheckCircle,
  XCircle,
  Calendar as CalendarDays,
  TrendingUp,
  ArrowLeft,
  Settings,
  FileText,
} from "lucide-react";
import { format, addDays, subDays } from "date-fns";
import { cn } from "@/lib/utils";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import type { DateRange } from "react-day-picker";

// Types
interface ExceptionData {
  id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  date: string;
  type: string;
  severity: string;
  description: string;
  status: string;
  notes?: string;
}

export default function AttendanceExceptionsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedSeverity, setSelectedSeverity] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedException, setSelectedException] =
    useState<ExceptionData | null>(null);
  const [notes, setNotes] = useState("");

  // Mock data
  const exceptionsData: ExceptionData[] = [
    {
      id: "1",
      employeeId: "EMP001",
      employeeName: "John Doe",
      department: "Engineering",
      date: "2024-01-15",
      type: "late-arrival",
      severity: "medium",
      description: "Arrived 45 minutes late without prior notification",
      status: "pending",
      notes: "Employee cited traffic issues",
    },
    // Add more mock data as needed
  ];

  const filteredExceptions = useMemo(() => {
    return exceptionsData.filter((exception) => {
      const matchesSearch =
        exception.employeeName
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        exception.employeeId.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDepartment =
        selectedDepartment === "all" ||
        exception.department === selectedDepartment;
      const matchesType =
        selectedType === "all" || exception.type === selectedType;
      const matchesSeverity =
        selectedSeverity === "all" || exception.severity === selectedSeverity;
      const matchesStatus =
        selectedStatus === "all" || exception.status === selectedStatus;

      let matchesDateRange = true;
      if (dateRange?.from && dateRange?.to) {
        const exceptionDate = new Date(exception.date);
        matchesDateRange =
          exceptionDate >= dateRange.from && exceptionDate <= dateRange.to;
      }

      return (
        matchesSearch &&
        matchesDepartment &&
        matchesType &&
        matchesSeverity &&
        matchesStatus &&
        matchesDateRange
      );
    });
  }, [
    exceptionsData,
    searchTerm,
    selectedDepartment,
    selectedType,
    selectedSeverity,
    selectedStatus,
    dateRange,
  ]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "late-arrival":
        return <Clock className="w-4 h-4 text-orange-500" />;
      case "early-departure":
        return <UserX className="w-4 h-4 text-red-500" />;
      case "absent":
        return <XCircle className="w-4 h-4 text-red-600" />;
      case "overtime":
        return <TrendingUp className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const config: Record<string, { color: string; label: string }> = {
      "late-arrival": {
        color: "bg-orange-100 text-orange-800",
        label: "Late Arrival",
      },
      "early-departure": {
        color: "bg-red-100 text-red-800",
        label: "Early Departure",
      },
      absent: { color: "bg-red-100 text-red-800", label: "Absent" },
      overtime: { color: "bg-blue-100 text-blue-800", label: "Overtime" },
    };

    const { color, label } = config[type] || {
      color: "bg-gray-100 text-gray-800",
      label: "Unknown",
    };
    return <Badge className={`text-xs ${color}`}>{label}</Badge>;
  };

  const getSeverityBadge = (severity: string) => {
    const config: Record<string, { color: string; label: string }> = {
      low: { color: "bg-green-100 text-green-800", label: "Low" },
      medium: { color: "bg-yellow-100 text-yellow-800", label: "Medium" },
      high: { color: "bg-orange-100 text-orange-800", label: "High" },
      critical: { color: "bg-red-100 text-red-800", label: "Critical" },
    };

    const { color, label } = config[severity] || {
      color: "bg-gray-100 text-gray-800",
      label: "Unknown",
    };
    return <Badge className={`text-xs ${color}`}>{label}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const config: Record<string, { color: string; label: string }> = {
      pending: { color: "bg-yellow-100 text-yellow-800", label: "Pending" },
      reviewed: { color: "bg-blue-100 text-blue-800", label: "Reviewed" },
      resolved: { color: "bg-green-100 text-green-800", label: "Resolved" },
      ignored: { color: "bg-gray-100 text-gray-800", label: "Ignored" },
    };

    const { color, label } = config[status] || {
      color: "bg-gray-100 text-gray-800",
      label: "Unknown",
    };
    return <Badge className={`text-xs ${color}`}>{label}</Badge>;
  };

  const viewDetails = (exception: ExceptionData) => {
    setSelectedException(exception);
    setNotes(exception.notes || "");
    setDetailsModalOpen(true);
  };

  const handleStatusUpdate = (newStatus: string) => {
    // Update status logic here
    console.log("Updating status:", selectedException?.id, newStatus);
    setDetailsModalOpen(false);
  };

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header with Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div className="flex items-center gap-4">
            <Link href="/admin/attendance">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Attendance
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Attendance Exceptions
              </h1>
              <p className="text-muted-foreground mt-1">
                Track and manage attendance irregularities
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            <Link href="/admin/attendance/normalize">
              <Button variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Normalize Records
              </Button>
            </Link>
            <Link href="/admin/attendance/overtime">
              <Button variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                View Overtime
              </Button>
            </Link>
            <Link href="/admin/attendance/settings">
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4"
        >
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search employees..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select
            value={selectedDepartment}
            onValueChange={setSelectedDepartment}
          >
            <SelectTrigger>
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="Engineering">Engineering</SelectItem>
              <SelectItem value="Marketing">Marketing</SelectItem>
              <SelectItem value="Sales">Sales</SelectItem>
              <SelectItem value="HR">HR</SelectItem>
              <SelectItem value="Finance">Finance</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger>
              <SelectValue placeholder="Exception Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="late-arrival">Late Arrival</SelectItem>
              <SelectItem value="early-departure">Early Departure</SelectItem>
              <SelectItem value="absent">Absent</SelectItem>
              <SelectItem value="overtime">Overtime</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
            <SelectTrigger>
              <SelectValue placeholder="Severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Severities</SelectItem>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="reviewed">Reviewed</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="ignored">Ignored</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </motion.div>

        {/* Exceptions Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Exception Records ({filteredExceptions.length} items)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredExceptions.map((exception) => (
                      <TableRow key={exception.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {exception.employeeName}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {exception.employeeId} • {exception.department}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {format(new Date(exception.date), "MMM dd, yyyy")}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTypeIcon(exception.type)}
                            {getTypeBadge(exception.type)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getSeverityBadge(exception.severity)}
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <p className="text-sm truncate">
                            {exception.description}
                          </p>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(exception.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => viewDetails(exception)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Link
                              href={`/admin/attendance/normalize?employee=${exception.employeeId}&date=${exception.date}`}
                            >
                              <Button variant="ghost" size="sm">
                                <Edit className="w-4 h-4" />
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Exception Details Modal */}
        <Dialog open={detailsModalOpen} onOpenChange={setDetailsModalOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Exception Details</DialogTitle>
            </DialogHeader>
            {selectedException && (
              <div className="space-y-6">
                {/* Employee Info */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Employee</Label>
                      <p className="text-lg font-semibold">
                        {selectedException.employeeName}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {selectedException.employeeId} •{" "}
                        {selectedException.department}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Date</Label>
                      <p className="text-lg">
                        {format(new Date(selectedException.date), "PPP")}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Exception Details */}
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    {getTypeBadge(selectedException.type)}
                    {getSeverityBadge(selectedException.severity)}
                    {getStatusBadge(selectedException.status)}
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="mt-1">{selectedException.description}</p>
                  </div>
                </div>

                {/* Notes */}
                <div className="space-y-2">
                  <Label htmlFor="notes">Admin Notes</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add notes about this exception..."
                    rows={3}
                  />
                </div>

                {/* Actions */}
                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate("ignored")}
                  >
                    Mark as Ignored
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate("reviewed")}
                  >
                    Mark as Reviewed
                  </Button>
                  <Link
                    href={`/admin/attendance/normalize?employee=${selectedException.employeeId}&date=${selectedException.date}`}
                  >
                    <Button onClick={() => setDetailsModalOpen(false)}>
                      Normalize Record
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
}
