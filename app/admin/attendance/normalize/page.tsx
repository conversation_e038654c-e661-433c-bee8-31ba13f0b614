"use client"

import { useState, useEffect, Suspense } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
  CalendarIcon,
  User,
  Clock,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Info,
  ArrowLeft,
  FileText,
  Settings
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Mock employee data
const employees = [
  { id: "EMP001", name: "John Smith", department: "Engineering" },
  { id: "EMP002", name: "Sarah Johnson", department: "Marketing" },
  { id: "EMP003", name: "Mike Wilson", department: "Sales" },
  { id: "EMP004", name: "Emily Davis", department: "HR" },
  { id: "EMP005", name: "David Brown", department: "Finance" }
]

// Mock existing attendance data
const existingAttendance: Record<string, Record<string, any>> = {
  "EMP001": {
    "2024-01-15": {
      session1In: "9:30",
      session1Out: "13:00",
      session2In: "14:00",
      session2Out: "17:30",
      status: "late",
      totalHours: "7h 00m",
      flags: ["late"]
    }
  }
}

function NormalizePageContent() {
  const searchParams = useSearchParams()
  const [selectedEmployee, setSelectedEmployee] = useState("")
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [formData, setFormData] = useState({
    session1In: "",
    session1Out: "",
    session2In: "",
    session2Out: "",
    extraHours: "",
    status: "full-day",
    reason: "",
    overrideWeekend: false
  })
  const [existingData, setExistingData] = useState<any>(null)
  const [isModified, setIsModified] = useState(false)

  // Pre-fill from URL params
  useEffect(() => {
    const employeeParam = searchParams.get('employee')
    const dateParam = searchParams.get('date')
    
    if (employeeParam) {
      setSelectedEmployee(employeeParam)
    }
    
    if (dateParam) {
      setSelectedDate(new Date(dateParam))
    }
  }, [searchParams])

  const handleEmployeeChange = (employeeId: string) => {
    setSelectedEmployee(employeeId)
    loadExistingData(employeeId, selectedDate)
  }

  const handleDateChange = (date: Date | undefined) => {
    setSelectedDate(date)
    if (selectedEmployee && date) {
      loadExistingData(selectedEmployee, date)
    }
  }

  const loadExistingData = (employeeId: string, date: Date | undefined) => {
    if (!date) return
    
    const dateKey = format(date, "yyyy-MM-dd")
    const existing = existingAttendance[employeeId]?.[dateKey]
    
    if (existing) {
      setExistingData(existing)
      setFormData({
        session1In: existing.session1In || "",
        session1Out: existing.session1Out || "",
        session2In: existing.session2In || "",
        session2Out: existing.session2Out || "",
        extraHours: "",
        status: existing.status === "late" ? "full-day" : existing.status,
        reason: "",
        overrideWeekend: false
      })
    } else {
      setExistingData(null)
      setFormData({
        session1In: "",
        session1Out: "",
        session2In: "",
        session2Out: "",
        extraHours: "",
        status: "full-day",
        reason: "",
        overrideWeekend: false
      })
    }
    setIsModified(false)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setIsModified(true)
  }

  const calculateTotalHours = () => {
    const { session1In, session1Out, session2In, session2Out, extraHours } = formData
    
    if (!session1In || !session1Out) return "0h 00m"
    
    // Simple calculation for demo
    let total = 0
    if (session1In && session1Out) total += 4 // Session 1
    if (session2In && session2Out) total += 4 // Session 2
    if (extraHours) total += parseFloat(extraHours) || 0
    
    const hours = Math.floor(total)
    const minutes = Math.round((total - hours) * 60)
    return `${hours}h ${minutes.toString().padStart(2, '0')}m`
  }

  const handleSave = () => {
    // Save logic here
    console.log("Saving normalized attendance:", {
      employee: selectedEmployee,
      date: selectedDate,
      data: formData
    })
    setIsModified(false)
  }

  const handleReset = () => {
    loadExistingData(selectedEmployee, selectedDate)
  }

  const selectedEmployeeData = employees.find(emp => emp.id === selectedEmployee)
  const isWeekend = selectedDate && (selectedDate.getDay() === 0 || selectedDate.getDay() === 6)

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header with Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div className="flex items-center gap-4">
            <Link href="/admin/attendance">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Attendance
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Manual Attendance Normalization</h1>
              <p className="text-muted-foreground mt-1">Override or correct attendance records for specific employees</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Link href="/admin/attendance/exceptions">
              <Button variant="outline">
                <AlertTriangle className="w-4 h-4 mr-2" />
                View Exceptions
              </Button>
            </Link>
            <Link href="/admin/attendance/overtime">
              <Button variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                Overtime Tracker
              </Button>
            </Link>
            <Button variant="outline" onClick={handleReset} disabled={!isModified}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button onClick={handleSave} disabled={!selectedEmployee || !selectedDate || !isModified}>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </motion.div>

        {/* Selection Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Select Employee
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select value={selectedEmployee} onValueChange={handleEmployeeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an employee" />
                </SelectTrigger>
                <SelectContent>
                  {employees.map(emp => (
                    <SelectItem key={emp.id} value={emp.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{emp.name}</span>
                        <span className="text-xs text-muted-foreground">{emp.id} • {emp.department}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {selectedEmployeeData && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                      {selectedEmployeeData.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <p className="font-medium">{selectedEmployeeData.name}</p>
                      <p className="text-sm text-muted-foreground">{selectedEmployeeData.department}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="w-5 h-5" />
                Select Date
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className={cn("w-full justify-start text-left font-normal")}>
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              {isWeekend && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    This is a weekend day. Enable override below if attendance is required.
                  </AlertDescription>
                </Alert>
              )}

              {existingData && (
                <div className="p-4 bg-amber-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="w-4 h-4 text-amber-600" />
                    <span className="font-medium text-amber-800">Existing Record Found</span>
                  </div>
                  <div className="text-sm text-amber-700">
                    <p>Total Hours: {existingData.totalHours}</p>
                    <div className="flex gap-2 mt-1">
                      {existingData.flags?.map((flag: string) => (
                        <Badge key={flag} variant="secondary" className="text-xs">
                          {flag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Normalization Form */}
        {selectedEmployee && selectedDate && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 xl:grid-cols-3 gap-6"
          >
            {/* Time Inputs */}
            <Card className="xl:col-span-2 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Time Entries
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Session 1 */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <Label className="text-base font-medium">Session 1 (Morning)</Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4 pl-5">
                    <div className="space-y-2">
                      <Label htmlFor="session1In">Check In</Label>
                      <Input
                        id="session1In"
                        type="time"
                        value={formData.session1In}
                        onChange={(e) => handleInputChange("session1In", e.target.value)}
                        className="font-mono"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="session1Out">Check Out</Label>
                      <Input
                        id="session1Out"
                        type="time"
                        value={formData.session1Out}
                        onChange={(e) => handleInputChange("session1Out", e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Session 2 */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <Label className="text-base font-medium">Session 2 (Afternoon)</Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4 pl-5">
                    <div className="space-y-2">
                      <Label htmlFor="session2In">Check In</Label>
                      <Input
                        id="session2In"
                        type="time"
                        value={formData.session2In}
                        onChange={(e) => handleInputChange("session2In", e.target.value)}
                        className="font-mono"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="session2Out">Check Out</Label>
                      <Input
                        id="session2Out"
                        type="time"
                        value={formData.session2Out}
                        onChange={(e) => handleInputChange("session2Out", e.target.value)}
                        className="font-mono"
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Extra Hours */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <Label className="text-base font-medium">Additional Hours</Label>
                  </div>
                  <div className="pl-5">
                    <div className="space-y-2">
                      <Label htmlFor="extraHours">Extra Hours (decimal format, e.g., 1.5)</Label>
                      <Input
                        id="extraHours"
                        type="number"
                        step="0.25"
                        min="0"
                        max="12"
                        value={formData.extraHours}
                        onChange={(e) => handleInputChange("extraHours", e.target.value)}
                        placeholder="0.0"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status & Settings */}
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle>Status & Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Calculated Total */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                  <Label className="text-sm font-medium text-blue-700">Calculated Total Hours</Label>
                  <p className="text-2xl font-bold text-blue-900">{calculateTotalHours()}</p>
                </div>

                {/* Status Selection */}
                <div className="space-y-2">
                  <Label>Attendance Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-day">Full Day</SelectItem>
                      <SelectItem value="half-day">Half Day</SelectItem>
                      <SelectItem value="absent">Absent</SelectItem>
                      <SelectItem value="paid-leave">Paid Leave</SelectItem>
                      <SelectItem value="unpaid-leave">Unpaid Leave</SelectItem>
                      <SelectItem value="work-from-home">Work From Home</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Weekend Override */}
                {isWeekend && (
                  <div className="flex items-center justify-between p-3 bg-amber-50 rounded-lg">
                    <div>
                      <Label className="text-sm font-medium">Override Weekend</Label>
                      <p className="text-xs text-muted-foreground">Allow attendance on weekend</p>
                    </div>
                    <Switch
                      checked={formData.overrideWeekend}
                      onCheckedChange={(checked) => handleInputChange("overrideWeekend", checked)}
                    />
                  </div>
                )}

                {/* Reason */}
                <div className="space-y-2">
                  <Label htmlFor="reason">Reason for Normalization</Label>
                  <Textarea
                    id="reason"
                    placeholder="Explain why this attendance record is being normalized..."
                    value={formData.reason}
                    onChange={(e) => handleInputChange("reason", e.target.value)}
                    rows={4}
                  />
                </div>

                {/* Save Status */}
                {isModified && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      You have unsaved changes. Click "Save Changes" to apply them.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Quick Actions */}
        {selectedEmployee && selectedDate && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        session1In: "09:00",
                        session1Out: "13:00",
                        session2In: "14:00",
                        session2Out: "18:00",
                        status: "full-day"
                      }))
                      setIsModified(true)
                    }}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Standard Day
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        session1In: "09:00",
                        session1Out: "13:00",
                        session2In: "",
                        session2Out: "",
                        status: "half-day"
                      }))
                      setIsModified(true)
                    }}
                  >
                    Half Day (AM)
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        session1In: "",
                        session1Out: "",
                        session2In: "14:00",
                        session2Out: "18:00",
                        status: "half-day"
                      }))
                      setIsModified(true)
                    }}
                  >
                    Half Day (PM)
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        session1In: "",
                        session1Out: "",
                        session2In: "",
                        session2Out: "",
                        status: "absent"
                      }))
                      setIsModified(true)
                    }}
                  >
                    Mark Absent
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </AdminLayout>
  )
}

export default function NormalizePage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <NormalizePageContent />
    </Suspense>
  )
}
