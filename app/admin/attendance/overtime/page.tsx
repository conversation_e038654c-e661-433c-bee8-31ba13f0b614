"use client"

import { useState, useMemo } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  CalendarIcon,
  Search,
  Eye,
  AlertTriangle,
  Clock,
  TrendingUp,
  ArrowLeft,
  Settings,
  Check,
  X,
  DollarSign,
  Users
} from "lucide-react"
import { format } from "date-fns"
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Line
} from "recharts"
import type { DateRange } from "react-day-picker"

// Types
interface OvertimeData {
  id: string
  employeeId: string
  employeeName: string
  department: string
  date: string
  extraHours: number
  reason: string
  compensationType: string
  status: string
  approvedBy?: string
  approvedDate?: string
  linkedAbsence?: string
  notes?: string
}

export default function OvertimePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedCompensation, setSelectedCompensation] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<OvertimeData | null>(null)

  // Mock data
  const overtimeData: OvertimeData[] = [
    {
      id: "1",
      employeeId: "EMP001",
      employeeName: "John Doe",
      department: "Engineering",
      date: "2024-01-15",
      extraHours: 3.5,
      reason: "Critical bug fix for production deployment",
      compensationType: "overtime-pay",
      status: "pending",
      notes: "Urgent production issue"
    },
    // Add more mock data as needed
  ]

  const filteredData = useMemo(() => {
    return overtimeData.filter(record => {
      const matchesSearch = record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesDepartment = selectedDepartment === "all" || record.department === selectedDepartment
      const matchesStatus = selectedStatus === "all" || record.status === selectedStatus
      const matchesCompensation = selectedCompensation === "all" || record.compensationType === selectedCompensation
      
      let matchesDateRange = true
      if (dateRange?.from && dateRange?.to) {
        const recordDate = new Date(record.date)
        matchesDateRange = recordDate >= dateRange.from && recordDate <= dateRange.to
      }

      return matchesSearch && matchesDepartment && matchesStatus && matchesCompensation && matchesDateRange
    })
  }, [overtimeData, searchTerm, selectedDepartment, selectedStatus, selectedCompensation, dateRange])

  // Summary stats
  const summaryStats = useMemo(() => {
    return {
      totalHours: filteredData.reduce((sum, record) => sum + record.extraHours, 0),
      pending: filteredData.filter(r => r.status === "pending").length,
      approved: filteredData.filter(r => r.status === "approved").length,
      totalEmployees: new Set(filteredData.map(r => r.employeeId)).size,
      estimatedCost: filteredData.reduce((sum, record) => sum + (record.extraHours * 50), 0) // $50/hour estimate
    }
  }, [filteredData])

  // Mock chart data
  const monthlyOvertimeStats = [
    { month: "Jan", hours: 120, employees: 15 },
    { month: "Feb", hours: 98, employees: 12 },
    { month: "Mar", hours: 156, employees: 18 },
    { month: "Apr", hours: 134, employees: 16 },
    { month: "May", hours: 178, employees: 22 },
    { month: "Jun", hours: 145, employees: 19 }
  ]

  const departmentOvertimeStats = [
    { department: "Engineering", hours: 245, cost: 12250 },
    { department: "Marketing", hours: 89, cost: 4450 },
    { department: "Sales", hours: 156, cost: 7800 },
    { department: "HR", hours: 34, cost: 1700 },
    { department: "Finance", hours: 67, cost: 3350 }
  ]

  const getStatusBadge = (status: string) => {
    const config: Record<string, { color: string; label: string }> = {
      "pending": { color: "bg-yellow-100 text-yellow-800", label: "Pending" },
      "approved": { color: "bg-green-100 text-green-800", label: "Approved" },
      "rejected": { color: "bg-red-100 text-red-800", label: "Rejected" },
      "used": { color: "bg-blue-100 text-blue-800", label: "Used" }
    }
    
    const { color, label } = config[status] || { color: "bg-gray-100 text-gray-800", label: "Unknown" }
    return <Badge className={`text-xs ${color}`}>{label}</Badge>
  }

  const getCompensationBadge = (type: string) => {
    const config: Record<string, { color: string; label: string }> = {
      "time-off": { color: "bg-blue-100 text-blue-800", label: "Time Off" },
      "overtime-pay": { color: "bg-green-100 text-green-800", label: "Overtime Pay" }
    }
    
    const { color, label } = config[type] || { color: "bg-gray-100 text-gray-800", label: "N/A" }
    return <Badge className={`text-xs ${color}`}>{label}</Badge>
  }

  const viewDetails = (record: OvertimeData) => {
    setSelectedRecord(record)
    setDetailsModalOpen(true)
  }

  const handleApproval = (recordId: string, approved: boolean) => {
    // Handle approval logic
    console.log(`${approved ? 'Approving' : 'Rejecting'} overtime record:`, recordId)
  }

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header with Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div className="flex items-center gap-4">
            <Link href="/admin/attendance">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Attendance
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Overtime & Extra Hours</h1>
              <p className="text-muted-foreground mt-1">Track and manage employee overtime compensation</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Link href="/admin/attendance/exceptions">
              <Button variant="outline">
                <AlertTriangle className="w-4 h-4 mr-2" />
                View Exceptions
              </Button>
            </Link>
            <Link href="/admin/attendance/normalize">
              <Button variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                Normalize Records
              </Button>
            </Link>
            <Link href="/admin/attendance/settings">
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-5 gap-6"
        >
          <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Hours</p>
                  <p className="text-3xl font-bold text-blue-700">{summaryStats.totalHours.toFixed(1)}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-amber-50 to-yellow-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-amber-600">Pending</p>
                  <p className="text-3xl font-bold text-amber-700">{summaryStats.pending}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-amber-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-emerald-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Approved</p>
                  <p className="text-3xl font-bold text-green-700">{summaryStats.approved}</p>
                </div>
                <Check className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-purple-50 to-indigo-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Employees</p>
                  <p className="text-3xl font-bold text-purple-700">{summaryStats.totalEmployees}</p>
                </div>
                <Users className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-teal-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-emerald-600">Est. Cost</p>
                  <p className="text-2xl font-bold text-emerald-700">${summaryStats.estimatedCost.toLocaleString()}</p>
                </div>
                <DollarSign className="w-8 h-8 text-emerald-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Analytics Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tabs defaultValue="trends" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
              <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
              <TabsTrigger value="departments">Department Analysis</TabsTrigger>
            </TabsList>

            <TabsContent value="trends" className="space-y-6">
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-xl font-semibold text-muted-foreground">
                    Monthly Overtime Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={350}>
                    <AreaChart data={monthlyOvertimeStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="hours" orientation="left" />
                      <YAxis yAxisId="employees" orientation="right" />
                      <RechartsTooltip />
                      <Area
                        yAxisId="hours"
                        type="monotone"
                        dataKey="hours"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.3}
                        name="Total Hours"
                      />
                      <Line
                        yAxisId="employees"
                        type="monotone"
                        dataKey="employees"
                        stroke="#10b981"
                        strokeWidth={3}
                        name="Employees"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="departments" className="space-y-6">
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-xl font-semibold text-muted-foreground">
                    Department-wise Overtime Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={350}>
                    <BarChart data={departmentOvertimeStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="department" />
                      <YAxis yAxisId="hours" orientation="left" />
                      <YAxis yAxisId="cost" orientation="right" />
                      <RechartsTooltip />
                      <Bar yAxisId="hours" dataKey="hours" fill="#3b82f6" name="Hours" />
                      <Bar yAxisId="cost" dataKey="cost" fill="#10b981" name="Cost ($)" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4"
        >
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search employees..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger>
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="engineering">Engineering</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="sales">Sales</SelectItem>
              <SelectItem value="hr">HR</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="used">Used</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedCompensation} onValueChange={setSelectedCompensation}>
            <SelectTrigger>
              <SelectValue placeholder="Compensation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="time-off">Time Off</SelectItem>
              <SelectItem value="overtime-pay">Overtime Pay</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                Date Range
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={setDateRange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </motion.div>

        {/* Overtime Records Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Overtime Records ({filteredData.length} items)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Extra Hours</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>Compensation</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{record.employeeName}</div>
                            <div className="text-xs text-muted-foreground">
                              {record.employeeId} • {record.department}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {format(new Date(record.date), "MMM dd, yyyy")}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-blue-500" />
                            <span className="font-medium">{record.extraHours}h</span>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <p className="text-sm truncate">{record.reason}</p>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">{record.reason}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          {getCompensationBadge(record.compensationType)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(record.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => viewDetails(record)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            {record.status === "pending" && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleApproval(record.id, true)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Check className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleApproval(record.id, false)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Overtime Details Modal */}
        <Dialog open={detailsModalOpen} onOpenChange={setDetailsModalOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Overtime Record Details</DialogTitle>
            </DialogHeader>
            {selectedRecord && (
              <div className="space-y-6">
                {/* Employee Info */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Employee</Label>
                      <p className="text-lg font-semibold">{selectedRecord.employeeName}</p>
                      <p className="text-sm text-muted-foreground">
                        {selectedRecord.employeeId} • {selectedRecord.department}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Date</Label>
                      <p className="text-lg">{format(new Date(selectedRecord.date), "PPP")}</p>
                    </div>
                  </div>
                </div>

                {/* Overtime Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <Label className="text-sm font-medium">Extra Hours</Label>
                    <p className="text-2xl font-bold text-blue-600">{selectedRecord.extraHours}h</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Compensation Type</Label>
                    <div className="mt-1">
                      {getCompensationBadge(selectedRecord.compensationType)}
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Reason</Label>
                  <p className="mt-1 p-3 bg-gray-50 rounded-lg">{selectedRecord.reason}</p>
                </div>

                {selectedRecord.linkedAbsence && (
                  <div>
                    <Label className="text-sm font-medium">Linked Absence</Label>
                    <p className="mt-1 text-blue-600">{selectedRecord.linkedAbsence}</p>
                  </div>
                )}

                {selectedRecord.notes && (
                  <div>
                    <Label className="text-sm font-medium">Notes</Label>
                    <p className="mt-1 p-3 bg-gray-50 rounded-lg">{selectedRecord.notes}</p>
                  </div>
                )}

                {/* Status & Approval Info */}
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium">Status</Label>
                    <div className="mt-1">
                      {getStatusBadge(selectedRecord.status)}
                    </div>
                  </div>
                  {selectedRecord.approvedBy && (
                    <div className="text-right">
                      <Label className="text-sm font-medium">Approved By</Label>
                      <p className="text-sm">{selectedRecord.approvedBy}</p>
                      <p className="text-xs text-muted-foreground">
                        {selectedRecord.approvedDate && format(new Date(selectedRecord.approvedDate), "PPP")}
                      </p>
                    </div>
                  )}
                </div>

                {/* Actions */}
                {selectedRecord.status === "pending" && (
                  <div className="flex justify-end gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        handleApproval(selectedRecord.id, false)
                        setDetailsModalOpen(false)
                      }}
                    >
                      <X className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      onClick={() => {
                        handleApproval(selectedRecord.id, true)
                        setDetailsModalOpen(false)
                      }}
                    >
                      <Check className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  )
}

