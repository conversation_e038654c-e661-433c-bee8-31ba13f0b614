"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Clock,
  Calendar,
  Users,
  AlertTriangle,
  Settings,
  FileText,
  TrendingUp,
  UserCheck,
  Edit,
  BarChart3,
  CheckCircle,
  XCircle,
  Timer
} from "lucide-react"

// Navigation items for attendance module
const attendanceNavItems = [
  {
    id: "overview",
    label: "Overview",
    href: "/admin/attendance",
    icon: BarChart3,
    description: "Dashboard and analytics"
  },
  {
    id: "daily",
    label: "Daily Records",
    href: "/admin/attendance/daily",
    icon: Calendar,
    description: "View daily attendance"
  },
  {
    id: "exceptions",
    label: "Exceptions",
    href: "/admin/attendance/exceptions",
    icon: Alert<PERSON>riangle,
    description: "Attendance irregularities",
    badge: "12"
  },
  {
    id: "overtime",
    label: "Overtime",
    href: "/admin/attendance/overtime",
    icon: Timer,
    description: "Extra hours tracking",
    badge: "5"
  },
  {
    id: "normalize",
    label: "Normalize",
    href: "/admin/attendance/normalize",
    icon: Edit,
    description: "Manual corrections"
  },
  {
    id: "reports",
    label: "Reports",
    href: "/admin/attendance/reports",
    icon: FileText,
    description: "Generate reports"
  },
  {
    id: "settings",
    label: "Settings",
    href: "/admin/attendance/settings",
    icon: Settings,
    description: "Configure attendance"
  }
]

// Quick stats data
const quickStats = [
  {
    title: "Present Today",
    value: "847",
    change: "+2.5%",
    trend: "up",
    icon: UserCheck,
    color: "green"
  },
  {
    title: "Late Arrivals",
    value: "23",
    change: "-12%",
    trend: "down",
    icon: Clock,
    color: "orange"
  },
  {
    title: "Exceptions",
    value: "12",
    change: "+5%",
    trend: "up",
    icon: AlertTriangle,
    color: "red"
  },
  {
    title: "Overtime Hours",
    value: "156",
    change: "+8%",
    trend: "up",
    icon: Timer,
    color: "blue"
  }
]

export default function AttendancePage() {
  const pathname = usePathname()

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Attendance Management</h1>
            <p className="text-muted-foreground mt-1">
              Track, manage, and analyze employee attendance data
            </p>
          </div>
          
          <div className="flex gap-3">
            <Link href="/admin/attendance/reports">
              <Button variant="outline">
                <FileText className="w-4 h-4 mr-2" />
                Generate Report
              </Button>
            </Link>
            <Link href="/admin/attendance/settings">
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {quickStats.map((stat, index) => (
            <Card key={stat.title} className="shadow-lg border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-3xl font-bold mt-2">{stat.value}</p>
                    <p className={`text-sm mt-1 ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change} from yesterday
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${
                    stat.color === 'green' ? 'bg-green-100' :
                    stat.color === 'orange' ? 'bg-orange-100' :
                    stat.color === 'red' ? 'bg-red-100' :
                    'bg-blue-100'
                  }`}>
                    <stat.icon className={`w-6 h-6 ${
                      stat.color === 'green' ? 'text-green-600' :
                      stat.color === 'orange' ? 'text-orange-600' :
                      stat.color === 'red' ? 'text-red-600' :
                      'text-blue-600'
                    }`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Navigation Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Attendance Modules
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {attendanceNavItems.map((item) => {
                  const isActive = pathname === item.href
                  return (
                    <Link key={item.id} href={item.href}>
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-6 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                          isActive
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className={`p-2 rounded-lg ${
                            isActive ? 'bg-blue-100' : 'bg-gray-100'
                          }`}>
                            <item.icon className={`w-5 h-5 ${
                              isActive ? 'text-blue-600' : 'text-gray-600'
                            }`} />
                          </div>
                          {item.badge && (
                            <Badge variant="destructive" className="text-xs">
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                        <h3 className={`font-semibold mb-1 ${
                          isActive ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {item.label}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {item.description}
                        </p>
                      </motion.div>
                    </Link>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-orange-50 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  <div className="flex-1">
                    <p className="font-medium">12 new attendance exceptions</p>
                    <p className="text-sm text-muted-foreground">
                      Require review and action
                    </p>
                  </div>
                  <Link href="/admin/attendance/exceptions">
                    <Button size="sm" variant="outline">
                      Review
                    </Button>
                  </Link>
                </div>

                <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
                  <Timer className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium">5 overtime requests pending</p>
                    <p className="text-sm text-muted-foreground">
                      Awaiting approval
                    </p>
                  </div>
                  <Link href="/admin/attendance/overtime">
                    <Button size="sm" variant="outline">
                      Approve
                    </Button>
                  </Link>
                </div>

                <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div className="flex-1">
                    <p className="font-medium">Weekly report generated</p>
                    <p className="text-sm text-muted-foreground">
                      Ready for download
                    </p>
                  </div>
                  <Link href="/admin/attendance/reports">
                    <Button size="sm" variant="outline">
                      Download
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
