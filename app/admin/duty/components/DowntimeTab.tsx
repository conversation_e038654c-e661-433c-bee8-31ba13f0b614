"use client";

import { Clock, AlertCircle } from "lucide-react";
import { motion } from "framer-motion";

const activeDowntimes = [
  { machine: "Machine A12", duration: "3.4 hours", status: "Ongoing" },
  { machine: "Line B3", duration: "1.2 hours", status: "Resolved" },
  { machine: "Packing Unit", duration: "0.8 hours", status: "Resolved" },
];

const analytics = [
  { label: "Most Frequent Issue", value: "Sensor Fault" },
  { label: "Avg Downtime", value: "2.3 hrs/event" },
  { label: "Downtime Compliance Rate", value: "96.4%" },
  { label: "Pending Review Tickets", value: "4" },
];

export function DowntimeTab() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Active Downtimes */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="font-bold text-lg text-gray-900 mb-6">Active Downtimes</h2>
        <div className="space-y-4">
          {activeDowntimes.map((downtime) => (
            <div
              key={downtime.machine}
              className="flex items-center justify-between p-4 rounded-lg border border-gray-100"
            >
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-900">{downtime.machine}</p>
                  <p className="text-sm text-gray-500">{downtime.duration}</p>
                </div>
              </div>
              <span
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  downtime.status === "Ongoing"
                    ? "bg-red-100 text-red-600"
                    : "bg-green-100 text-green-600"
                }`}
              >
                {downtime.status}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Downtime Analytics */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="font-bold text-lg text-gray-900 mb-6">Downtime Analytics</h2>
        <div className="grid grid-cols-2 gap-6">
          {analytics.map((item) => (
            <div key={item.label} className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-500 mb-1">{item.label}</p>
              <p className="font-semibold text-gray-900">{item.value}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}