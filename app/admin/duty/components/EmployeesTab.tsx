"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

const departments = [
  { name: "Production", count: 50, color: "bg-blue-100 text-blue-600" },
  { name: "Maintenance", count: 30, color: "bg-green-100 text-green-600" },
  { name: "Quality Control", count: 20, color: "bg-purple-100 text-purple-600" },
];

export function EmployeesTab() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="font-bold text-lg text-gray-900 mb-1">Employee Management</h2>
          <p className="text-gray-500">Manage all employees across departments</p>
        </div>
        <Button className="bg-black text-white hover:bg-gray-800">
          + Add Employee
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {departments.map((dept) => (
          <motion.div
            key={dept.name}
            whileHover={{ y: -2 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <h3 className="font-semibold text-lg mb-2">{dept.name}</h3>
            <div className="flex items-center justify-between">
              <span className={`px-3 py-1 rounded-full text-sm ${dept.color}`}>
                {dept.count} employees
              </span>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="flex gap-3">
        <Button variant="outline" className="border-gray-200">
          View All Employees
        </Button>
        <Button variant="outline" className="border-gray-200">
          Bulk Import
        </Button>
      </div>
    </div>
  );
}