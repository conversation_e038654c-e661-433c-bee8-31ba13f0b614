"use client";

import { Alert<PERSON>rian<PERSON>, Clock } from "lucide-react";

const alerts = [
  {
    message: "Duty roster for next week not finalized",
    type: "warning",
  },
  {
    message: "5 operators reported idle for more than 4 hours",
    type: "error",
  },
  {
    message: "2 machines need scheduled maintenance",
    type: "warning",
  },
];

const activities = [
  {
    title: "Roster Updated",
    description: "Updated by Supervisor",
    time: "2h ago",
  },
  {
    title: "Downtime logged",
    description: "Line B3 stopped for 1.2 hrs",
    time: "4h ago",
  },
  {
    title: "New Staff Assigned",
    description: "<PERSON> (Maintenance)",
    time: "1 day ago",
  },
];

export function OverviewTab() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Policy Alerts */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="font-bold text-lg text-gray-900 mb-6">Policy Alerts</h2>
        <div className="space-y-4">
          {alerts.map((alert, idx) => (
            <div
              key={idx}
              className={`p-4 rounded-lg flex items-center justify-between ${
                alert.type === "warning" ? "bg-yellow-50" : "bg-red-50"
              }`}
            >
              <div className="flex items-center gap-3">
                <AlertTriangle
                  className={`w-5 h-5 ${
                    alert.type === "warning" ? "text-yellow-500" : "text-red-500"
                  }`}
                />
                <span className="text-gray-700">{alert.message}</span>
              </div>
              <button className="text-blue-600 font-medium hover:text-blue-700">
                Resolve
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="font-bold text-lg text-gray-900 mb-6">Recent Activities</h2>
        <div className="space-y-6">
          {activities.map((activity, idx) => (
            <div key={idx} className="flex items-start gap-4">
              <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{activity.title}</h3>
                <p className="text-sm text-gray-500">{activity.description}</p>
                <span className="text-xs text-gray-400 mt-1">{activity.time}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}