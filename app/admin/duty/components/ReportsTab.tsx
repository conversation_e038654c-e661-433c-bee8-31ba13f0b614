"use client";

import { But<PERSON> } from "@/components/ui/button";
import { FileText } from "lucide-react";

const reportSections = [
  {
    title: "Shift Reports",
    reports: ["Daily Shift Summary", "Late Check-ins", "Absentee Summary"],
  },
  {
    title: "Downtime Reports",
    reports: ["Event Log", "Average Resolution Time", "By Machine/Shift"],
  },
  {
    title: "Employee Reports",
    reports: ["Roster Trends", "Departmental Utilization", "Cross-shift Allocation"],
  },
];

export function ReportsTab() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="font-bold text-lg text-gray-900 mb-1">Reports & Analytics</h2>
          <p className="text-gray-500">Generate and download reports</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          + Generate Report
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {reportSections.map((section) => (
          <div key={section.title} className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="font-semibold text-gray-900 mb-4">{section.title}</h3>
            <div className="space-y-3">
              {section.reports.map((report) => (
                <button
                  key={report}
                  className="flex items-center gap-3 w-full px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors text-left"
                >
                  <FileText className="w-4 h-4 text-gray-400" />
                  {report}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}