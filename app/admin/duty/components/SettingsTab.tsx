"use client";

const settingSections = [
  {
    title: "System Configuration",
    settings: ["Manage Shifts", "Department Setup", "Machine Tags"],
  },
  {
    title: "Notification Settings",
    settings: ["Email Templates", "Alert Rules", "Reminder Settings"],
  },
];

export function SettingsTab() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {settingSections.map((section) => (
        <div key={section.title} className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="font-bold text-lg text-gray-900 mb-6">{section.title}</h2>
          <div className="space-y-4">
            {section.settings.map((setting) => (
              <div
                key={setting}
                className="flex items-center justify-between p-4 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <span className="font-medium text-gray-700">{setting}</span>
                <button className="text-blue-600 text-sm font-medium">Configure</button>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}