"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Users, RefreshCcw, Clock, PlusCircle, AlertTriangle, CheckCircle } from "lucide-react";

// Tab components will be imported
import { OverviewTab } from "./components/OverviewTab";
import { EmployeesTab } from "./components/EmployeesTab";
import { DowntimeTab } from "./components/DowntimeTab";
import { ReportsTab } from "./components/ReportsTab";
import { SettingsTab } from "./components/SettingsTab";

const metrics = [
  {
    title: "Total Employees",
    value: 134,
    subtext: "126 on duty, 8 on leave",
    icon: <Users className="w-5 h-5 text-blue-500" />,
  },
  {
    title: "Pending Requests",
    value: 18,
    subtext: "Across all departments",
    icon: <RefreshCcw className="w-5 h-5 text-orange-500" />,
  },
  {
    title: "Downtime Events",
    value: 12,
    subtext: "Logged this month",
    icon: <Clock className="w-5 h-5 text-red-500" />,
  },
  {
    title: "New Machines Added",
    value: 7,
    subtext: "In the last 30 days",
    icon: <PlusCircle className="w-5 h-5 text-green-500" />,
  },
];

const navTabs = ["Overview", "Employees", "Downtime", "Reports", "Settings"];

export default function DutyPage() {
  const [activeTab, setActiveTab] = useState("Overview");

  // Render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case "Overview":
        return <OverviewTab />;
      case "Employees":
        return <EmployeesTab />;
      case "Downtime":
        return <DowntimeTab />;
      case "Reports":
        return <ReportsTab />;
      case "Settings":
        return <SettingsTab />;
      default:
        return <OverviewTab />;
    }
  };

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        {/* Header */}
        <div className="mb-8">
          <h1 className="font-bold text-2xl text-gray-900">Duty & Downtime Dashboard</h1>
          <p className="text-gray-500 mt-1">
            Welcome back! Monitor employee duties and equipment downtimes easily.
          </p>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {metrics.map((card) => (
            <div
              key={card.title}
              className="bg-white rounded-xl shadow-sm p-6 flex flex-col justify-between"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold text-gray-700">{card.title}</span>
                {card.icon}
              </div>
              <span className="font-bold text-2xl text-gray-900">{card.value}</span>
              <span className="text-sm text-gray-400 mt-2">{card.subtext}</span>
            </div>
          ))}
        </div>

        {/* Tab Navigation */}
        <nav className="flex items-center bg-white rounded-xl shadow-sm mb-8 px-2 py-2 gap-2">
          {navTabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-6 py-2 text-base font-medium rounded-lg transition-colors ${
                activeTab === tab
                  ? "bg-white shadow font-bold"
                  : "bg-transparent text-gray-500 hover:bg-gray-100"
              }`}
              style={activeTab === tab ? { borderBottom: "2px solid #2563eb" } : {}}
            >
              {tab}
            </button>
          ))}
        </nav>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </AdminLayout>
  );
}