@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.font-poppins {
  font-family: var(--font-poppins), system-ui, sans-serif;
}

.font-inter {
  font-family: var(--font-inter), system-ui, sans-serif;
}

.font-sf-pro {
  font-family: var(--font-sf-pro), -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
}

.font-michroma {
  font-family: var(--font-michroma), monospace, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Asset Management Dark Mode Enhancements */
@layer components {
  /* Asset Stats Cards */
  .asset-stats-card {
    @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
    @apply shadow-sm hover:shadow-md dark:shadow-gray-900/20;
    @apply transition-all duration-300;
  }

  .asset-stats-card:hover {
    @apply shadow-lg dark:shadow-gray-900/40;
  }

  /* Asset Table Enhancements */
  .asset-table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800/50;
    @apply border-b border-gray-200 dark:border-gray-700;
    @apply transition-colors duration-200;
  }

  .asset-table-header {
    @apply bg-gray-50 dark:bg-gray-800/50;
    @apply border-b border-gray-200 dark:border-gray-700;
  }

  /* Dialog Enhancements */
  .asset-dialog-content {
    @apply bg-white dark:bg-gray-900;
    @apply border border-gray-200 dark:border-gray-700;
    @apply shadow-xl dark:shadow-gray-900/50;
  }

  .asset-dialog-overlay {
    @apply bg-black/50 dark:bg-black/70;
  }

  /* Form Elements */
  .asset-form-input {
    @apply bg-white dark:bg-gray-800;
    @apply border-gray-300 dark:border-gray-600;
    @apply text-gray-900 dark:text-gray-100;
    @apply placeholder-gray-500 dark:placeholder-gray-400;
  }

  .asset-form-input:focus {
    @apply ring-blue-500 dark:ring-blue-400;
    @apply border-blue-500 dark:border-blue-400;
  }

  /* Badge Variants for Assets */
  .asset-status-active {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .asset-status-available {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }

  .asset-status-maintenance {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400;
  }

  .asset-status-retired {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
  }

  .asset-condition-excellent {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .asset-condition-good {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }

  .asset-condition-fair {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }

  .asset-condition-poor {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400;
  }

  .asset-condition-damaged {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  /* Priority Indicators */
  .priority-low {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
  }

  .priority-normal {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }

  .priority-high {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400;
  }

  .priority-critical {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  /* Asset Cards */
  .asset-card {
    @apply bg-white dark:bg-gray-800;
    @apply border border-gray-200 dark:border-gray-700;
    @apply rounded-lg shadow-sm hover:shadow-md;
    @apply transition-all duration-300;
  }

  .asset-card:hover {
    @apply shadow-lg dark:shadow-gray-900/40;
    @apply border-gray-300 dark:border-gray-600;
  }

  /* Dropdown Menus */
  .asset-dropdown {
    @apply bg-white dark:bg-gray-800;
    @apply border border-gray-200 dark:border-gray-700;
    @apply shadow-lg dark:shadow-gray-900/50;
  }

  .asset-dropdown-item {
    @apply text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-100 dark:hover:bg-gray-700;
    @apply transition-colors duration-150;
  }

  /* Progress Bars */
  .asset-progress-bg {
    @apply bg-gray-200 dark:bg-gray-700;
  }

  .asset-progress-fill {
    @apply bg-blue-600 dark:bg-blue-500;
  }

  /* Alert Variants */
  .asset-alert-info {
    @apply bg-blue-50 dark:bg-blue-900/20;
    @apply border-blue-200 dark:border-blue-800;
    @apply text-blue-800 dark:text-blue-200;
  }

  .asset-alert-warning {
    @apply bg-orange-50 dark:bg-orange-900/20;
    @apply border-orange-200 dark:border-orange-800;
    @apply text-orange-800 dark:text-orange-200;
  }

  .asset-alert-error {
    @apply bg-red-50 dark:bg-red-900/20;
    @apply border-red-200 dark:border-red-800;
    @apply text-red-800 dark:text-red-200;
  }

  .asset-alert-success {
    @apply bg-green-50 dark:bg-green-900/20;
    @apply border-green-200 dark:border-green-800;
    @apply text-green-800 dark:text-green-200;
  }
}
