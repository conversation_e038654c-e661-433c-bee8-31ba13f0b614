"use client";

import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";

const navItems = [
  {
    label: "Dashboard",
    path: "/admin/growth-tracking/dashboard",
  },
  {
    label: "Goals",
    path: "/admin/growth-tracking/goals",
  },
  {
    label: "Reviews",
    path: "/admin/growth-tracking/reviews",
  },
  {
    label: "360° Feedback",
    path: "/admin/growth-tracking/feedback360",
  },
  {
    label: "Reports",
    path: "/admin/growth-tracking/reports",
  },
];

export function GrowthTrackingNavbar() {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <nav className="flex items-center bg-white rounded-xl shadow-sm mb-8 px-2 py-2 gap-2">
      {navItems.map((item) => (
        <button
          key={item.label}
          onClick={() => router.push(item.path)}
          className={cn(
            "px-6 py-2 text-base font-medium rounded-lg transition-colors",
            pathname === item.path
              ? "bg-white shadow font-bold"
              : "bg-transparent text-gray-500 hover:bg-gray-100"
          )}
          style={
            pathname === item.path ? { borderBottom: "2px solid #2563eb" } : {}
          }
        >
          {item.label}
        </button>
      ))}
    </nav>
  );
}