"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { GrowthTrackingNavbar } from "../components/growth-tracking-navbar";
import { Button } from "@/components/ui/button";
import { Calendar, Eye } from "lucide-react";
import { useState } from "react";

const cardStyles = {
	primary: {
		gradient: "from-blue-100 to-blue-400",
		iconBg: "bg-blue-100",
		iconColor: "text-blue-500",
		borderColor: "border-blue-300",
	},
	success: {
		gradient: "from-green-100 to-green-400",
		iconBg: "bg-green-100",
		iconColor: "text-green-500",
		borderColor: "border-green-300",
	},
	warning: {
		gradient: "from-yellow-100 to-yellow-400",
		iconBg: "bg-yellow-100",
		iconColor: "text-yellow-500",
		borderColor: "border-yellow-300",
	},
	info: {
		gradient: "from-indigo-100 to-indigo-400",
		iconBg: "bg-indigo-100",
		iconColor: "text-indigo-500",
		borderColor: "border-indigo-300",
	},
};

const overviewCards = [
	{
		title: "Active Goals",
		value: 12,
		subtext: "+2 from last month",
		icon: <Calendar className="w-5 h-5" />,
		...cardStyles.primary,
	},
	{
		title: "Completed Goals",
		value: 8,
		subtext: "67% completion rate",
		icon: <Calendar className="w-5 h-5" />,
		...cardStyles.success,
	},
	{
		title: "Pending Reviews",
		value: 3,
		subtext: "Due this week",
		icon: <Calendar className="w-5 h-5" />,
		...cardStyles.warning,
	},
	{
		title: "Feedback Requests",
		value: 5,
		subtext: "Awaiting response",
		icon: <Calendar className="w-5 h-5" />,
		...cardStyles.info,
	},
];

const goals = [
	{
		title: "Increase Sales Revenue",
		priority: "High",
		type: "Individual",
		progress: 75,
		due: "2024-03-31",
	},
	{
		title: "Team Collaboration Improvement",
		priority: "Medium",
		type: "Team",
		progress: 60,
		due: "2024-04-15",
	},
	{
		title: "Customer Satisfaction Score",
		priority: "High",
		type: "Individual",
		progress: 90,
		due: "2024-02-28",
	},
];

const reviews = [
  {
    initials: "JS",
    name: "John Smith",
    type: "Quarterly Review",
    status: "Pending",
    due: "2024-01-15",
  },
  {
    initials: "SJ",
    name: "Sarah Johnson",
    type: "Annual Review",
    status: "In Progress",
    due: "2024-01-20",
  },
  {
    initials: "JS",
    name: "John Smith",
    type: "Quarterly Review",
    status: "Pending",
    due: "2024-01-15",
  },
  {
    initials: "SJ",
    name: "Sarah Johnson",
    type: "Annual Review",
    status: "In Progress",
    due: "2024-01-20",
  },
];

function Badge({
	children,
	color,
}: {
	children: React.ReactNode;
	color: string;
}) {
	return (
		<span
			className={`px-3 py-1 rounded-full text-xs font-semibold ${color}`}
			style={{
				backgroundColor:
					color === "red"
						? "#fee2e2"
						: color === "gray"
						? "#f3f4f6"
						: color === "black"
						? "#111827"
						: "#e5e7eb",
				color:
					color === "red"
						? "#dc2626"
						: color === "gray"
						? "#374151"
						: color === "black"
						? "#fff"
						: "#374151",
			}}
		>
			{children}
		</span>
	);
}

function ProgressBar({ value }: { value: number }) {
	return (
		<div className="w-full bg-gray-200 rounded-full h-2 mt-2 mb-1">
			<div
				className="h-2 rounded-full bg-blue-600 transition-all"
				style={{ width: `${value}%` }}
			/>
		</div>
	);
}

export default function GrowthTrackingDashboard() {
	const [activeTab, setActiveTab] = useState("Dashboard");

	return (
		<AdminLayout>
			<div className="max-w-7xl mx-auto pt-2 pb-10">
				<GrowthTrackingNavbar />

				{/* Overview Cards */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
					{overviewCards.map((card) => (
						<div
							key={card.title}
							className={`bg-gradient-to-br ${card.gradient} rounded-xl shadow-sm p-6 flex flex-col justify-between border ${card.borderColor} hover:shadow-md transition-all duration-300`}
						>
							<div className="flex items-center justify-between mb-2">
								<span className="font-semibold text-gray-700">{card.title}</span>
								<div className={`p-2 ${card.iconBg} rounded-lg`}>
									<div className={card.iconColor}>{card.icon}</div>
								</div>
							</div>
							<span className="font-bold text-2xl text-gray-900">
								{card.value}
							</span>
							<span className="text-sm text-gray-500 mt-2">
								{card.subtext}
							</span>
						</div>
					))}
				</div>

				{/* Main Body: 2 Columns */}
				<div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
					{/* Left Column: Recent Goals */}
					<section className="lg:col-span-7">
						<div className="bg-white rounded-xl shadow-sm p-6 mb-6 ">
							<h2 className="font-semibold text-xl text-gray-900 mb-1">
								Recent Goals
							</h2>
							<p className="text-gray-500 mb-6">
								Your current goal progress
							</p>
							<div className="space-y-6">
								{goals.map((goal) => (
									<div
										key={goal.title}
										className="bg-gray-50 rounded-lg p-5 shadow-sm flex flex-col gap-2"
									>
										<div className="flex items-center justify-between">
											<span className="font-semibold text-gray-900 text-lg">
												{goal.title}
											</span>
											<div className="flex gap-2">
												<Badge color={goal.priority === "High" ? "red" : "gray"}>
													{goal.priority}
												</Badge>
												<Badge color="gray">{goal.type}</Badge>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<ProgressBar value={goal.progress} />
											<span className="ml-2 text-sm font-medium text-gray-700">
												{goal.progress}%
											</span>
										</div>
										<div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
											<Calendar className="w-4 h-4" />
											<span>Due: {goal.due}</span>
										</div>
									</div>
								))}
							</div>
							<div className="flex justify-center mt-8">
								<Button
									variant="outline"
									className="font-semibold px-6 py-2"
								>
									+ Create New Goal
								</Button>
							</div>
						</div>
					</section>

					{/* Right Column: Upcoming Reviews */}
					<section className="lg:col-span-5">
						<div className="bg-white rounded-xl shadow-sm p-6 mb-6">
							<h2 className="font-semibold text-xl text-gray-900 mb-1 ">
								Upcoming Reviews
							</h2>
							<p className="text-gray-500 mb-6">
								Performance reviews due soon
							</p>
							<div className="space-y-5 mt-9 mb-9">
								{reviews.map((review) => (
									<div
										key={review.name}
										className="flex items-center bg-gray-50 rounded-lg p-4 shadow-sm"
									>
										{/* Avatar */}
										<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center font-bold text-lg text-blue-700 mr-4 my-1">
											{review.initials}
										</div>
										{/* Info */}
										<div className="flex-1">
											<div className="font-semibold text-gray-900">
												{review.name}
											</div>
											<div className="text-sm text-gray-500">
												{review.type}
											</div>
										</div>
										{/* Status & Due */}
										<div className="flex flex-col items-end gap-2">
											<Badge
												color={
													review.status === "Pending"
														? "red"
														: review.status === "In Progress"
														? "black"
														: "gray"
												}
											>
												{review.status}
											</Badge>
											<span className="text-xs text-gray-500">
												Due: {review.due}
											</span>
										</div>
									</div>
								))}
							</div>
							<div className="flex justify-center mt-8">
								<Button
									variant="outline"
									className="font-semibold px-6 py-2 flex items-center gap-2"
								>
									<Eye className="w-4 h-4" />
									View All Reviews
								</Button>
							</div>
						</div>
					</section>
				</div>
			</div>
		</AdminLayout>
	);
}