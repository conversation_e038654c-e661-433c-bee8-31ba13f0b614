"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { User, MessageCircle, Eye, Send, RefreshCcw, CheckCircle, Clock, Users } from "lucide-react";
import { useState } from "react";
import { GrowthTrackingNavbar } from "../components/growth-tracking-navbar";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON>bsList, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const navTabs = [
	"Dashboard",
	"Goals",
	"Reviews",
	"360° Feedback",
	"Reports",
];

// Change cardStyles from string values to objects
const cardStyles = {
	blue: {
		bgColor: "bg-blue-50",
		textColor: "text-blue-600",
		gradient: "from-blue-100 to-blue-100",
		borderColor: "border-blue-100"
	},
	green: {
		bgColor: "bg-green-50",
		textColor: "text-green-600",
		gradient: "from-green-100 to-green-200",
		borderColor: "border-green-100"
	},
	amber: {
		bgColor: "bg-amber-50",
		textColor: "text-amber-600",
		gradient: "from-yellow-100 to-yellow-100",
		borderColor: "border-amber-100"
	},
	purple: {
		bgColor: "bg-purple-50",
		textColor: "text-purple-600",
		gradient: "from-purple-100 to-purple-200",
		borderColor: "border-purple-100"
	}
};

// Then update the metrics array to use the new object structure
const metrics = [
	{
		title: "Active Cycles",
		value: 3,
		subtext: "Currently running",
		icon: <RefreshCcw className={`w-5 h-5 ${cardStyles.blue.textColor}`} />,
		style: cardStyles.blue
	},
	{
		title: "Completed",
		value: 12,
		subtext: "This quarter",
		icon: <CheckCircle className={`w-5 h-5 ${cardStyles.green.textColor}`} />,
		style: cardStyles.green
	},
	{
		title: "Pending",
		value: 8,
		subtext: "Awaiting feedback",
		icon: <Clock className={`w-5 h-5 ${cardStyles.amber.textColor}`} />,
		style: cardStyles.amber
	},
	{
		title: "Participants",
		value: 45,
		subtext: "Total reviewers",
		icon: <Users className={`w-5 h-5 ${cardStyles.purple.textColor}`} />,
		style: cardStyles.purple
	}
];

const feedbackCycles = [
	{
		employee: "John Smith",
		cycle: "Sarah Johnson",
		status: "Completed",
		progress: "7/8",
		due: "2024-01-15",
		anonymity: "Anonymous",
	},
	{
		employee: "John Smith",
		cycle: "Sarah Johnson",
		status: "In Progress",
		progress: "7/8",
		due: "2024-01-15",
		anonymity: "Anonymous",
	},
	{
		employee: "John Smith",
		cycle: "Sarah Johnson",
		status: "Pending",
		progress: "7/8",
		due: "2024-01-15",
		anonymity: "Identifiable",
	},
];

function StatusPill({ status }: { status: string }) {
	if (status === "Completed") {
		return (
			<span className="px-3 py-1 rounded-full text-xs font-semibold bg-gray-900 text-white">
				Completed
			</span>
		);
	}
	if (status === "In Progress") {
		return (
			<span className="px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-700 border border-gray-300">
				In Progress
			</span>
		);
	}
	// Pending (red)
	return (
		<span className="px-3 py-1 rounded-full text-xs font-semibold bg-red-500 text-white">
			Pending
		</span>
	);
}

function AnonymityTag({ value }: { value: string }) {
	if (value === "Anonymous") {
		return (
			<span className="px-3 py-1 rounded-full text-xs font-semibold bg-gray-900 text-white">
				Anonymous
			</span>
		);
	}
	return (
		<span className="px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-700 border border-gray-300">
			Identifiable
		</span>
	);
}

export default function Feedback360Page() {
	const [activeTab, setActiveTab] = useState("360° Feedback");
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [dueDate, setDueDate] = useState<Date>();
	const [currentStep, setCurrentStep] = useState(0);
	const [showReviewForm, setShowReviewForm] = useState(false);
	const [reviewData, setReviewData] = useState({
		selfRating: "",
		achievements: "",
		improvements: "",
		technicalSkills: "",
		communicationSkills: "",
		managerComments: "",
		overallScore: 0,
		actionItems: [] as string[]
	});

	return (
		<AdminLayout>
			<div className="max-w-7xl mx-auto pt-2 pb-10">
				<GrowthTrackingNavbar />
				

				{/* Section Title & CTA */}
				<div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
					<div>
						<h1 className="font-bold text-2xl md:text-3xl text-gray-900">
							360 Feedback System
						</h1>
						<p className="text-gray-500 mt-1 text-base">
							Collect comprehensive feedback from multiple sources
						</p>
					</div>
					<Button 
						className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg px-6 py-2"
						onClick={() => setShowCreateDialog(true)}
					>
						+ Create Feedback Cycle
					</Button>
				</div>

				{/* Metrics Cards */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 ">
					{metrics.map((card) => (
						<div
							key={card.title}
							className={`bg-gradient-to-br ${card.style.gradient} rounded-xl shadow-sm p-6 flex flex-col justify-between h-[150px] border ${card.style.borderColor}`}
						>
							<div className="flex items-center justify-between mb-2">
								<span className="font-semibold text-gray-700">
									{card.title}
								</span>
								<div className={`p-2 ${card.style.bgColor} rounded-lg`}>
									{card.icon}
								</div>
							</div>
							<span className="font-bold text-2xl text-gray-900">
								{card.value}
							</span>
							<span className="text-sm text-gray-400 mt-2">
								{card.subtext}
							</span>
						</div>
					))}
				</div>

				{/* Feedback Cycles Table Section */}
				<div className="bg-white rounded-xl shadow-sm p-6">
					<h2 className="font-bold text-xl text-gray-900 mb-1">
						Feedback Cycles
					</h2>
					<p className="text-gray-500 mb-6">
						Manage 360 feedback collection
					</p>
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-100">
							<thead>
								<tr className="text-left text-xs text-gray-500 uppercase tracking-wide">
									<th className="px-6 py-3">Employee</th>
									<th className="px-6 py-3">Cycle</th>
									<th className="px-6 py-3">Status</th>
									<th className="px-6 py-3">Progress</th>
									<th className="px-6 py-3">Due Date</th>
									<th className="px-6 py-3">Anonymity</th>
									<th className="px-6 py-3">Actions</th>
								</tr>
							</thead>
							<tbody className="divide-y divide-gray-100">
								{feedbackCycles.map((cycle, idx) => (
									<tr key={idx} className="hover:bg-gray-50 transition">
										<td className="px-6 py-4">{cycle.employee}</td>
										<td className="px-6 py-4">{cycle.cycle}</td>
										<td className="px-6 py-4">
											<StatusPill status={cycle.status} />
										</td>
										<td className="px-6 py-4 font-semibold">
											{cycle.progress}
										</td>
										<td className="px-6 py-4">{cycle.due}</td>
										<td className="px-6 py-4">
											<AnonymityTag value={cycle.anonymity} />
										</td>
										<td className="px-6 py-4">
											<div className="flex gap-2">
												<Button
													size="icon"
													variant="ghost"
													className="h-8 w-8 p-0"
												>
													<Eye className="w-4 h-4" />
												</Button>
												<Button
													size="icon"
													variant="ghost"
													className="h-8 w-8 p-0"
												>
													<Send className="w-4 h-4" />
												</Button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>

				{/* Create Feedback Cycle Dialog */}
				<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
					<DialogContent className="sm:max-w-[600px]">
						<DialogHeader>
							<DialogTitle className="text-xl font-semibold">
								Create 360 Feedback Cycle
							</DialogTitle>
							<DialogDescription>
								Set up a comprehensive feedback collection process
							</DialogDescription>
						</DialogHeader>

						<div className="grid gap-6 py-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<label className="text-sm font-medium">Employee</label>
									<Select>
										<SelectTrigger>
											<SelectValue placeholder="Select Employee" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="john">John Smith</SelectItem>
											<SelectItem value="emily">Emily Davis</SelectItem>
											<SelectItem value="michael">Michael Brown</SelectItem>
										</SelectContent>
									</Select>
								</div>

								<div className="space-y-2">
									<label className="text-sm font-medium">Cycle Name</label>
									<Input placeholder="e.g. Q1 2024 360 Review" />
								</div>
							</div>

							<div className="space-y-2">
								<label className="text-sm font-medium">Select Feedback Providers</label>
								<div className="border rounded-lg p-4 space-y-4 max-h-[200px] overflow-y-auto">
									{[
										{ name: "Sarah Johnson", role: "Manager", avatar: "SJ" },
										{ name: "Michael Brown", role: "Peer", avatar: "MB" },
										{ name: "Lisa Chen", role: "Peer", avatar: "LC" },
										{ name: "David Wilson", role: "Subordinate", avatar: "DW" },
									].map((provider) => (
										<div key={provider.name} className="flex items-center justify-between">
											<div className="flex items-center space-x-3">
												<Checkbox id={provider.name.toLowerCase()} />
												<div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
													{provider.avatar}
												</div>
												<div>
													<label 
														htmlFor={provider.name.toLowerCase()}
														className="text-sm font-medium"
													>
														{provider.name}
													</label>
													<p className="text-xs text-gray-500">{provider.role}</p>
												</div>
											</div>
											<span className="text-xs font-medium px-2 py-1 rounded-full bg-gray-100">
												{provider.role}
											</span>
										</div>
									))}
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<label className="text-sm font-medium">Due Date</label>
									<Popover>
										<PopoverTrigger asChild>
											<Button
												variant="outline"
												className="w-full justify-start text-left font-normal"
											>
												<CalendarIcon className="mr-2 h-4 w-4" />
												{dueDate ? format(dueDate, "PPP") : "Select date"}
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0">
											<Calendar
												mode="single"
												selected={dueDate}
												onSelect={setDueDate}
												initialFocus
											/>
										</PopoverContent>
									</Popover>
								</div>

								<div className="space-y-2">
									<label className="text-sm font-medium">Anonymity</label>
									<Select>
										<SelectTrigger>
											<SelectValue placeholder="Full Anonymous" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="full">Full Anonymous</SelectItem>
											<SelectItem value="partial">Partial Anonymous</SelectItem>
											<SelectItem value="none">No Anonymity</SelectItem>
										</SelectContent>
									</Select>
								</div>
							</div>

							<div className="space-y-2">
								<label className="text-sm font-medium">Review Template</label>
								<Select>
									<SelectTrigger>
										<SelectValue placeholder="Select Template" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="standard">Standard 360 Review</SelectItem>
										<SelectItem value="leadership">Leadership Assessment</SelectItem>
										<SelectItem value="technical">Technical Evaluation</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						<DialogFooter className="flex justify-end gap-2">
							<Button
								variant="outline"
								onClick={() => setShowCreateDialog(false)}
							>
								Cancel
							</Button>
							<Button 
								className="bg-indigo-600 hover:bg-indigo-700 text-white"
								onClick={() => {
									setShowCreateDialog(false);
									setShowReviewForm(true);
								}}
							>
								Create Goal
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>

				{/* Review Form Dialog */}
				<Dialog open={showReviewForm} onOpenChange={setShowReviewForm}>
					<DialogContent className="sm:max-w-[800px]">
						<DialogHeader>
							<DialogTitle className="text-xl font-semibold">
								Create 360 Feedback Cycle
							</DialogTitle>
							<DialogDescription>
								Set up a comprehensive feedback collection process
							</DialogDescription>
						</DialogHeader>

						<Tabs defaultValue="self" className="w-full">
							<TabsList className="grid w-full grid-cols-3">
								<TabsTrigger value="self">Self Assessment</TabsTrigger>
								<TabsTrigger value="manager">Manager Evaluation</TabsTrigger>
								<TabsTrigger value="summary">Summary</TabsTrigger>
							</TabsList>

							{/* Self Assessment Tab */}
							<TabsContent value="self">
								<div className="space-y-6 py-4">
									<div className="space-y-2">
										<label className="text-sm font-medium">How would you rate your overall performance this period?</label>
										<RadioGroup className="flex gap-4">
											{[1, 2, 3, 4, 5].map((value) => (
												<div key={value} className="flex items-center space-x-2">
													<RadioGroupItem value={value.toString()} id={`rating-${value}`} />
													<label htmlFor={`rating-${value}`}>{value}</label>
												</div>
											))}
										</RadioGroup>
									</div>

									<div className="space-y-2">
										<label className="text-sm font-medium">What were your key achievements?</label>
										<Textarea 
											placeholder="List your major accomplishments and successes..."
											className="h-32"
											value={reviewData.achievements}
											onChange={(e) => setReviewData({...reviewData, achievements: e.target.value})}
										/>
									</div>

									<div className="space-y-2">
										<label className="text-sm font-medium">What areas would you like to improve?</label>
										<Textarea 
											placeholder="Describe areas for development and growth..."
											className="h-32"
											value={reviewData.improvements}
											onChange={(e) => setReviewData({...reviewData, improvements: e.target.value})}
										/>
									</div>
								</div>
							</TabsContent>

							{/* Manager Evaluation Tab */}
							<TabsContent value="manager">
								<div className="space-y-6 py-4">
									<div className="grid grid-cols-2 gap-8">
										<div className="space-y-2">
											<label className="text-sm font-medium">Technical Skills</label>
											<RadioGroup className="flex gap-4">
												{[1, 2, 3, 4, 5].map((value) => (
													<div key={value} className="flex items-center space-x-2">
														<RadioGroupItem value={`tech-${value}`} id={`tech-${value}`} />
														<label htmlFor={`tech-${value}`}>{value}</label>
													</div>
												))}
											</RadioGroup>
										</div>

										<div className="space-y-2">
											<label className="text-sm font-medium">Communication Skills</label>
											<RadioGroup className="flex gap-4">
												{[1, 2, 3, 4, 5].map((value) => (
													<div key={value} className="flex items-center space-x-2">
														<RadioGroupItem value={`comm-${value}`} id={`comm-${value}`} />
														<label htmlFor={`comm-${value}`}>{value}</label>
													</div>
												))}
											</RadioGroup>
										</div>
									</div>

									<div className="space-y-2">
										<label className="text-sm font-medium">Additional Comments</label>
										<Textarea 
											placeholder="Provide detailed feedback on performance and areas for improvement..."
											className="h-32"
											value={reviewData.managerComments}
											onChange={(e) => setReviewData({...reviewData, managerComments: e.target.value})}
										/>
									</div>
								</div>
							</TabsContent>

							{/* Summary Tab */}
							<TabsContent value="summary">
								<div className="space-y-6 py-4">
									<div className="bg-gray-50 p-6 rounded-lg">
										<h3 className="text-xl font-semibold mb-4">Review Summary</h3>
										<div className="space-y-4">
											<div>
												<span className="text-gray-600">Overall Score</span>
												<h4 className="text-3xl font-bold">{reviewData.overallScore}/5.0</h4>
											</div>

											<div>
												<h4 className="font-medium mb-2">Action Plan</h4>
												<ul className="list-disc pl-5 space-y-2">
													{reviewData.actionItems.map((item, index) => (
														<li key={index}>{item}</li>
													))}
												</ul>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>
						</Tabs>

						<DialogFooter className="flex justify-end gap-2">
							<Button
								variant="outline"
								onClick={() => setShowReviewForm(false)}
							>
								Close
							</Button>
							<Button 
								className="bg-indigo-600 hover:bg-indigo-700 text-white"
							>
								Save Changes
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</div>
		</AdminLayout>
	);
}