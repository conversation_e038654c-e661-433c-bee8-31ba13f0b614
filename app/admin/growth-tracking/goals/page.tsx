"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Send, Target, Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { useState } from "react";
import { GrowthTrackingNavbar } from "../components/growth-tracking-navbar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

const navTabs = [
	"Dashboard",
	"Goals",
	"Reviews",
	"360° Feedback",
	"Reports",
];

const cardStyles = {
  blue: {
    bgColor: "bg-blue-100",
    textColor: "text-blue-600",
    gradient: "from-blue-100 to-blue-400",
    borderColor: "border-blue-100",
  },
  green: {
    bgColor: "bg-green-100",
    textColor: "text-green-600",
    gradient: "from-green-100 to-green-300",
    borderColor: "border-green-100",
  },
  amber: {
    bgColor: "bg-amber-50",
    textColor: "text-amber-600",
    gradient: "from-yellow-100 to-yellow-200",
    borderColor: "border-amber-100",
  },
  purple: {
    bgColor: "bg-purple-50",
    textColor: "text-purple-600",
    gradient: "from-red-100 to-red-500",
    borderColor: "border-purple-100",
  },
};

const metrics = [
	{
		title: "Total Goals",
		value: 24,
		subtext: "Active goals",
		icon: <Target className={`w-5 h-5 ${cardStyles.blue.textColor}`} />,
		style: cardStyles.blue
	},
	{
		title: "Completed",
		value: "16",
		subtext: "67% success rate",
		icon: <CheckCircle className={`w-5 h-5 ${cardStyles.green.textColor}`} />,
		style: cardStyles.green
	},
	{
		title: "In Progress",
		value: "8",
		subtext: "On track",
		icon: <Clock className={`w-5 h-5 ${cardStyles.amber.textColor}`} />,
		style: cardStyles.amber
	},
	{
		title: "High Priority",
		value: "3",
		subtext: "Need attention",
		icon: <AlertTriangle className={`w-5 h-5 ${cardStyles.purple.textColor}`} />,
		style: cardStyles.purple
	}
];

const goals = [
	{
		title: "Increase Sales Revenue",
		description: "$50,000/month",
		type: "Individual",
		priority: "High",
		progress: "7/8",
		status: "In Progress",
		due: "2024-01-15",
	},
	{
		title: "Team Collaboration Score",
		description: "4.5/5 rating",
		type: "Team",
		priority: "Medium",
		progress: "7/8",
		status: "In Progress",
		due: "2024-01-15",
	},
	{
		title: "Customer Satisfaction",
		description: "95% satisfaction",
		type: "Individual",
		priority: "High",
		progress: "7/8",
		status: "On Track",
		due: "2024-01-15",
	},
];

function PillBadge({
	children,
	color,
}: {
	children: React.ReactNode;
	color: "red" | "black" | "gray";
}) {
	const styles =
		color === "red"
			? "bg-red-100 text-red-600"
			: color === "black"
			? "bg-gray-900 text-white"
			: "bg-gray-100 text-gray-700";
	return (
		<span className={`px-3 py-1 rounded-full text-xs font-semibold ${styles}`}>
			{children}
		</span>
	);
}

export default function GoalsPage() {
	const [activeTab, setActiveTab] = useState("Goals");
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [startDate, setStartDate] = useState<Date>();
	const [endDate, setEndDate] = useState<Date>();

	return (
		<AdminLayout>
			<div className="max-w-7xl mx-auto pt-2 pb-10">
				<GrowthTrackingNavbar />
			

				{/* Page Header */}
				<div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
					<div>
						<h1 className="font-bold text-2xl text-gray-900">All Goals</h1>
						<p className="text-gray-500 mt-1">
							Manage 360 feedback collection
						</p>
					</div>
					<Button 
					  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg px-6 py-2"
					  onClick={() => setShowCreateDialog(true)}
					>
					  + Create Goals
					</Button>
				</div>

				{/* Metrics Cards */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
					{metrics.map((card) => (
						<div
							key={card.title}
							className={`bg-gradient-to-br ${card.style.gradient} rounded-xl shadow-sm p-6 flex flex-col justify-between border ${card.style.borderColor} hover:shadow-md transition-all duration-300`}
						>
							<div className="flex items-center justify-between mb-2">
								<span className="font-medium text-gray-700">{card.title}</span>
								<div className={`p-2 ${card.style.bgColor} rounded-lg`}>
									{card.icon}
								</div>
							</div>
							<span className="font-bold text-2xl text-gray-900">
								{card.value}
							</span>
							<span className="text-sm text-gray-500 mt-2">
								{card.subtext}
							</span>
						</div>
					))}
				</div>

				{/* Goals Table Section */}
				<div className="bg-white rounded-xl shadow-sm p-6">
					<h2 className="font-bold text-xl text-gray-900 mb-1">All Goals</h2>
					<p className="text-gray-500 mb-6">
						Manage 360 feedback collection
					</p>
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-100">
							<thead>
								<tr className="text-left text-xs text-gray-500 uppercase tracking-wide">
									<th className="px-6 py-3">Goal</th>
									<th className="px-6 py-3">Type</th>
									<th className="px-6 py-3">Priority</th>
									<th className="px-6 py-3">Progress</th>
									<th className="px-6 py-3">Status</th>
									<th className="px-6 py-3">Due Date</th>
									<th className="px-6 py-3">Actions</th>
								</tr>
							</thead>
							<tbody className="divide-y divide-gray-100">
								{goals.map((goal, idx) => (
									<tr key={idx} className="hover:bg-gray-50 transition">
										<td className="px-6 py-4">
											<div>
												<span className="font-semibold text-gray-900">
													{goal.title}
												</span>
												<div className="text-xs text-gray-500">
													{goal.description}
												</div>
											</div>
										</td>
										<td className="px-6 py-4">
											<PillBadge color="gray">{goal.type}</PillBadge>
										</td>
										<td className="px-6 py-4">
											<PillBadge
												color={
													goal.priority === "High"
														? "red"
														: "black"
												}
											>
												{goal.priority}
											</PillBadge>
										</td>
										<td className="px-6 py-4 font-semibold">
											{goal.progress}
										</td>
										<td className="px-6 py-4">
											<PillBadge
												color={
													goal.status === "On Track"
														? "black"
														: "gray"
												}
											>
												{goal.status}
											</PillBadge>
										</td>
										<td className="px-6 py-4">{goal.due}</td>
										<td className="px-6 py-4">
											<div className="flex gap-2">
												<Button
													size="icon"
													variant="ghost"
													className="h-8 w-8 p-0"
												>
													<Eye className="w-4 h-4" />
												</Button>
												<Button
													size="icon"
													variant="ghost"
													className="h-8 w-8 p-0"
												>
													<Send className="w-4 h-4" />
												</Button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>

				<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
  <DialogContent className="sm:max-w-[600px]">
    <DialogHeader>
      <DialogTitle className="text-xl font-semibold">Create New Goal</DialogTitle>
      <DialogDescription>
        Set up a SMART goal with specific metrics and timelines
      </DialogDescription>
    </DialogHeader>

    <div className="grid gap-6 py-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Title *</label>
          <Input placeholder="Goal title (max 100 chars)" maxLength={100} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">KPI *</label>
          <Input placeholder="e.g. $10,000/ month" />
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Description</label>
        <Textarea 
          placeholder="Detailed description (max 1000 chars)" 
          className="h-32"
          maxLength={1000}
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Type</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Individual" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="individual">Individual</SelectItem>
              <SelectItem value="team">Team</SelectItem>
              <SelectItem value="department">Department</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Priority</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Medium" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Weightage (%)</label>
          <Input type="number" placeholder="24" min={0} max={100} />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Start Date</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">End Date</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>

    <DialogFooter className="flex justify-end gap-2">
      <Button
        variant="outline"
        onClick={() => setShowCreateDialog(false)}
      >
        Cancel
      </Button>
      <Button 
        className="bg-indigo-600 hover:bg-indigo-700 text-white"
        onClick={() => {
          // Handle form submission
          setShowCreateDialog(false);
        }}
      >
        Create Goal
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
			</div>
		</AdminLayout>
	);
}