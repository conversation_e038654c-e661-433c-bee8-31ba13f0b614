"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Download, TrendingUp, Users, FileText, CheckCircle, MessageCircle } from "lucide-react";
import { useState } from "react";
import { GrowthTrackingNavbar } from "../components/growth-tracking-navbar";

// Top nav tabs
const navTabs = [
  "Dashboard",
  "Goals",
  "Reviews",
  "360° Feedback",
  "Reports",
];

// Metrics cards
const metrics = [
  {
    title: "Total Employees",
    value: 45,
    subtext: "In selected period",
    icon: <Users className="w-5 h-5 text-blue-600" />,
    gradient: "from-blue-50 to-white",
    borderColor: "border-blue-100",
    iconBg: "bg-blue-50",
  },
  {
    title: "Avg Performance",
    value: "4.2/5.0",
    subtext: "+0.2 from last period",
    icon: <TrendingUp className="w-5 h-5 text-green-600" />,
    gradient: "from-green-50 to-white",
    borderColor: "border-green-100",
    iconBg: "bg-green-50",
  },
  {
    title: "Goal Completion",
    value: "87%",
    subtext: "Above target (85%)",
    icon: <CheckCircle className="w-5 h-5 text-purple-600" />,
    gradient: "from-purple-50 to-white",
    borderColor: "border-purple-100",
    iconBg: "bg-purple-50",
  },
  {
    title: "Reviews Done",
    value: 42,
    subtext: "93% completion rate",
    icon: <FileText className="w-5 h-5 text-orange-600" />,
    gradient: "from-orange-50 to-white",
    borderColor: "border-orange-100",
    iconBg: "bg-orange-50",
  },
  {
    title: "360° Cycles",
    value: 8,
    subtext: "Feedback collected",
    icon: <MessageCircle className="w-5 h-5 text-emerald-600" />,
    gradient: "from-emerald-50 to-white",
    borderColor: "border-emerald-100",
    iconBg: "bg-emerald-50",
  },
];

// Tab navigation below metrics
const reportTabs = [
  "Goal Achievement",
  "Performance Trends",
  "Department Analysis",
  "KPI Overview",
];

// Dummy chart data
const chartData = [
  { name: "Sarah Johnson", value: 95 },
  { name: "Emily Davis", value: 90 },
  { name: "Michael Brown", value: 85 },
  { name: "John Smith", value: 92 },
  { name: "Lisa Wilson", value: 88 },
  { name: "David Lee", value: 97 },
];

const topPerformers = [
  { name: "Sarah Johnson", score: "4.0/5.0" },
  { name: "Emily Davis", score: "4.0/5.0" },
  { name: "Michael Brown", score: "4.0/5.0" },
];

const improvements = [
  { area: "Communication Skills", score: "4.0/5.0" },
  { area: "Time Management", score: "4.0/5.0" },
  { area: "Leadership Skills", score: "4.0/5.0" },
];

function PillTab({ active, children, ...props }: any) {
  return (
    <button
      className={`px-5 py-2 rounded-full font-medium text-sm transition-colors ${
        active
          ? "bg-blue-600 text-white shadow"
          : "bg-gray-100 text-gray-700 hover:bg-blue-50"
      }`}
      {...props}
    >
      {children}
    </button>
  );
}

function Badge({ children, color }: { children: React.ReactNode; color: string }) {
  const styles =
    color === "green"
      ? "bg-green-100 text-green-700"
      : color === "yellow"
      ? "bg-yellow-100 text-yellow-700"
      : "bg-gray-100 text-gray-700";
  return (
    <span className={`px-3 py-1 rounded-full text-xs font-semibold ${styles}`}>
      {children}
    </span>
  );
}

// Chart data for each tab
const goalAchievementData = [
  { name: "Sarah Johnson", value: 95 },
  { name: "Emily Davis", value: 90 },
  { name: "Michael Brown", value: 85 },
  { name: "John Smith", value: 92 },
  { name: "Lisa Wilson", value: 88 },
  { name: "David Lee", value: 97 },
];

const performanceTrendsData = [
  { month: "Jan", value: 4.0 },
  { month: "Feb", value: 4.1 },
  { month: "Mar", value: 3.95 },
  { month: "Apr", value: 4.2 },
  { month: "May", value: 4.3 },
  { month: "Jun", value: 4.2 },
];

const departmentAnalysisData = [
  { dept: "Engineering", value: 4.3 },
  { dept: "Sales", value: 3.95 },
  { dept: "Marketing", value: 4.45 },
  { dept: "HR", value: 4.1 },
  { dept: "Finance", value: 3.98 },
];

const kpiOverviewData = [
  { label: "Revenue goals", value: 85, color: "#fbbf24" },
  { label: "Innovation Metrics", value: 88, color: "#34d399" },
  { label: "Team collaboration", value: 78, color: "#ef4444" },
  { label: "Customer Satisfaction", value: 90, color: "#3b82f6" },
];

// Chart components for each tab
function GoalAchievementChart() {
  return (
    <div className="w-full h-64 flex items-end gap-6 px-6 pb-8">
      {goalAchievementData.map((bar, idx) => (
        <div key={bar.name} className="flex flex-col items-center justify-end h-full">
          <div
            className="w-8 rounded-lg"
            style={{
              height: `${bar.value * 2}px`,
              background: idx % 2 === 0 ? "#34d399" : "#fbbf24",
            }}
          />
          <span className="mt-2 text-xs font-medium text-gray-700">{bar.name}</span>
          <span className="text-xs text-gray-500">{bar.value}%</span>
        </div>
      ))}
    </div>
  );
}

function PerformanceTrendsChart() {
  // Calculate points for SVG polyline
  const maxY = 5, minY = 3.5;
  const chartHeight = 200, chartWidth = 600;
  const points = performanceTrendsData.map((d, i) => {
    const x = (i * (chartWidth / (performanceTrendsData.length - 1)));
    const y = chartHeight - ((d.value - minY) / (maxY - minY)) * chartHeight;
    return `${x},${y}`;
  }).join(" ");
  return (
    <div className="w-full h-64 flex items-center justify-center px-6 pb-8">
      <svg width={chartWidth} height={chartHeight}>
        {/* Grid lines */}
        {[...Array(6)].map((_, i) => (
          <line
            key={i}
            x1={0}
            y1={i * (chartHeight / 5)}
            x2={chartWidth}
            y2={i * (chartHeight / 5)}
            stroke="#e5e7eb"
            strokeWidth={1}
          />
        ))}
        {/* Polyline */}
        <polyline
          fill="none"
          stroke="#a78bfa"
          strokeWidth={3}
          points={points}
        />
        {/* Data points */}
        {performanceTrendsData.map((d, i) => {
          const x = (i * (chartWidth / (performanceTrendsData.length - 1)));
          const y = chartHeight - ((d.value - minY) / (maxY - minY)) * chartHeight;
          return (
            <circle key={i} cx={x} cy={y} r={6} fill="#a78bfa" stroke="#fff" strokeWidth={2} />
          );
        })}
        {/* X-axis labels */}
        {performanceTrendsData.map((d, i) => {
          const x = (i * (chartWidth / (performanceTrendsData.length - 1)));
          return (
            <text key={i} x={x} y={chartHeight + 18} textAnchor="middle" fontSize="14" fill="#6b7280">{d.month}</text>
          );
        })}
        {/* Y-axis labels */}
        {[...Array(6)].map((_, i) => {
          const y = chartHeight - (i * (chartHeight / 5));
          const val = (minY + ((maxY - minY) * i / 5)).toFixed(1);
          return (
            <text key={i} x={-10} y={y + 5} textAnchor="end" fontSize="14" fill="#6b7280">{val}</text>
          );
        })}
      </svg>
    </div>
  );
}

function DepartmentAnalysisChart() {
  const maxY = 5, minY = 3.5;
  const chartHeight = 200, chartWidth = 600;
  return (
    <div className="w-full h-64 flex items-center justify-center px-6 pb-8">
      <svg width={chartWidth} height={chartHeight}>
        {/* Grid lines */}
        {[...Array(6)].map((_, i) => (
          <line
            key={i}
            x1={0}
            y1={i * (chartHeight / 5)}
            x2={chartWidth}
            y2={i * (chartHeight / 5)}
            stroke="#e5e7eb"
            strokeWidth={1}
          />
        ))}
        {/* Bars */}
        {departmentAnalysisData.map((d, i) => {
          const barWidth = 60;
          const x = 60 + i * 110;
          const barHeight = ((d.value - minY) / (maxY - minY)) * chartHeight;
          return (
            <g key={d.dept}>
              <rect
                x={x}
                y={chartHeight - barHeight}
                width={barWidth}
                height={barHeight}
                fill="#818cf8"
                rx={8}
              />
              <text
                x={x + barWidth / 2}
                y={chartHeight + 18}
                textAnchor="middle"
                fontSize="14"
                fill="#6b7280"
              >
                {d.dept}
              </text>
            </g>
          );
        })}
        {/* Y-axis labels */}
        {[...Array(6)].map((_, i) => {
          const y = chartHeight - (i * (chartHeight / 5));
          const val = (minY + ((maxY - minY) * i / 5)).toFixed(1);
          return (
            <text key={i} x={40} y={y + 5} textAnchor="end" fontSize="14" fill="#6b7280">{val}</text>
          );
        })}
      </svg>
    </div>
  );
}

function KPIOverviewChart() {
  const total = kpiOverviewData.reduce((sum, d) => sum + d.value, 0);
  let startAngle = 0;
  const cx = 300, cy = 130, r = 100;
  // Pie chart slices
  const slices = kpiOverviewData.map((d, i) => {
    const angle = (d.value / total) * 360;
    const endAngle = startAngle + angle;
    const largeArc = angle > 180 ? 1 : 0;
    const x1 = cx + r * Math.cos((Math.PI * startAngle) / 180);
    const y1 = cy + r * Math.sin((Math.PI * startAngle) / 180);
    const x2 = cx + r * Math.cos((Math.PI * endAngle) / 180);
    const y2 = cy + r * Math.sin((Math.PI * endAngle) / 180);
    const path = `
      M ${cx} ${cy}
      L ${x1} ${y1}
      A ${r} ${r} 0 ${largeArc} 1 ${x2} ${y2}
      Z
    `;
    const midAngle = startAngle + angle / 2;
    const labelX = cx + (r + 40) * Math.cos((Math.PI * midAngle) / 180);
    const labelY = cy + (r + 40) * Math.sin((Math.PI * midAngle) / 180);
    const label = (
      <text
        key={d.label}
        x={labelX}
        y={labelY}
        fontSize="16"
        fill="#6b7280"
        textAnchor={labelX < cx ? "end" : "start"}
        alignmentBaseline="middle"
      >
        {d.label}: {d.value}%
      </text>
    );
    startAngle = endAngle;
    return { path, color: d.color, label };
  });
  return (
    <div className="w-full h-64 flex items-center justify-center px-6 pb-8">
      <svg width={600} height={260}>
        {slices.map((slice, i) => (
          <path key={i} d={slice.path} fill={slice.color} stroke="#fff" strokeWidth={2} />
        ))}
        {slices.map((slice) => slice.label)}
      </svg>
    </div>
  );
}

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState("Reports");
  const [activeReportTab, setActiveReportTab] = useState("Goal Achievement");
  const [period, setPeriod] = useState("Q4 2023");
  const [department, setDepartment] = useState("All Departments");

  // Chart section content based on tab
  let chartSection;
  if (activeReportTab === "Goal Achievement") {
    chartSection = (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8" style={{ minHeight: 370 }}>
        <h2 className="font-bold text-lg text-gray-900 mb-1">Goal Completion by Employee</h2>
        <p className="text-gray-500 mb-6">Individual goal achievement rates</p>
        <GoalAchievementChart />
      </div>
    );
  } else if (activeReportTab === "Performance Trends") {
    chartSection = (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8" style={{ minHeight: 370 }}>
        <h2 className="font-bold text-lg text-gray-900 mb-1">Feedback Cycles</h2>
        <p className="text-gray-500 mb-6">Manage 360 feedback collection</p>
        <PerformanceTrendsChart />
      </div>
    );
  } else if (activeReportTab === "Department Analysis") {
    chartSection = (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8" style={{ minHeight: 370 }}>
        <h2 className="font-bold text-lg text-gray-900 mb-1">Feedback Cycles</h2>
        <p className="text-gray-500 mb-6">Manage 360 feedback collection</p>
        <DepartmentAnalysisChart />
      </div>
    );
  } else if (activeReportTab === "KPI Overview") {
    chartSection = (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8" style={{ minHeight: 370 }}>
        <h2 className="font-bold text-lg text-gray-900 mb-1">Feedback Cycles</h2>
        <p className="text-gray-500 mb-6">Manage 360 feedback collection</p>
        <KPIOverviewChart />
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <GrowthTrackingNavbar />
      

        {/* Header & Filters */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
          <div>
            <h1 className="font-bold text-2xl md:text-3xl text-gray-900">Performance Reports</h1>
            <p className="text-gray-500 mt-1 text-base">
              Analytics and insights on performance data
            </p>
          </div>
          <div className="flex gap-2 items-center">
            <select
              value={period}
              onChange={e => setPeriod(e.target.value)}
              className="rounded-lg border border-gray-200 px-3 py-2 text-sm bg-white"
            >
              <option>Q4 2023</option>
              <option>Q3 2023</option>
              <option>Q2 2023</option>
              <option>Q1 2023</option>
            </select>
            <select
              value={department}
              onChange={e => setDepartment(e.target.value)}
              className="rounded-lg border border-gray-200 px-3 py-2 text-sm bg-white"
            >
              <option>All Departments</option>
              <option>Sales</option>
              <option>HR</option>
              <option>Engineering</option>
              <option>Finance</option>
            </select>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg px-5 py-2 flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export Pdf
            </Button>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
          {metrics.map((card) => (
            <div
              key={card.title}
              className={`bg-gradient-to-br ${card.gradient} rounded-xl shadow-sm p-6 flex flex-col justify-between border ${card.borderColor} hover:shadow-md transition-all duration-300`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-700">{card.title}</span>
                <div className={`p-2 ${card.iconBg} rounded-lg`}>
                  {card.icon}
                </div>
              </div>
              <span className="font-bold text-2xl text-gray-900">{card.value}</span>
              <span className="text-sm text-gray-500 mt-2">{card.subtext}</span>
            </div>
          ))}
        </div>

        {/* Tab Navigation */}
        <div className="flex gap-2 mb-8">
          {reportTabs.map(tab => (
            <PillTab
              key={tab}
              active={activeReportTab === tab}
              onClick={() => setActiveReportTab(tab)}
            >
              {tab}
            </PillTab>
          ))}
        </div>

        {/* Dynamic Chart Section */}
        {chartSection}

        {/* Detailed Performance Report */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900">Detailed Performance Report</h2>
          <p className="text-gray-500 text-sm mb-8">Comprehensive performance data for Q4-2023</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-8">
            {/* Top Performers */}
            <div>
              <h3 className="text-base font-medium text-gray-900 mb-4">Top Performers</h3>
              <div className="space-y-3">
                {topPerformers.map(tp => (
                  <div 
                    key={tp.name}
                    className="flex items-center justify-between py-3 px-4 bg-green-50/60 rounded-lg hover:bg-green-50 transition-colors"
                  >
                    <span className="text-gray-900">{tp.name}</span>
                    <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                      {tp.score}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Area of Improvement */}
            <div>
              <h3 className="text-base font-medium text-gray-900 mb-4">Area of Improvement</h3>
              <div className="space-y-3">
                {improvements.map(impr => (
                  <div 
                    key={impr.area}
                    className="flex items-center justify-between py-3 px-4 bg-yellow-50/60 rounded-lg hover:bg-yellow-50 transition-colors"
                  >
                    <span className="text-gray-900">{impr.area}</span>
                    <span className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
                      {impr.score}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Export Buttons */}
        <div className="flex justify-end gap-2 mb-2">
          <Button variant="outline" className="font-semibold rounded-lg px-4 py-2 flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Pdf
          </Button>
          <Button variant="outline" className="font-semibold rounded-lg px-4 py-2 flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Excel
          </Button>
          <Button variant="outline" className="font-semibold rounded-lg px-4 py-2 flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export CSV
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
}
