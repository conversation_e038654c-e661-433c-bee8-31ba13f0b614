"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { FileText, CheckCircle, Clock, User } from "lucide-react";
import { useState } from "react";
import { GrowthTrackingNavbar } from "../components/growth-tracking-navbar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

const navTabs = [
  "Dashboard",
  "Goals",
  "Reviews",
  "360° Feedback",
  "Reports",
];

const cardStyles = {
  blue: {
    gradient: "from-blue-100 to-blue-200",
    iconBg: "bg-blue-100",
    iconColor: "text-blue-600",
    borderColor: "border-blue-200",
  },
  green: {
    gradient: "from-green-100 to-green-200",
    iconBg: "bg-green-100",
    iconColor: "text-green-600",
    borderColor: "border-green-200",
  },
  amber: {
    gradient: "from-amber-100 to-amber-200",
    iconBg: "bg-amber-100",
    iconColor: "text-amber-600",
    borderColor: "border-amber-200",
  },
  purple: {
    gradient: "from-purple-100 to-purple-200",
    iconBg: "bg-purple-100",
    iconColor: "text-purple-600",
    borderColor: "border-purple-200",
  },
};

const metrics = [
  {
    title: "Total Reviews",
    value: 3,
    subtext: "This period",
    icon: <FileText className="w-5 h-5" />,
    ...cardStyles.blue
  },
  {
    title: "Completed",
    value: 1,
    subtext: "Reviews finished",
    icon: <CheckCircle className="w-5 h-5" />,
    ...cardStyles.green
  },
  {
    title: "In Progress",
    value: 1,
    subtext: "Currently active",
    icon: <Clock className="w-5 h-5" />,
    ...cardStyles.amber
  },
  {
    title: "Average Score",
    value: 4.2,
    subtext: "Out of 5.0",
    icon: <User className="w-5 h-5" />,
    ...cardStyles.purple
  }
];

const reviews = [
  {
    employee: "John Smith",
    reviewer: "Sarah Johnson",
    type: "Quarterly",
    period: "Q4 2023",
    status: "Completed",
    progress: ["Self", "Manager"],
    due: "2024-01-15",
    score: "4.2/5.0",
    action: "View",
  },
  {
    employee: "John Smith",
    reviewer: "Sarah Johnson",
    type: "Annual",
    period: "2023",
    status: "In Progress",
    progress: ["Self", "Manager"],
    due: "2024-01-15",
    score: "Pending",
    action: "View",
  },
  {
    employee: "John Smith",
    reviewer: "Sarah Johnson",
    type: "Monthly",
    period: "Dec 2023",
    status: "Progress",
    progress: ["Self", "Manager"],
    due: "2024-01-15",
    score: "Pending",
    action: "View",
  },
];

function StatusBadge({ status }: { status: string }) {
  if (status === "Completed") {
    return (
      <span className="flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold bg-gray-900 text-white">
        <CheckCircle className="w-4 h-4" />
        Completed
      </span>
    );
  }
  if (status === "In Progress") {
    return (
      <span className="flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold border border-gray-300 bg-white text-gray-700">
        <Clock className="w-4 h-4" />
        In Progress
      </span>
    );
  }
  // Progress (red)
  return (
    <span className="flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-600">
      <Clock className="w-4 h-4 text-red-500" />
      Progress
    </span>
  );
}

function PillBadge({ children, color = "gray" }: { children: React.ReactNode; color?: string }) {
  const styles =
    color === "black"
      ? "bg-gray-900 text-white"
      : "bg-gray-100 text-gray-700";
  return (
    <span className={`px-3 py-1 rounded-full text-xs font-semibold ${styles}`}>
      {children}
    </span>
  );
}

export default function ReviewsPage() {
  const [activeTab, setActiveTab] = useState("Reviews");
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [dueDate, setDueDate] = useState<Date>();

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <GrowthTrackingNavbar />
       

        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
          <div>
            <h1 className="font-bold text-2xl text-gray-900">Performance Reviews</h1>
            <p className="text-gray-500 mt-1">Manage employee performance evaluation</p>
          </div>
          <Button 
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg px-6 py-2"
            onClick={() => setShowScheduleDialog(true)}
          >
            + Schedule review
          </Button>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {metrics.map((card) => (
            <div
              key={card.title}
              className={`bg-gradient-to-br ${card.gradient} rounded-xl shadow-sm p-6 flex flex-col justify-between border ${card.borderColor} hover:shadow-md transition-all duration-300`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-700">{card.title}</span>
                <div className={`p-2 ${card.iconBg} rounded-lg`}>
                  <div className={card.iconColor}>{card.icon}</div>
                </div>
              </div>
              <span className="font-bold text-2xl text-gray-900">{card.value}</span>
              <span className="text-sm text-gray-500 mt-2">{card.subtext}</span>
            </div>
          ))}
        </div>

        {/* Reviews Table Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="font-bold text-xl text-gray-900 mb-1">All Reviews</h2>
          <p className="text-gray-500 mb-6">Your current goal progress</p>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-100">
              <thead>
                <tr className="text-left text-xs text-gray-500 uppercase tracking-wide">
                  <th className="px-6 py-3">Employee</th>
                  <th className="px-6 py-3">Reviewer</th>
                  <th className="px-6 py-3">Type</th>
                  <th className="px-6 py-3">Period</th>
                  <th className="px-6 py-3">Status</th>
                  <th className="px-6 py-3">Progress</th>
                  <th className="px-6 py-3">Due Date</th>
                  <th className="px-6 py-3">Score</th>
                  <th className="px-6 py-3">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {reviews.map((review, idx) => (
                  <tr key={idx} className="hover:bg-gray-50 transition">
                    <td className="px-6 py-4">{review.employee}</td>
                    <td className="px-6 py-4">{review.reviewer}</td>
                    <td className="px-6 py-4">
                      <PillBadge>{review.type}</PillBadge>
                    </td>
                    <td className="px-6 py-4">{review.period}</td>
                    <td className="px-6 py-4">
                      <StatusBadge status={review.status} />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex gap-2">
                        <PillBadge color="black">{review.progress[0]}</PillBadge>
                        <PillBadge color="black">{review.progress[1]}</PillBadge>
                      </div>
                    </td>
                    <td className="px-6 py-4">{review.due}</td>
                    <td className="px-6 py-4">{review.score}</td>
                    <td className="px-6 py-4">
                      <Button variant="link" className="text-blue-600 font-semibold px-0">
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Schedule Review Dialog */}
        <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold">
                Schedule Performance Review
              </DialogTitle>
              <DialogDescription>
                Create a new performance review cycle
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Employee</label>
                  <Select>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Employee" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="john">John Smith</SelectItem>
                      <SelectItem value="sarah">Sarah Johnson</SelectItem>
                      <SelectItem value="mike">Mike Brown</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Reviewer</label>
                  <Select>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Reviewer" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sarah">Sarah Johnson</SelectItem>
                      <SelectItem value="david">David Clark</SelectItem>
                      <SelectItem value="emily">Emily Davis</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Review Type</label>
                  <Select>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="quarterly">Quarterly Review</SelectItem>
                      <SelectItem value="annual">Annual Review</SelectItem>
                      <SelectItem value="monthly">Monthly Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Review Period</label>
                  <Select>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="q4-2023">Q4 2023</SelectItem>
                      <SelectItem value="q1-2024">Q1 2024</SelectItem>
                      <SelectItem value="2023">Year 2023</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Due Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dueDate ? format(dueDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dueDate}
                      onSelect={setDueDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Review Template</label>
                <Select>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Performance Review</SelectItem>
                    <SelectItem value="leadership">Leadership Assessment</SelectItem>
                    <SelectItem value="technical">Technical Evaluation</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowScheduleDialog(false)}
              >
                Cancel
              </Button>
              <Button 
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
                onClick={() => {
                  // Handle form submission
                  setShowScheduleDialog(false);
                }}
              >
                Create Review
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
}