import type React from "react"
import type { Metadata } from "next"
import { Inter, Poppins, Michroma } from "next/font/google"
import "../admin/globals.css"
import "../../components/admin/admin-responsive.css"


const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
})

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
})

const michroma = Michroma({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-michroma",
  display: "swap",
})

export const metadata: Metadata = {
  title: "SRNR IT Solutions - Admin Dashboard",
  description: "Premium Admin Management Dashboard",
  generator: 'Next.js',
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${poppins.variable} ${inter.variable} ${michroma.variable}`}>
      <head>
        <link rel="icon" href="/srnr_logo.png" type="image/png" />
      </head>
      <body className="font-poppins antialiased bg-gray-100">{children}</body>
    </html>
  )
}
