"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertTriangle, Search, Filter, Mail, Phone, Eye, MoreHorizontal, Users, Clock, AlertCircle } from "lucide-react"
import { useState } from "react"

// Dummy data for absentees
const absenteesData = [
  {
    id: "EMP001",
    name: "<PERSON>",
    department: "IT",
    role: "Senior Developer",
    lastActiveDate: "2024-02-01",
    daysAbsent: 3,
    status: "unnotified",
    contactAttempts: 0,
    emergencyContact: "******-0123",
    manager: "Sarah Johnson",
    reason: null
  },
  {
    id: "EMP007",
    name: "Alex Rodriguez",
    department: "Sales",
    role: "Sales Representative",
    lastActiveDate: "2024-01-30",
    daysAbsent: 5,
    status: "investigating",
    contactAttempts: 2,
    emergencyContact: "******-0456",
    manager: "Mike Wilson",
    reason: null
  },
  {
    id: "EMP012",
    name: "Lisa Chen",
    department: "Marketing",
    role: "Marketing Coordinator",
    lastActiveDate: "2024-02-02",
    daysAbsent: 2,
    status: "notified",
    contactAttempts: 1,
    emergencyContact: "******-0789",
    manager: "Emily Davis",
    reason: "Family emergency - verbal confirmation"
  },
  {
    id: "EMP018",
    name: "Robert Taylor",
    department: "Finance",
    role: "Financial Analyst",
    lastActiveDate: "2024-01-29",
    daysAbsent: 6,
    status: "emergency",
    contactAttempts: 3,
    emergencyContact: "******-0321",
    manager: "David Brown",
    reason: "Medical emergency - hospitalized"
  },
  {
    id: "EMP025",
    name: "Maria Garcia",
    department: "HR",
    role: "HR Specialist",
    lastActiveDate: "2024-02-03",
    daysAbsent: 1,
    status: "unnotified",
    contactAttempts: 0,
    emergencyContact: "******-0654",
    manager: "Jennifer Lee",
    reason: null
  }
]

function getStatusBadge(status: string) {
  switch (status) {
    case "unnotified":
      return <Badge className="bg-red-100 text-red-700">Unnotified</Badge>
    case "notified":
      return <Badge className="bg-yellow-100 text-yellow-700">Notified</Badge>
    case "investigating":
      return <Badge className="bg-orange-100 text-orange-700">Investigating</Badge>
    case "emergency":
      return <Badge className="bg-purple-100 text-purple-700">Emergency</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getSeverityColor(daysAbsent: number) {
  if (daysAbsent >= 5) return "text-red-600"
  if (daysAbsent >= 3) return "text-orange-600"
  return "text-yellow-600"
}

export default function AbsenteesPage() {
  const [selectedAbsentees, setSelectedAbsentees] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [minDaysFilter, setMinDaysFilter] = useState("1")

  // Filter absentees based on current filters
  const filteredAbsentees = absenteesData.filter(absentee => {
    const matchesSearch = 
      absentee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      absentee.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesDepartment = departmentFilter === "all" || absentee.department === departmentFilter
    const matchesStatus = statusFilter === "all" || absentee.status === statusFilter
    const matchesMinDays = absentee.daysAbsent >= parseInt(minDaysFilter)

    return matchesSearch && matchesDepartment && matchesStatus && matchesMinDays
  })

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAbsentees(filteredAbsentees.map(a => a.id))
    } else {
      setSelectedAbsentees([])
    }
  }

  const handleSelectAbsentee = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedAbsentees([...selectedAbsentees, id])
    } else {
      setSelectedAbsentees(selectedAbsentees.filter(selectedId => selectedId !== id))
    }
  }

  const handleBulkAction = (action: string) => {
    console.log(`Performing ${action} on:`, selectedAbsentees)
    // In a real app, this would trigger the appropriate action
    setSelectedAbsentees([])
  }

  const handleStatusUpdate = (id: string, newStatus: string) => {
    console.log(`Updating ${id} status to:`, newStatus)
    // In a real app, this would update the backend
  }

  const criticalAbsentees = filteredAbsentees.filter(a => a.daysAbsent >= 5).length
  const unnotifiedAbsentees = filteredAbsentees.filter(a => a.status === "unnotified").length

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-gray-900">Unnotified Absentees</h1>
            <p className="text-gray-600 mt-1">Track employees absent without leave applications</p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              disabled={selectedAbsentees.length === 0}
              onClick={() => handleBulkAction("send-reminder")}
              className="gap-2"
            >
              <Mail className="h-4 w-4" />
              Send Reminder ({selectedAbsentees.length})
            </Button>
            <Button 
              variant="outline" 
              disabled={selectedAbsentees.length === 0}
              onClick={() => handleBulkAction("call-emergency")}
              className="gap-2"
            >
              <Phone className="h-4 w-4" />
              Call Emergency Contact
            </Button>
          </div>
        </div>

        {/* Alert for Critical Cases */}
        {criticalAbsentees > 0 && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Critical Alert:</strong> {criticalAbsentees} employee(s) have been absent for 5+ days without notification.
              Immediate action required.
            </AlertDescription>
          </Alert>
        )}

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Absentees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{filteredAbsentees.length}</div>
              <p className="text-xs text-gray-500 mt-1">Currently tracking</p>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Unnotified</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{unnotifiedAbsentees}</div>
              <p className="text-xs text-gray-500 mt-1">No contact made</p>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Critical Cases</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{criticalAbsentees}</div>
              <p className="text-xs text-gray-500 mt-1">5+ days absent</p>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Avg Days Absent</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {filteredAbsentees.length > 0 
                  ? Math.round(filteredAbsentees.reduce((sum, a) => sum + a.daysAbsent, 0) / filteredAbsentees.length * 10) / 10
                  : 0
                }
              </div>
              <p className="text-xs text-gray-500 mt-1">Average duration</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="unnotified">Unnotified</SelectItem>
                  <SelectItem value="notified">Notified</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="emergency">Emergency</SelectItem>
                </SelectContent>
              </Select>

              <Select value={minDaysFilter} onValueChange={setMinDaysFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Min Days Absent" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1+ days</SelectItem>
                  <SelectItem value="2">2+ days</SelectItem>
                  <SelectItem value="3">3+ days</SelectItem>
                  <SelectItem value="5">5+ days</SelectItem>
                  <SelectItem value="7">7+ days</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm("")
                  setDepartmentFilter("all")
                  setStatusFilter("all")
                  setMinDaysFilter("1")
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Absentees Table */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Absentee Tracking</CardTitle>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedAbsentees.length === filteredAbsentees.length && filteredAbsentees.length > 0}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm text-gray-600">Select All</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="w-12"></TableHead>
                  <TableHead className="font-semibold">Employee</TableHead>
                  <TableHead className="font-semibold">Last Active</TableHead>
                  <TableHead className="font-semibold">Days Absent</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Contact Attempts</TableHead>
                  <TableHead className="font-semibold">Manager</TableHead>
                  <TableHead className="font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAbsentees.map((absentee) => (
                  <TableRow key={absentee.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Checkbox
                        checked={selectedAbsentees.includes(absentee.id)}
                        onCheckedChange={(checked) => handleSelectAbsentee(absentee.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {absentee.daysAbsent >= 5 && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <div>
                          <div className="font-medium text-gray-900">{absentee.name}</div>
                          <div className="text-sm text-gray-500">{absentee.id}</div>
                          <div className="text-xs text-gray-400">{absentee.department} • {absentee.role}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{new Date(absentee.lastActiveDate).toLocaleDateString()}</div>
                        <div className="text-gray-500 text-xs">
                          {Math.floor((new Date().getTime() - new Date(absentee.lastActiveDate).getTime()) / (1000 * 60 * 60 * 24))} days ago
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`text-lg font-bold ${getSeverityColor(absentee.daysAbsent)}`}>
                        {absentee.daysAbsent}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(absentee.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{absentee.contactAttempts}</div>
                        <div className="text-gray-500 text-xs">attempts made</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{absentee.manager}</div>
                        <div className="text-gray-500 text-xs">Direct Manager</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleStatusUpdate(absentee.id, "notified")}>
                            <Mail className="mr-2 h-4 w-4" />
                            Mark as Notified
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusUpdate(absentee.id, "investigating")}>
                            <Eye className="mr-2 h-4 w-4" />
                            Start Investigation
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusUpdate(absentee.id, "emergency")}>
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            Mark as Emergency
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Phone className="mr-2 h-4 w-4" />
                            Call: {absentee.emergencyContact}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Additional Information */}
        {filteredAbsentees.some(a => a.reason) && (
          <Card className="rounded-2xl shadow-md">
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredAbsentees
                  .filter(a => a.reason)
                  .map((absentee) => (
                    <div key={absentee.id} className="border-l-4 border-blue-500 pl-4 py-2">
                      <div className="font-medium text-gray-900">{absentee.name} ({absentee.id})</div>
                      <div className="text-sm text-gray-600 mt-1">{absentee.reason}</div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  )
}