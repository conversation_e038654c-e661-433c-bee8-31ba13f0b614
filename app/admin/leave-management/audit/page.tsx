"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  CalendarIcon,
  Search,
  ArrowLeft,
  Download,
  Filter,
  Eye,
  FileText
} from "lucide-react"
import { format } from "date-fns"
import type { DateRange } from "react-day-picker"

interface AuditLog {
  id: string
  timestamp: string
  action: string
  performedBy: string
  targetEmployee: string
  details: string
  ipAddress: string
  userAgent: string
}

export default function LeaveAuditPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedAction, setSelectedAction] = useState("all")
  const [selectedUser, setSelectedUser] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>()

  // Mock audit data
  const auditLogs: AuditLog[] = [
    {
      id: "1",
      timestamp: "2024-01-15T10:30:00Z",
      action: "leave_approved",
      performedBy: "<EMAIL>",
      targetEmployee: "<EMAIL>",
      details: "Approved annual leave request for 5 days",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
  ]

  const getActionBadge = (action: string) => {
    const config: Record<string, { color: string; label: string }> = {
      "leave_approved": { color: "bg-green-100 text-green-800", label: "Approved" },
      "leave_rejected": { color: "bg-red-100 text-red-800", label: "Rejected" },
      "leave_created": { color: "bg-blue-100 text-blue-800", label: "Created" },
      "leave_modified": { color: "bg-yellow-100 text-yellow-800", label: "Modified" },
      "leave_cancelled": { color: "bg-gray-100 text-gray-800", label: "Cancelled" }
    }
    
    const { color, label } = config[action] || { color: "bg-gray-100 text-gray-800", label: action }
    return <Badge className={`text-xs ${color}`}>{label}</Badge>
  }

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div className="flex items-center gap-4">
            <Link href="/admin/leave-management">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Leave Management
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Leave Audit Trail</h1>
              <p className="text-muted-foreground mt-1">Track all leave-related activities and changes</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Logs
            </Button>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
        >
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedAction} onValueChange={setSelectedAction}>
            <SelectTrigger>
              <SelectValue placeholder="Action Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              <SelectItem value="leave_approved">Approved</SelectItem>
              <SelectItem value="leave_rejected">Rejected</SelectItem>
              <SelectItem value="leave_created">Created</SelectItem>
              <SelectItem value="leave_modified">Modified</SelectItem>
              <SelectItem value="leave_cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedUser} onValueChange={setSelectedUser}>
            <SelectTrigger>
              <SelectValue placeholder="Performed By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Users</SelectItem>
              <SelectItem value="admin">Admin Users</SelectItem>
              <SelectItem value="manager">Managers</SelectItem>
              <SelectItem value="hr">HR Team</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </motion.div>

        {/* Audit Logs Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Audit Logs ({auditLogs.length} entries)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Performed By</TableHead>
                      <TableHead>Target Employee</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          {format(new Date(log.timestamp), "MMM dd, yyyy HH:mm:ss")}
                        </TableCell>
                        <TableCell>
                          {getActionBadge(log.action)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {log.performedBy}
                        </TableCell>
                        <TableCell>
                          {log.targetEmployee}
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <p className="text-sm truncate">{log.details}</p>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {log.ipAddress}
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
