"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Upload, Search, Filter, Download, FileUp, Edit, ArrowLeft, Calendar, BarChart3, FileText } from "lucide-react"
import { useState } from "react"
import Link from "next/link"
import { LeaveBreadcrumb } from "@/components/leave/LeaveBreadcrumb"
import { LeaveManagementLayout } from "@/components/leave/LeaveManagementLayout"
import { LeaveBalanceTable } from "@/components/leave/LeaveBalanceTable"
import { CSVUpload } from "@/components/leave/CSVUpload"

// Dummy data for employee leave balances
const employeeBalances = [
  {
    id: "EMP001",
    name: "John Smith",
    department: "IT",
    role: "Senior Developer",
    sickLeave: { used: 3, total: 12, remaining: 9 },
    casualLeave: { used: 5, total: 12, remaining: 7 },
    emergencyLeave: { used: 1, total: 3, remaining: 2 },
    totalPaidLeaves: 27,
    totalUnpaidLeaves: 2,
    lastUpdated: "2024-01-15"
  },
  {
    id: "EMP002",
    name: "Sarah Johnson",
    department: "HR",
    role: "HR Manager",
    sickLeave: { used: 2, total: 12, remaining: 10 },
    casualLeave: { used: 8, total: 12, remaining: 4 },
    emergencyLeave: { used: 0, total: 3, remaining: 3 },
    totalPaidLeaves: 27,
    totalUnpaidLeaves: 0,
    lastUpdated: "2024-01-20"
  },
  {
    id: "EMP003",
    name: "Mike Wilson",
    department: "Sales",
    role: "Sales Representative",
    sickLeave: { used: 6, total: 12, remaining: 6 },
    casualLeave: { used: 4, total: 12, remaining: 8 },
    emergencyLeave: { used: 2, total: 3, remaining: 1 },
    totalPaidLeaves: 27,
    totalUnpaidLeaves: 1,
    lastUpdated: "2024-01-18"
  },
  {
    id: "EMP004",
    name: "Emily Davis",
    department: "Marketing",
    role: "Marketing Specialist",
    sickLeave: { used: 1, total: 12, remaining: 11 },
    casualLeave: { used: 3, total: 12, remaining: 9 },
    emergencyLeave: { used: 0, total: 3, remaining: 3 },
    totalPaidLeaves: 27,
    totalUnpaidLeaves: 0,
    lastUpdated: "2024-01-22"
  },
  {
    id: "EMP005",
    name: "David Brown",
    department: "IT",
    role: "Frontend Developer",
    sickLeave: { used: 4, total: 12, remaining: 8 },
    casualLeave: { used: 6, total: 12, remaining: 6 },
    emergencyLeave: { used: 1, total: 3, remaining: 2 },
    totalPaidLeaves: 27,
    totalUnpaidLeaves: 0,
    lastUpdated: "2024-01-25"
  }
]

function getUsageColor(used: number, total: number) {
  const percentage = (used / total) * 100
  if (percentage >= 80) return "text-red-600"
  if (percentage >= 60) return "text-yellow-600"
  return "text-green-600"
}

function EditBalanceDialog({ employee, onSave }: { employee: any, onSave: (data: any) => void }) {
  const [formData, setFormData] = useState({
    sickLeave: employee.sickLeave.total,
    casualLeave: employee.casualLeave.total,
    emergencyLeave: employee.emergencyLeave.total
  })

  const handleSave = () => {
    onSave({
      ...employee,
      sickLeave: { ...employee.sickLeave, total: formData.sickLeave, remaining: formData.sickLeave - employee.sickLeave.used },
      casualLeave: { ...employee.casualLeave, total: formData.casualLeave, remaining: formData.casualLeave - employee.casualLeave.used },
      emergencyLeave: { ...employee.emergencyLeave, total: formData.emergencyLeave, remaining: formData.emergencyLeave - employee.emergencyLeave.used }
    })
  }

  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Edit Leave Balance - {employee.name}</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <div>
          <Label htmlFor="sickLeave">Sick Leave Total</Label>
          <Input
            id="sickLeave"
            type="number"
            value={formData.sickLeave}
            onChange={(e) => setFormData({ ...formData, sickLeave: parseInt(e.target.value) || 0 })}
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="casualLeave">Casual Leave Total</Label>
          <Input
            id="casualLeave"
            type="number"
            value={formData.casualLeave}
            onChange={(e) => setFormData({ ...formData, casualLeave: parseInt(e.target.value) || 0 })}
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="emergencyLeave">Emergency Leave Total</Label>
          <Input
            id="emergencyLeave"
            type="number"
            value={formData.emergencyLeave}
            onChange={(e) => setFormData({ ...formData, emergencyLeave: parseInt(e.target.value) || 0 })}
            className="mt-1"
          />
        </div>
        <div className="flex gap-2 pt-4">
          <Button onClick={handleSave} className="flex-1">Save Changes</Button>
          <Button variant="outline" className="flex-1">Cancel</Button>
        </div>
      </div>
    </DialogContent>
  )
}

function CSVUploadDialog() {
  const [file, setFile] = useState<File | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
    }
  }

  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Upload Leave Balances</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <FileUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Drop your CSV file here, or click to browse</p>
            <Input
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="hidden"
              id="csv-upload"
            />
            <Label htmlFor="csv-upload" className="cursor-pointer">
              <Button variant="outline" className="mt-2">Choose File</Button>
            </Label>
          </div>
        </div>
        {file && (
          <div className="text-sm text-gray-600">
            Selected: {file.name}
          </div>
        )}
        <div className="text-xs text-gray-500">
          <p>CSV should contain columns: Employee ID, Sick Leave, Casual Leave, Emergency Leave</p>
        </div>
        <div className="flex gap-2 pt-4">
          <Button className="flex-1" disabled={!file}>Upload & Process</Button>
          <Button variant="outline" className="flex-1">Cancel</Button>
        </div>
      </div>
    </DialogContent>
  )
}

export default function LeaveBalancePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [roleFilter, setRoleFilter] = useState("all")
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null)

  // Filter employees based on search and filters
  const filteredEmployees = employeeBalances.filter(employee => {
    const matchesSearch = 
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesDepartment = departmentFilter === "all" || employee.department === departmentFilter
    const matchesRole = roleFilter === "all" || employee.role === roleFilter

    return matchesSearch && matchesDepartment && matchesRole
  })

  const handleSaveBalance = (updatedEmployee: any) => {
    // In a real app, this would update the backend
    console.log("Saving balance for:", updatedEmployee)
    setSelectedEmployee(null)
  }

  return (
    <LeaveManagementLayout>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <LeaveBreadcrumb />

        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Link href="/admin/leave-management">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-semibold text-gray-900">Leave Balance Management</h1>
              <p className="text-gray-600 mt-1">Monitor and manage employee leave balances</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Link href="/admin/leave-management/calendar">
              <Button variant="outline" className="gap-2">
                <Calendar className="h-4 w-4" />
                Calendar
              </Button>
            </Link>
            <Link href="/admin/leave-management/reports">
              <Button variant="outline" className="gap-2">
                <BarChart3 className="h-4 w-4" />
                Reports
              </Button>
            </Link>
            <Link href="/admin/leave-management/policies">
              <Button variant="outline" className="gap-2">
                <FileText className="h-4 w-4" />
                Policies
              </Button>
            </Link>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{employeeBalances.length}</div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Avg Sick Leave Used</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(employeeBalances.reduce((sum, emp) => sum + emp.sickLeave.used, 0) / employeeBalances.length)}
              </div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Avg Casual Leave Used</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(employeeBalances.reduce((sum, emp) => sum + emp.casualLeave.used, 0) / employeeBalances.length)}
              </div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">High Usage Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {employeeBalances.filter(emp => 
                  (emp.sickLeave.used / emp.sickLeave.total) > 0.7 ||
                  (emp.casualLeave.used / emp.casualLeave.total) > 0.7
                ).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>

              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="Senior Developer">Senior Developer</SelectItem>
                  <SelectItem value="HR Manager">HR Manager</SelectItem>
                  <SelectItem value="Sales Representative">Sales Representative</SelectItem>
                  <SelectItem value="Marketing Specialist">Marketing Specialist</SelectItem>
                  <SelectItem value="Frontend Developer">Frontend Developer</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm("")
                  setDepartmentFilter("all")
                  setRoleFilter("all")
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Balance Table */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle>Employee Leave Balances</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Employee</TableHead>
                  <TableHead className="font-semibold">Sick Leave</TableHead>
                  <TableHead className="font-semibold">Casual Leave</TableHead>
                  <TableHead className="font-semibold">Emergency Leave</TableHead>
                  <TableHead className="font-semibold">Total Leaves</TableHead>
                  <TableHead className="font-semibold">Last Updated</TableHead>
                  <TableHead className="font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEmployees.map((employee) => (
                  <TableRow key={employee.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div>
                        <div className="font-medium text-gray-900">{employee.name}</div>
                        <div className="text-sm text-gray-500">{employee.id}</div>
                        <div className="text-xs text-gray-400">{employee.department} • {employee.role}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${getUsageColor(employee.sickLeave.used, employee.sickLeave.total)}`}>
                          {employee.sickLeave.used} / {employee.sickLeave.total}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(employee.sickLeave.used / employee.sickLeave.total) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">{employee.sickLeave.remaining} remaining</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${getUsageColor(employee.casualLeave.used, employee.casualLeave.total)}`}>
                          {employee.casualLeave.used} / {employee.casualLeave.total}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${(employee.casualLeave.used / employee.casualLeave.total) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">{employee.casualLeave.remaining} remaining</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${getUsageColor(employee.emergencyLeave.used, employee.emergencyLeave.total)}`}>
                          {employee.emergencyLeave.used} / {employee.emergencyLeave.total}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-red-600 h-2 rounded-full" 
                            style={{ width: `${(employee.emergencyLeave.used / employee.emergencyLeave.total) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">{employee.emergencyLeave.remaining} remaining</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm font-medium text-gray-900">
                          Paid: {employee.totalPaidLeaves}
                        </div>
                        <div className="text-sm text-gray-600">
                          Unpaid: {employee.totalUnpaidLeaves}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-600">
                        {new Date(employee.lastUpdated).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="h-8 w-8 p-0"
                            onClick={() => setSelectedEmployee(employee)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        {selectedEmployee && (
                          <EditBalanceDialog 
                            employee={selectedEmployee} 
                            onSave={handleSaveBalance}
                          />
                        )}
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </LeaveManagementLayout>
  )
}
