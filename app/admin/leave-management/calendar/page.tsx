"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Users, Filter } from "lucide-react"
import { useState } from "react"

// Dummy data for leave calendar
const leaveData = [
  {
    id: "L001",
    employeeName: "<PERSON>",
    employeeId: "EMP001",
    department: "IT",
    leaveType: "Sick Leave",
    startDate: "2024-02-05",
    endDate: "2024-02-07",
    status: "approved"
  },
  {
    id: "L002",
    employeeName: "<PERSON>",
    employeeId: "EMP002",
    department: "HR",
    leaveType: "Casual Leave",
    startDate: "2024-02-12",
    endDate: "2024-02-14",
    status: "approved"
  },
  {
    id: "L003",
    employeeName: "<PERSON>",
    employeeId: "EMP003",
    department: "Sales",
    leaveType: "Emergency Leave",
    startDate: "2024-02-08",
    endDate: "2024-02-08",
    status: "approved"
  },
  {
    id: "L004",
    employeeName: "Emily Davis",
    employeeId: "EMP004",
    department: "Marketing",
    leaveType: "Holiday Leave",
    startDate: "2024-02-20",
    endDate: "2024-02-23",
    status: "approved"
  },
  {
    id: "L005",
    employeeName: "David Brown",
    employeeId: "EMP005",
    department: "IT",
    leaveType: "Casual Leave",
    startDate: "2024-02-15",
    endDate: "2024-02-16",
    status: "approved"
  }
]

const months = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
]

const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

function getLeaveTypeColor(leaveType: string) {
  switch (leaveType) {
    case "Sick Leave":
      return "bg-blue-100 text-blue-700 border-blue-200"
    case "Casual Leave":
      return "bg-green-100 text-green-700 border-green-200"
    case "Emergency Leave":
      return "bg-red-100 text-red-700 border-red-200"
    case "Holiday Leave":
      return "bg-yellow-100 text-yellow-700 border-yellow-200"
    default:
      return "bg-gray-100 text-gray-700 border-gray-200"
  }
}

function isDateInRange(date: Date, startDate: string, endDate: string) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  return date >= start && date <= end
}

function getEmployeesOnLeave(date: Date, leaves: any[], departmentFilter: string) {
  return leaves.filter(leave => {
    const matchesDepartment = departmentFilter === "all" || leave.department === departmentFilter
    return matchesDepartment && isDateInRange(date, leave.startDate, leave.endDate)
  })
}

function CalendarDay({ date, leaves, departmentFilter }: { date: Date, leaves: any[], departmentFilter: string }) {
  const employeesOnLeave = getEmployeesOnLeave(date, leaves, departmentFilter)
  const isToday = date.toDateString() === new Date().toDateString()
  const isCurrentMonth = date.getMonth() === new Date().getMonth()

  return (
    <div className={`min-h-24 p-2 border border-gray-200 ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'} ${isToday ? 'ring-2 ring-blue-500' : ''}`}>
      <div className={`text-sm font-medium mb-1 ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}`}>
        {date.getDate()}
      </div>
      <div className="space-y-1">
        {employeesOnLeave.slice(0, 2).map((leave, index) => (
          <Popover key={index}>
            <PopoverTrigger asChild>
              <div className={`text-xs px-1 py-0.5 rounded cursor-pointer truncate ${getLeaveTypeColor(leave.leaveType)}`}>
                {leave.employeeName}
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3">
              <div className="space-y-2">
                <div className="font-medium">{leave.employeeName}</div>
                <div className="text-sm text-gray-600">ID: {leave.employeeId}</div>
                <div className="text-sm text-gray-600">Department: {leave.department}</div>
                <Badge className={getLeaveTypeColor(leave.leaveType)}>
                  {leave.leaveType}
                </Badge>
                <div className="text-sm text-gray-600">
                  {new Date(leave.startDate).toLocaleDateString()} - {new Date(leave.endDate).toLocaleDateString()}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        ))}
        {employeesOnLeave.length > 2 && (
          <div className="text-xs text-gray-500 px-1">
            +{employeesOnLeave.length - 2} more
          </div>
        )}
      </div>
    </div>
  )
}

function WeeklyView({ currentDate, leaves, departmentFilter }: { currentDate: Date, leaves: any[], departmentFilter: string }) {
  const startOfWeek = new Date(currentDate)
  startOfWeek.setDate(currentDate.getDate() - currentDate.getDay())

  const weekDates = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)
    return date
  })

  return (
    <div className="grid grid-cols-7 gap-1">
      {weekDays.map(day => (
        <div key={day} className="p-3 text-center font-medium text-gray-700 bg-gray-50 border border-gray-200">
          {day}
        </div>
      ))}
      {weekDates.map((date, index) => (
        <CalendarDay key={index} date={date} leaves={leaves} departmentFilter={departmentFilter} />
      ))}
    </div>
  )
}

function MonthlyView({ currentDate, leaves, departmentFilter }: { currentDate: Date, leaves: any[], departmentFilter: string }) {
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  
  const firstDayOfMonth = new Date(year, month, 1)
  const lastDayOfMonth = new Date(year, month + 1, 0)
  const startDate = new Date(firstDayOfMonth)
  startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay())
  
  const endDate = new Date(lastDayOfMonth)
  endDate.setDate(endDate.getDate() + (6 - lastDayOfMonth.getDay()))

  const calendarDates = []
  const currentDateIter = new Date(startDate)
  
  while (currentDateIter <= endDate) {
    calendarDates.push(new Date(currentDateIter))
    currentDateIter.setDate(currentDateIter.getDate() + 1)
  }

  return (
    <div className="grid grid-cols-7 gap-1">
      {weekDays.map(day => (
        <div key={day} className="p-3 text-center font-medium text-gray-700 bg-gray-50 border border-gray-200">
          {day}
        </div>
      ))}
      {calendarDates.map((date, index) => (
        <CalendarDay key={index} date={date} leaves={leaves} departmentFilter={departmentFilter} />
      ))}
    </div>
  )
}

export default function LeaveCalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<"weekly" | "monthly">("monthly")
  const [departmentFilter, setDepartmentFilter] = useState("all")

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(currentDate.getMonth() - 1)
    } else {
      newDate.setMonth(currentDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const navigateWeek = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setDate(currentDate.getDate() - 7)
    } else {
      newDate.setDate(currentDate.getDate() + 7)
    }
    setCurrentDate(newDate)
  }

  const navigate = (direction: "prev" | "next") => {
    if (viewMode === "monthly") {
      navigateMonth(direction)
    } else {
      navigateWeek(direction)
    }
  }

  const filteredLeaves = leaveData.filter(leave => 
    departmentFilter === "all" || leave.department === departmentFilter
  )

  const totalEmployeesOnLeave = new Set(
    filteredLeaves
      .filter(leave => isDateInRange(new Date(), leave.startDate, leave.endDate))
      .map(leave => leave.employeeId)
  ).size

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-gray-900">Leave Calendar</h1>
            <p className="text-gray-600 mt-1">Visual overview of employee leave schedules</p>
          </div>
          <div className="flex gap-2">
            <Select value={viewMode} onValueChange={(value: "weekly" | "monthly") => setViewMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">On Leave Today</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{totalEmployeesOnLeave}</div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Sick Leave</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {filteredLeaves.filter(l => l.leaveType === "Sick Leave").length}
              </div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Casual Leave</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {filteredLeaves.filter(l => l.leaveType === "Casual Leave").length}
              </div>
            </CardContent>
          </Card>
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Emergency Leave</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {filteredLeaves.filter(l => l.leaveType === "Emergency Leave").length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Calendar Navigation */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate("prev")}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <h2 className="text-xl font-semibold">
                  {viewMode === "monthly" 
                    ? `${months[currentDate.getMonth()]} ${currentDate.getFullYear()}`
                    : `Week of ${currentDate.toLocaleDateString()}`
                  }
                </h2>
                <Button variant="outline" size="sm" onClick={() => navigate("next")}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
                Today
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {viewMode === "monthly" ? (
              <MonthlyView 
                currentDate={currentDate} 
                leaves={filteredLeaves} 
                departmentFilter={departmentFilter}
              />
            ) : (
              <WeeklyView 
                currentDate={currentDate} 
                leaves={filteredLeaves} 
                departmentFilter={departmentFilter}
              />
            )}
          </CardContent>
        </Card>

        {/* Legend */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Leave Type Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-100 border border-blue-200 rounded"></div>
                <span className="text-sm">Sick Leave</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-100 border border-green-200 rounded"></div>
                <span className="text-sm">Casual Leave</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-100 border border-red-200 rounded"></div>
                <span className="text-sm">Emergency Leave</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"></div>
                <span className="text-sm">Holiday Leave</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}