"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { CalendarDays, Users, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { LeaveNavigation } from "@/components/leave/LeaveNavigation"
import Link from "next/link"

// Dummy data
const summaryStats = {
  totalEmployees: 247,
  onLeaveToday: 18,
  onLeaveWithoutPermission: 3,
  pendingRequests: 12,
  approvedRequests: 45,
  rejectedRequests: 8,
  paidLeaveDaysMTD: 156,
  unpaidLeaveDaysMTD: 23
}

const leaveTypeData = [
  { name: 'Sick Leave', value: 35, color: '#ef4444' },
  { name: '<PERSON><PERSON>ual Leave', value: 28, color: '#3b82f6' },
  { name: 'Emergency Leave', value: 15, color: '#f59e0b' },
  { name: 'Holiday Leave', value: 22, color: '#10b981' }
]

const monthlyTrendsData = [
  { month: 'Jan', sick: 12, casual: 18, emergency: 5, holiday: 8 },
  { month: 'Feb', sick: 15, casual: 22, emergency: 7, holiday: 12 },
  { month: 'Mar', sick: 18, casual: 25, emergency: 9, holiday: 15 },
  { month: 'Apr', sick: 14, casual: 20, emergency: 6, holiday: 18 },
  { month: 'May', sick: 16, casual: 24, emergency: 8, holiday: 20 },
  { month: 'Jun', sick: 13, casual: 19, emergency: 4, holiday: 16 }
]

const COLORS = ['#ef4444', '#3b82f6', '#f59e0b', '#10b981']

export default function LeaveManagementDashboard() {
  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-gray-900">Leave Management</h1>
            <p className="text-gray-600 mt-1">Monitor and manage employee leave requests</p>
          </div>
          <div className="flex gap-3">
            <Select defaultValue="all-departments">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-departments">All Departments</SelectItem>
                <SelectItem value="it">IT</SelectItem>
                <SelectItem value="hr">HR</SelectItem>
                <SelectItem value="sales">Sales</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all-types">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Leave Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-types">All Types</SelectItem>
                <SelectItem value="sick">Sick Leave</SelectItem>
                <SelectItem value="casual">Casual Leave</SelectItem>
                <SelectItem value="emergency">Emergency Leave</SelectItem>
                <SelectItem value="holiday">Holiday Leave</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="this-month">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="this-month">This Month</SelectItem>
                <SelectItem value="last-month">Last Month</SelectItem>
                <SelectItem value="this-quarter">This Quarter</SelectItem>
                <SelectItem value="this-year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="rounded-2xl shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Employees</CardTitle>
              <Users className="h-5 w-5 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{summaryStats.totalEmployees}</div>
              <p className="text-xs text-green-600 mt-1">+2.5% from last month</p>
            </CardContent>
          </Card>

          <Card className="rounded-2xl shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">On Leave Today</CardTitle>
              <CalendarDays className="h-5 w-5 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{summaryStats.onLeaveToday}</div>
              <p className="text-xs text-red-600 mt-1">{summaryStats.onLeaveWithoutPermission} without permission</p>
            </CardContent>
          </Card>

          <Card className="rounded-2xl shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Pending Requests</CardTitle>
              <Clock className="h-5 w-5 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{summaryStats.pendingRequests}</div>
              <p className="text-xs text-gray-600 mt-1">Requires attention</p>
            </CardContent>
          </Card>

          <Card className="rounded-2xl shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Leave Days (MTD)</CardTitle>
              <AlertCircle className="h-5 w-5 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{summaryStats.paidLeaveDaysMTD + summaryStats.unpaidLeaveDaysMTD}</div>
              <p className="text-xs text-gray-600 mt-1">{summaryStats.paidLeaveDaysMTD} paid, {summaryStats.unpaidLeaveDaysMTD} unpaid</p>
            </CardContent>
          </Card>
        </div>

        {/* Request Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="rounded-2xl shadow-md border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Approved Requests</CardTitle>
              <CheckCircle className="h-5 w-5 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-700">{summaryStats.approvedRequests}</div>
              <p className="text-xs text-gray-600 mt-1">This month</p>
            </CardContent>
          </Card>

          <Card className="rounded-2xl shadow-md border-l-4 border-l-yellow-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Pending Requests</CardTitle>
              <Clock className="h-5 w-5 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-700">{summaryStats.pendingRequests}</div>
              <p className="text-xs text-gray-600 mt-1">Awaiting approval</p>
            </CardContent>
          </Card>

          <Card className="rounded-2xl shadow-md border-l-4 border-l-red-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Rejected Requests</CardTitle>
              <XCircle className="h-5 w-5 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-700">{summaryStats.rejectedRequests}</div>
              <p className="text-xs text-gray-600 mt-1">This month</p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Leave Type Distribution */}
          <Card className="rounded-2xl shadow-md">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-900">Leave Type Distribution</CardTitle>
              <p className="text-sm text-gray-600">Current month breakdown</p>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={leaveTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {leaveTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card className="rounded-2xl shadow-md">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-900">Monthly Leave Trends</CardTitle>
              <p className="text-sm text-gray-600">Leave requests by type over time</p>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyTrendsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="sick" stackId="a" fill="#ef4444" name="Sick" />
                  <Bar dataKey="casual" stackId="a" fill="#3b82f6" name="Casual" />
                  <Bar dataKey="emergency" stackId="a" fill="#f59e0b" name="Emergency" />
                  <Bar dataKey="holiday" stackId="a" fill="#10b981" name="Holiday" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Navigation to Sub-pages */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-900">Leave Management Modules</CardTitle>
            <p className="text-gray-600">Navigate to different sections of the leave management system</p>
          </CardHeader>
          <CardContent>
            <LeaveNavigation variant="grid" showDescriptions={true} />
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-900">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Link href="/admin/leave-management/requests">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  View All Requests
                </Button>
              </Link>
              <Link href="/admin/leave-management/reports">
                <Button variant="outline">
                  Export Leave Report
                </Button>
              </Link>
              <Link href="/admin/leave-management/policies">
                <Button variant="outline">
                  Leave Policy Settings
                </Button>
              </Link>
              <Link href="/admin/leave-management/calendar">
                <Button variant="outline">
                  Holiday Calendar
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}