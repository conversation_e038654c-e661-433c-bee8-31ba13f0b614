"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Settings,
  Calendar,
  Clock,
  Users
} from "lucide-react"

interface LeavePolicy {
  id: string
  name: string
  type: string
  maxDays: number
  carryForward: boolean
  accrualType: string
  department: string
  description: string
  isActive: boolean
}

export default function LeavePoliciesPage() {
  const [policies, setPolicies] = useState<LeavePolicy[]>([
    {
      id: "1",
      name: "Annual Leave Policy",
      type: "annual",
      maxDays: 25,
      carryForward: true,
      accrualType: "monthly",
      department: "all",
      description: "Standard annual leave policy for all employees",
      isActive: true
    }
  ])

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingPolicy, setEditingPolicy] = useState<LeavePolicy | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    type: "annual",
    maxDays: 0,
    carryForward: false,
    accrualType: "monthly",
    department: "all",
    description: ""
  })

  const handleCreatePolicy = () => {
    const newPolicy: LeavePolicy = {
      id: Date.now().toString(),
      ...formData,
      isActive: true
    }
    setPolicies([...policies, newPolicy])
    setIsCreateModalOpen(false)
    resetForm()
  }

  const resetForm = () => {
    setFormData({
      name: "",
      type: "annual",
      maxDays: 0,
      carryForward: false,
      accrualType: "monthly",
      department: "all",
      description: ""
    })
  }

  const getTypeBadge = (type: string) => {
    const config: Record<string, { color: string; label: string }> = {
      "annual": { color: "bg-blue-100 text-blue-800", label: "Annual" },
      "sick": { color: "bg-red-100 text-red-800", label: "Sick" },
      "maternity": { color: "bg-pink-100 text-pink-800", label: "Maternity" },
      "paternity": { color: "bg-green-100 text-green-800", label: "Paternity" },
      "personal": { color: "bg-purple-100 text-purple-800", label: "Personal" }
    }
    
    const { color, label } = config[type] || { color: "bg-gray-100 text-gray-800", label: type }
    return <Badge className={`text-xs ${color}`}>{label}</Badge>
  }

  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
        >
          <div className="flex items-center gap-4">
            <Link href="/admin/leave-management">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Leave Management
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Leave Policies</h1>
              <p className="text-muted-foreground mt-1">Configure leave policies and rules</p>
            </div>
          </div>
          
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Policy
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Leave Policy</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Policy Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter policy name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Leave Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="annual">Annual Leave</SelectItem>
                        <SelectItem value="sick">Sick Leave</SelectItem>
                        <SelectItem value="maternity">Maternity Leave</SelectItem>
                        <SelectItem value="paternity">Paternity Leave</SelectItem>
                        <SelectItem value="personal">Personal Leave</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxDays">Maximum Days</Label>
                    <Input
                      id="maxDays"
                      type="number"
                      value={formData.maxDays}
                      onChange={(e) => setFormData({...formData, maxDays: parseInt(e.target.value) || 0})}
                      placeholder="Enter max days"
                    />
                  </div>
                  <div>
                    <Label htmlFor="accrualType">Accrual Type</Label>
                    <Select value={formData.accrualType} onValueChange={(value) => setFormData({...formData, accrualType: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                        <SelectItem value="immediate">Immediate</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Select value={formData.department} onValueChange={(value) => setFormData({...formData, department: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        <SelectItem value="engineering">Engineering</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="hr">HR</SelectItem>
                        <SelectItem value="finance">Finance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <Switch
                      id="carryForward"
                      checked={formData.carryForward}
                      onCheckedChange={(checked) => setFormData({...formData, carryForward: checked})}
                    />
                    <Label htmlFor="carryForward">Allow Carry Forward</Label>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Enter policy description"
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePolicy}>
                    Create Policy
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Policies Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-muted-foreground">
                Leave Policies ({policies.length} policies)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Policy Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Max Days</TableHead>
                      <TableHead>Accrual</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Carry Forward</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {policies.map((policy) => (
                      <TableRow key={policy.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{policy.name}</div>
                            <div className="text-xs text-muted-foreground truncate max-w-xs">
                              {policy.description}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getTypeBadge(policy.type)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-blue-500" />
                            <span className="font-medium">{policy.maxDays} days</span>
                          </div>
                        </TableCell>
                        <TableCell className="capitalize">
                          {policy.accrualType}
                        </TableCell>
                        <TableCell className="capitalize">
                          {policy.department === "all" ? "All Departments" : policy.department}
                        </TableCell>
                        <TableCell>
                          <Badge className={`text-xs ${
                            policy.carryForward 
                              ? "bg-green-100 text-green-800" 
                              : "bg-gray-100 text-gray-800"
                          }`}>
                            {policy.carryForward ? "Yes" : "No"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={`text-xs ${
                            policy.isActive 
                              ? "bg-green-100 text-green-800" 
                              : "bg-red-100 text-red-800"
                          }`}>
                            {policy.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
