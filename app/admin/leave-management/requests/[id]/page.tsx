"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { ArrowLeft, Check, X, Calendar, Clock, User, Building, FileText, Download } from "lucide-react"
import { useState } from "react"
import Link from "next/link"

// Dummy data for a specific leave request
const leaveRequestData = {
  id: "LR001",
  employee: {
    name: "<PERSON>",
    id: "EMP001",
    email: "<EMAIL>",
    role: "Senior Developer",
    department: "IT",
    avatar: "/placeholder-user.jpg"
  },
  leaveDetails: {
    type: "Sick Leave",
    fromDate: "2024-01-15",
    toDate: "2024-01-17",
    totalDays: 3,
    isPaid: true,
    appliedDate: "2024-01-10",
    reason: "Fever and flu symptoms. Doctor has advised complete rest for 3 days. I have attached the medical certificate for your reference."
  },
  leaveBalance: {
    sick: { used: 7, total: 12, remaining: 5 },
    casual: { used: 9, total: 12, remaining: 3 },
    emergency: { used: 2, total: 3, remaining: 1 },
    holiday: { used: 8, total: 21, remaining: 13 }
  },
  attachments: [
    { name: "medical_certificate.pdf", size: "245 KB", type: "pdf" },
    { name: "doctor_prescription.jpg", size: "156 KB", type: "image" }
  ],
  status: "pending",
  adminComments: "",
  history: [
    { date: "2024-01-10", action: "Leave request submitted", by: "John Smith" },
    { date: "2024-01-11", action: "Request forwarded to manager", by: "System" },
    { date: "2024-01-12", action: "Under review", by: "Sarah Johnson (Manager)" }
  ]
}

export default function LeaveRequestDetailsPage({ params }: { params: { id: string } }) {
  const [adminRemarks, setAdminRemarks] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  const handleApprove = async () => {
    setIsProcessing(true)
    // Simulate API call
    setTimeout(() => {
      setIsProcessing(false)
      // Handle success
    }, 2000)
  }

  const handleReject = async () => {
    setIsProcessing(true)
    // Simulate API call
    setTimeout(() => {
      setIsProcessing(false)
      // Handle success
    }, 2000)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-700">Approved</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-700">Rejected</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/admin/leave-management/requests">
            <Button variant="ghost" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Requests
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-semibold text-gray-900">Leave Request Details</h1>
            <p className="text-gray-600 mt-1">Request ID: {leaveRequestData.id}</p>
          </div>
          {getStatusBadge(leaveRequestData.status)}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Employee Information */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Employee Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-500" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900">{leaveRequestData.employee.name}</h3>
                    <p className="text-gray-600">{leaveRequestData.employee.role}</p>
                    <div className="mt-2 space-y-1">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Employee ID:</span> {leaveRequestData.employee.id}
                      </p>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Email:</span> {leaveRequestData.employee.email}
                      </p>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Department:</span> {leaveRequestData.employee.department}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Leave Details */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Leave Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Leave Type</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">{leaveRequestData.leaveDetails.type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Duration</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">{leaveRequestData.leaveDetails.totalDays} days</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">From Date</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">
                      {new Date(leaveRequestData.leaveDetails.fromDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">To Date</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">
                      {new Date(leaveRequestData.leaveDetails.toDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Leave Type</label>
                    <Badge variant={leaveRequestData.leaveDetails.isPaid ? "default" : "secondary"} className="mt-1">
                      {leaveRequestData.leaveDetails.isPaid ? "Paid Leave" : "Unpaid Leave"}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Applied Date</label>
                    <p className="text-lg font-semibold text-gray-900 mt-1">
                      {new Date(leaveRequestData.leaveDetails.appliedDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="mt-6">
                  <label className="text-sm font-medium text-gray-600">Reason for Leave</label>
                  <p className="text-gray-900 mt-2 p-4 bg-gray-50 rounded-lg">
                    {leaveRequestData.leaveDetails.reason}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Attachments */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Attachments
                </CardTitle>
              </CardHeader>
              <CardContent>
                {leaveRequestData.attachments.length > 0 ? (
                  <div className="space-y-3">
                    {leaveRequestData.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="font-medium text-gray-900">{attachment.name}</p>
                            <p className="text-sm text-gray-500">{attachment.size}</p>
                          </div>
                        </div>
                        <Button size="sm" variant="outline" className="gap-2">
                          <Download className="h-4 w-4" />
                          Download
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No attachments provided</p>
                )}
              </CardContent>
            </Card>

            {/* Request History */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Request History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {leaveRequestData.history.map((item, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{item.action}</p>
                        <p className="text-sm text-gray-600">by {item.by}</p>
                        <p className="text-xs text-gray-500">{new Date(item.date).toLocaleDateString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Leave Balance */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle>Leave Balance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(leaveRequestData.leaveBalance).map(([type, balance]) => (
                    <div key={type} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium capitalize">{type} Leave</span>
                        <span className="text-sm text-gray-600">{balance.remaining} left</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(balance.remaining / balance.total) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500">
                        {balance.used} used of {balance.total} total
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Admin Actions */}
            {leaveRequestData.status === "pending" && (
              <Card className="rounded-2xl shadow-md">
                <CardHeader>
                  <CardTitle>Admin Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 mb-2 block">
                      Admin Remarks (Optional)
                    </label>
                    <Textarea
                      placeholder="Add your comments here..."
                      value={adminRemarks}
                      onChange={(e) => setAdminRemarks(e.target.value)}
                      rows={4}
                    />
                  </div>
                  
                  <div className="flex gap-3">
                    <Button 
                      onClick={handleApprove}
                      disabled={isProcessing}
                      className="flex-1 bg-green-600 hover:bg-green-700 gap-2"
                    >
                      <Check className="h-4 w-4" />
                      Approve
                    </Button>
                    <Button 
                      onClick={handleReject}
                      disabled={isProcessing}
                      variant="destructive"
                      className="flex-1 gap-2"
                    >
                      <X className="h-4 w-4" />
                      Reject
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Requests This Year</span>
                    <span className="font-semibold">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Approved Requests</span>
                    <span className="font-semibold text-green-600">8</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Pending Requests</span>
                    <span className="font-semibold text-yellow-600">2</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Rejected Requests</span>
                    <span className="font-semibold text-red-600">2</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}