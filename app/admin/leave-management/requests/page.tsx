"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, Check, X, Search, Filter, ArrowLeft, Calendar, Users, BarChart3 } from "lucide-react"
import { useState } from "react"
import Link from "next/link"
import { LeaveBreadcrumb } from "@/components/leave/LeaveBreadcrumb"

// Dummy data for leave requests
const leaveRequests = [
  {
    id: "LR001",
    employeeName: "<PERSON>",
    employeeId: "EMP001",
    department: "IT",
    leaveType: "Sick Leave",
    fromDate: "2024-01-15",
    toDate: "2024-01-17",
    totalDays: 3,
    isPaid: true,
    status: "pending",
    appliedDate: "2024-01-10",
    reason: "Fever and flu symptoms"
  },
  {
    id: "LR002",
    employeeName: "<PERSON> <PERSON>",
    employeeId: "EMP002",
    department: "HR",
    leaveType: "Casual Leave",
    fromDate: "2024-01-20",
    toDate: "2024-01-22",
    totalDays: 3,
    isPaid: true,
    status: "approved",
    appliedDate: "2024-01-12",
    reason: "Personal work"
  },
  {
    id: "LR003",
    employeeName: "Mike Wilson",
    employeeId: "EMP003",
    department: "Sales",
    leaveType: "Emergency Leave",
    fromDate: "2024-01-18",
    toDate: "2024-01-18",
    totalDays: 1,
    isPaid: false,
    status: "rejected",
    appliedDate: "2024-01-17",
    reason: "Family emergency"
  },
  {
    id: "LR004",
    employeeName: "Emily Davis",
    employeeId: "EMP004",
    department: "Marketing",
    leaveType: "Holiday Leave",
    fromDate: "2024-02-01",
    toDate: "2024-02-05",
    totalDays: 5,
    isPaid: true,
    status: "pending",
    appliedDate: "2024-01-14",
    reason: "Vacation with family"
  },
  {
    id: "LR005",
    employeeName: "David Brown",
    employeeId: "EMP005",
    department: "IT",
    leaveType: "Sick Leave",
    fromDate: "2024-01-25",
    toDate: "2024-01-26",
    totalDays: 2,
    isPaid: true,
    status: "approved",
    appliedDate: "2024-01-23",
    reason: "Medical checkup"
  },
  {
    id: "LR006",
    employeeName: "Lisa Anderson",
    employeeId: "EMP006",
    department: "Finance",
    leaveType: "Casual Leave",
    fromDate: "2024-02-10",
    toDate: "2024-02-12",
    totalDays: 3,
    isPaid: true,
    status: "pending",
    appliedDate: "2024-01-28",
    reason: "Wedding ceremony"
  }
]

function getStatusBadge(status: string) {
  switch (status) {
    case "approved":
      return <Badge className="bg-green-100 text-green-700 hover:bg-green-100">Approved</Badge>
    case "rejected":
      return <Badge className="bg-red-100 text-red-700 hover:bg-red-100">Rejected</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-700 hover:bg-yellow-100">Pending</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getLeaveTypeBadge(leaveType: string) {
  switch (leaveType) {
    case "Sick Leave":
      return <Badge variant="outline" className="border-red-200 text-red-700">Sick</Badge>
    case "Casual Leave":
      return <Badge variant="outline" className="border-blue-200 text-blue-700">Casual</Badge>
    case "Emergency Leave":
      return <Badge variant="outline" className="border-orange-200 text-orange-700">Emergency</Badge>
    case "Holiday Leave":
      return <Badge variant="outline" className="border-green-200 text-green-700">Holiday</Badge>
    default:
      return <Badge variant="outline">{leaveType}</Badge>
  }
}

export default function LeaveRequestsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [leaveTypeFilter, setLeaveTypeFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Filter requests based on search and filters
  const filteredRequests = leaveRequests.filter(request => {
    const matchesSearch = 
      request.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.department.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || request.status === statusFilter
    const matchesLeaveType = leaveTypeFilter === "all" || request.leaveType === leaveTypeFilter
    const matchesDepartment = departmentFilter === "all" || request.department === departmentFilter

    return matchesSearch && matchesStatus && matchesLeaveType && matchesDepartment
  })

  // Pagination
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedRequests = filteredRequests.slice(startIndex, startIndex + itemsPerPage)

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <LeaveBreadcrumb />

        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Link href="/admin/leave-management">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-semibold text-gray-900">Leave Requests</h1>
              <p className="text-gray-600 mt-1">Review and manage employee leave requests</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Link href="/admin/leave-management/calendar">
              <Button variant="outline" className="gap-2">
                <Calendar className="h-4 w-4" />
                Calendar View
              </Button>
            </Link>
            <Link href="/admin/leave-management/balance">
              <Button variant="outline" className="gap-2">
                <Users className="h-4 w-4" />
                Balances
              </Button>
            </Link>
            <Link href="/admin/leave-management/reports">
              <Button variant="outline" className="gap-2">
                <BarChart3 className="h-4 w-4" />
                Reports
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>

              <Select value={leaveTypeFilter} onValueChange={setLeaveTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Leave Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Sick Leave">Sick Leave</SelectItem>
                  <SelectItem value="Casual Leave">Casual Leave</SelectItem>
                  <SelectItem value="Emergency Leave">Emergency Leave</SelectItem>
                  <SelectItem value="Holiday Leave">Holiday Leave</SelectItem>
                </SelectContent>
              </Select>

              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm("")
                  setStatusFilter("all")
                  setLeaveTypeFilter("all")
                  setDepartmentFilter("all")
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredRequests.length)} of {filteredRequests.length} requests
          </p>
        </div>

        {/* Requests Table */}
        <Card className="rounded-2xl shadow-md">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Employee</TableHead>
                  <TableHead className="font-semibold">Department</TableHead>
                  <TableHead className="font-semibold">Leave Type</TableHead>
                  <TableHead className="font-semibold">Duration</TableHead>
                  <TableHead className="font-semibold">Days</TableHead>
                  <TableHead className="font-semibold">Type</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRequests.map((request) => (
                  <TableRow key={request.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div>
                        <div className="font-medium text-gray-900">{request.employeeName}</div>
                        <div className="text-sm text-gray-500">{request.employeeId}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{request.department}</Badge>
                    </TableCell>
                    <TableCell>
                      {getLeaveTypeBadge(request.leaveType)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{new Date(request.fromDate).toLocaleDateString()}</div>
                        <div className="text-gray-500">to {new Date(request.toDate).toLocaleDateString()}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{request.totalDays}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant={request.isPaid ? "default" : "secondary"}>
                        {request.isPaid ? "Paid" : "Unpaid"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(request.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Link href={`/admin/leave-management/requests/${request.id}`}>
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        {request.status === "pending" && (
                          <>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-green-600 hover:text-green-700">
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-red-600 hover:text-red-700">
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                onClick={() => setCurrentPage(page)}
                className="w-10"
              >
                {page}
              </Button>
            ))}
            
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}