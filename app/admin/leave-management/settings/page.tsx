"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Settings, Bell, Clock, Shield, Mail, AlertTriangle, Save, RotateCcw } from "lucide-react"
import { useState } from "react"

// Initial settings state
const initialSettings = {
  notifications: {
    emailOnApproval: true,
    emailOnRejection: true,
    managerReminder: true,
    managerReminderDays: 3,
    longLeaveAlert: true,
    longLeaveThreshold: 7,
    absenteeAlert: true,
    absenteeThreshold: 2
  },
  rules: {
    maxBackdatedDays: 7,
    minNoticeHours: 24,
    maxContinuousLeave: 30,
    autoRejectDays: 30,
    requireManagerApproval: true,
    requireHRApproval: false,
    allowWeekendLeave: true,
    allowHolidayLeave: false
  },
  automation: {
    autoApproveShortLeave: false,
    shortLeaveThreshold: 1,
    autoCalculateBalance: true,
    sendReminderEmails: true,
    escalateToHR: true,
    escalationDays: 5
  },
  security: {
    requireReasonForRejection: true,
    logAllActions: true,
    requireTwoFactorForEdits: false,
    sessionTimeout: 60,
    maxLoginAttempts: 5
  }
}

export default function LeaveSettingsPage() {
  const [settings, setSettings] = useState(initialSettings)
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    setSaving(true)
    // Simulate API call
    setTimeout(() => {
      setSaving(false)
      setHasChanges(false)
      console.log("Settings saved:", settings)
    }, 2000)
  }

  const handleReset = () => {
    setSettings(initialSettings)
    setHasChanges(false)
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-gray-900">Leave Management Settings</h1>
            <p className="text-gray-600 mt-1">Configure system behavior and automation rules</p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handleReset}
              disabled={!hasChanges}
              className="gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>
            <Button 
              onClick={handleSave}
              disabled={!hasChanges || saving}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>

        {/* Changes Alert */}
        {hasChanges && (
          <Alert className="border-blue-200 bg-blue-50">
            <AlertTriangle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              You have unsaved changes. Don't forget to save your settings.
            </AlertDescription>
          </Alert>
        )}

        {/* Settings Tabs */}
        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="notifications" className="gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="rules" className="gap-2">
              <Shield className="h-4 w-4" />
              Rules
            </TabsTrigger>
            <TabsTrigger value="automation" className="gap-2">
              <Settings className="h-4 w-4" />
              Automation
            </TabsTrigger>
            <TabsTrigger value="security" className="gap-2">
              <Clock className="h-4 w-4" />
              Security
            </TabsTrigger>
          </TabsList>

          {/* Notifications Settings */}
          <TabsContent value="notifications" className="space-y-6">
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Email Notifications
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Email Employee on Approval</Label>
                    <p className="text-sm text-gray-500">Send confirmation email when leave is approved</p>
                  </div>
                  <Switch
                    checked={settings.notifications.emailOnApproval}
                    onCheckedChange={(checked) => updateSetting("notifications", "emailOnApproval", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Email Employee on Rejection</Label>
                    <p className="text-sm text-gray-500">Send notification email when leave is rejected</p>
                  </div>
                  <Switch
                    checked={settings.notifications.emailOnRejection}
                    onCheckedChange={(checked) => updateSetting("notifications", "emailOnRejection", checked)}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Manager Reminder</Label>
                      <p className="text-sm text-gray-500">Remind managers about pending leave requests</p>
                    </div>
                    <Switch
                      checked={settings.notifications.managerReminder}
                      onCheckedChange={(checked) => updateSetting("notifications", "managerReminder", checked)}
                    />
                  </div>
                  {settings.notifications.managerReminder && (
                    <div className="ml-6 space-y-2">
                      <Label>Reminder after (days)</Label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[settings.notifications.managerReminderDays]}
                          onValueChange={(value) => updateSetting("notifications", "managerReminderDays", value[0])}
                          max={10}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="w-12 text-sm font-medium">{settings.notifications.managerReminderDays} days</span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Long Leave Alert</Label>
                      <p className="text-sm text-gray-500">Alert for extended leave requests</p>
                    </div>
                    <Switch
                      checked={settings.notifications.longLeaveAlert}
                      onCheckedChange={(checked) => updateSetting("notifications", "longLeaveAlert", checked)}
                    />
                  </div>
                  {settings.notifications.longLeaveAlert && (
                    <div className="ml-6 space-y-2">
                      <Label>Alert threshold (days)</Label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[settings.notifications.longLeaveThreshold]}
                          onValueChange={(value) => updateSetting("notifications", "longLeaveThreshold", value[0])}
                          max={30}
                          min={3}
                          step={1}
                          className="flex-1"
                        />
                        <span className="w-12 text-sm font-medium">{settings.notifications.longLeaveThreshold} days</span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Absentee Alert</Label>
                      <p className="text-sm text-gray-500">Alert for employees absent without leave</p>
                    </div>
                    <Switch
                      checked={settings.notifications.absenteeAlert}
                      onCheckedChange={(checked) => updateSetting("notifications", "absenteeAlert", checked)}
                    />
                  </div>
                  {settings.notifications.absenteeAlert && (
                    <div className="ml-6 space-y-2">
                      <Label>Alert after (days)</Label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[settings.notifications.absenteeThreshold]}
                          onValueChange={(value) => updateSetting("notifications", "absenteeThreshold", value[0])}
                          max={7}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="w-12 text-sm font-medium">{settings.notifications.absenteeThreshold} days</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rules Settings */}
          <TabsContent value="rules" className="space-y-6">
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Leave Application Rules
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Max Backdated Leave Application (days)</Label>
                    <Input
                      type="number"
                      value={settings.rules.maxBackdatedDays}
                      onChange={(e) => updateSetting("rules", "maxBackdatedDays", parseInt(e.target.value) || 0)}
                      min="0"
                      max="30"
                    />
                    <p className="text-xs text-gray-500">How far back employees can apply for leave</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Minimum Notice Period (hours)</Label>
                    <Input
                      type="number"
                      value={settings.rules.minNoticeHours}
                      onChange={(e) => updateSetting("rules", "minNoticeHours", parseInt(e.target.value) || 0)}
                      min="0"
                      max="168"
                    />
                    <p className="text-xs text-gray-500">Minimum advance notice required</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Max Continuous Leave (days)</Label>
                    <Input
                      type="number"
                      value={settings.rules.maxContinuousLeave}
                      onChange={(e) => updateSetting("rules", "maxContinuousLeave", parseInt(e.target.value) || 0)}
                      min="1"
                      max="365"
                    />
                    <p className="text-xs text-gray-500">Maximum consecutive leave days allowed</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Auto-reject Pending After (days)</Label>
                    <Input
                      type="number"
                      value={settings.rules.autoRejectDays}
                      onChange={(e) => updateSetting("rules", "autoRejectDays", parseInt(e.target.value) || 0)}
                      min="0"
                      max="90"
                    />
                    <p className="text-xs text-gray-500">Auto-reject if no action taken (0 = disabled)</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Require Manager Approval</Label>
                      <p className="text-sm text-gray-500">All leave requests must be approved by direct manager</p>
                    </div>
                    <Switch
                      checked={settings.rules.requireManagerApproval}
                      onCheckedChange={(checked) => updateSetting("rules", "requireManagerApproval", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Require HR Approval</Label>
                      <p className="text-sm text-gray-500">Additional HR approval required for all requests</p>
                    </div>
                    <Switch
                      checked={settings.rules.requireHRApproval}
                      onCheckedChange={(checked) => updateSetting("rules", "requireHRApproval", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Allow Weekend Leave</Label>
                      <p className="text-sm text-gray-500">Employees can apply for leave on weekends</p>
                    </div>
                    <Switch
                      checked={settings.rules.allowWeekendLeave}
                      onCheckedChange={(checked) => updateSetting("rules", "allowWeekendLeave", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Allow Holiday Leave</Label>
                      <p className="text-sm text-gray-500">Employees can apply for leave on public holidays</p>
                    </div>
                    <Switch
                      checked={settings.rules.allowHolidayLeave}
                      onCheckedChange={(checked) => updateSetting("rules", "allowHolidayLeave", checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Automation Settings */}
          <TabsContent value="automation" className="space-y-6">
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Automation & Workflows
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Auto-approve Short Leave</Label>
                      <p className="text-sm text-gray-500">Automatically approve leave requests below threshold</p>
                    </div>
                    <Switch
                      checked={settings.automation.autoApproveShortLeave}
                      onCheckedChange={(checked) => updateSetting("automation", "autoApproveShortLeave", checked)}
                    />
                  </div>
                  {settings.automation.autoApproveShortLeave && (
                    <div className="ml-6 space-y-2">
                      <Label>Auto-approve threshold (days)</Label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[settings.automation.shortLeaveThreshold]}
                          onValueChange={(value) => updateSetting("automation", "shortLeaveThreshold", value[0])}
                          max={5}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="w-12 text-sm font-medium">{settings.automation.shortLeaveThreshold} days</span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Auto-calculate Leave Balance</Label>
                    <p className="text-sm text-gray-500">Automatically update balances when leave is taken</p>
                  </div>
                  <Switch
                    checked={settings.automation.autoCalculateBalance}
                    onCheckedChange={(checked) => updateSetting("automation", "autoCalculateBalance", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Send Reminder Emails</Label>
                    <p className="text-sm text-gray-500">Automatically send reminder emails to managers</p>
                  </div>
                  <Switch
                    checked={settings.automation.sendReminderEmails}
                    onCheckedChange={(checked) => updateSetting("automation", "sendReminderEmails", checked)}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-base font-medium">Escalate to HR</Label>
                      <p className="text-sm text-gray-500">Escalate pending requests to HR after specified days</p>
                    </div>
                    <Switch
                      checked={settings.automation.escalateToHR}
                      onCheckedChange={(checked) => updateSetting("automation", "escalateToHR", checked)}
                    />
                  </div>
                  {settings.automation.escalateToHR && (
                    <div className="ml-6 space-y-2">
                      <Label>Escalation after (days)</Label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[settings.automation.escalationDays]}
                          onValueChange={(value) => updateSetting("automation", "escalationDays", value[0])}
                          max={14}
                          min={1}
                          step={1}
                          className="flex-1"
                        />
                        <span className="w-12 text-sm font-medium">{settings.automation.escalationDays} days</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Settings */}
          <TabsContent value="security" className="space-y-6">
            <Card className="rounded-2xl shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security & Compliance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Require Reason for Rejection</Label>
                    <p className="text-sm text-gray-500">Admins must provide reason when rejecting leave</p>
                  </div>
                  <Switch
                    checked={settings.security.requireReasonForRejection}
                    onCheckedChange={(checked) => updateSetting("security", "requireReasonForRejection", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Log All Actions</Label>
                    <p className="text-sm text-gray-500">Maintain detailed audit trail of all actions</p>
                  </div>
                  <Switch
                    checked={settings.security.logAllActions}
                    onCheckedChange={(checked) => updateSetting("security", "logAllActions", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-base font-medium">Two-Factor for Balance Edits</Label>
                    <p className="text-sm text-gray-500">Require 2FA for manual balance adjustments</p>
                  </div>
                  <Switch
                    checked={settings.security.requireTwoFactorForEdits}
                    onCheckedChange={(checked) => updateSetting("security", "requireTwoFactorForEdits", checked)}
                  />
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Session Timeout (minutes)</Label>
                    <Input
                      type="number"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => updateSetting("security", "sessionTimeout", parseInt(e.target.value) || 0)}
                      min="5"
                      max="480"
                    />
                    <p className="text-xs text-gray-500">Auto-logout after inactivity</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Max Login Attempts</Label>
                    <Input
                      type="number"
                      value={settings.security.maxLoginAttempts}
                      onChange={(e) => updateSetting("security", "maxLoginAttempts", parseInt(e.target.value) || 0)}
                      min="3"
                      max="10"
                    />
                    <p className="text-xs text-gray-500">Lock account after failed attempts</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}