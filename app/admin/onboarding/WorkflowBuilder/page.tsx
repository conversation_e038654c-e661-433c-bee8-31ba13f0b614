"use client";
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Settings, Plus, ArrowLeft, X, User, Clock, CheckCircle, Files } from 'lucide-react';

const WorkflowBuilder = () => {
  const [activeTab, setActiveTab] = useState('Workflow Builder');

  const tabs = ['Workflow Templates', 'Workflow Builder', 'Preview & Test'];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Workflow Templates':
        return <WorkflowTemplatesContent />;
      case 'Workflow Builder':
        return <WorkflowBuilderContent />;
      case 'Preview & Test':
        return <PreviewTestContent />;
      default:
        return <WorkflowBuilderContent />;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Workflow Builder</h1>
            <p className="text-muted-foreground">Design and customize onboarding workflows</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <Settings className="h-4 w-4" />
              Save Workflow
            </Button>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              New Workflow
            </Button>
          </div>
        </div>

        {/* Tabbed Navigation */}
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

// Workflow Templates Content
const WorkflowTemplatesContent = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-foreground">Existing Workflow Templates</h2>
        <p className="text-muted-foreground">Select a template to edit or create a new workflow from scratch</p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Standard Onboarding</CardTitle>
          <p className="text-muted-foreground">Default workflow for full-time employee</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="text-sm font-medium text-foreground">Category:</p>
              <p className="text-muted-foreground">Full-Time</p>
            </div>
            <div>
              <p className="text-sm font-medium text-foreground">Tasks:</p>
              <p className="text-muted-foreground">3</p>
            </div>
          </div>
          <div>
            <p className="text-sm font-medium text-foreground">Last Modified</p>
            <p className="text-muted-foreground">2024-01-20</p>
          </div>
          <div className="flex gap-3 pt-4">
            <Button variant="outline">Edit</Button>
            <Button>Clone</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Workflow Builder Content
const WorkflowBuilderContent = () => {
  const [showAddTaskDialog, setShowAddTaskDialog] = useState(false);
  
  return (
    <div className="space-y-6">
      {/* Workflow Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Settings</CardTitle>
          <p className="text-muted-foreground">Configure basic workflow information</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Workflow Name</label>
              <Input placeholder="Enter workflow name" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Category</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full-time">Full-Time</SelectItem>
                  <SelectItem value="contractor">Contractor</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Description</label>
            <Textarea 
              placeholder="Describe the purpose and scope of this workflow"
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Workflow Tasks */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Workflow Tasks</CardTitle>
            <p className="text-muted-foreground">Add and configure tasks for your onboarding workflow</p>
          </div>
          <Button 
            className="gap-2"
            onClick={() => setShowAddTaskDialog(true)}
          >
            <Plus className="h-4 w-4" />
            Add Task
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Settings className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No tasks added yet</h3>
            <p className="text-muted-foreground mb-6">Start building your workflow by adding tasks</p>
            <Button 
              className="gap-2"
              onClick={() => setShowAddTaskDialog(true)}
            >
              <Plus className="h-4 w-4" />
              Add First Task
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Add New Task Dialog */}
      <AddNewTaskDialog 
        open={showAddTaskDialog} 
        onOpenChange={setShowAddTaskDialog}
      />
    </div>
  );
};

// Preview & Test Content
const PreviewTestContent = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Workflow Preview</CardTitle>
          <p className="text-muted-foreground">Review your workflow before saving and activation</p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <span className="text-2xl text-muted-foreground">!</span>
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">No workflow to preview</h3>
            <p className="text-muted-foreground mb-6">Create or load a workflow to see the preview</p>
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Go to Builder
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Add New Task Dialog Component
interface AddNewTaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AddNewTaskDialog: React.FC<AddNewTaskDialogProps> = ({ open, onOpenChange }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    taskName: '',
    taskType: '',
    description: '',
    priorityLevel: 'medium',
    duration: 1,
    durationUnit: 'days',
    requiresApproval: false,
    saveAsTemplate: false,
    assignee: '',
    approver: '',
    detailedInstructions: '',
    makeConditional: false,
    resources: [] as string[],
    tags: [] as string[],
  });

  const steps = [
    { id: 'basic-info', title: 'Basic Info', icon: User },
    { id: 'assignment', title: 'Assignment', icon: Settings },
    { id: 'conditions', title: 'Conditions', icon: CheckCircle },
    { id: 'resources', title: 'Resources', icon: Files }
  ];

  const taskTypes = [
    { value: 'document-submission', label: 'Document Submission' },
    { value: 'equipment-setup', label: 'Equipment Setup' },
    { value: 'training', label: 'Training' },
    { value: 'meeting', label: 'Meeting' },
    { value: 'review', label: 'Review' },
    { value: 'other', label: 'Other' }
  ];

  const priorityLevels = [
    { value: 'low', label: 'Low', color: 'text-green-600' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'critical', label: 'Critical', color: 'text-red-600' }
  ];

  const assignees = [
    { value: 'hr-team', label: 'HR Team' },
    { value: 'it-team', label: 'IT Team' },
    { value: 'manager', label: 'Direct Manager' },
    { value: 'new-employee', label: 'New Employee' },
    { value: 'admin', label: 'Admin' }
  ];

  const approvers = [
    { value: 'john-manager', label: 'John Smith (Manager)' },
    { value: 'sarah-hr', label: 'Sarah Johnson (HR Director)' },
    { value: 'mike-it', label: 'Mike Chen (IT Manager)' },
    { value: 'lisa-admin', label: 'Lisa Brown (Admin)' }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    console.log('Task created:', formData);
    // Reset form and close dialog
    setFormData({
      taskName: '',
      taskType: '',
      description: '',
      priorityLevel: 'medium',
      duration: 1,
      durationUnit: 'days',
      requiresApproval: false,
      saveAsTemplate: false,
      assignee: '',
      approver: '',
      detailedInstructions: '',
      makeConditional: false,
      resources: [],
      tags: [],
    });
    setCurrentStep(0);
    onOpenChange(false);
  };

  const canContinue = () => {
    switch (currentStep) {
      case 0: // Basic Info
        return formData.taskName && formData.taskType;
      case 1: // Assignment
        return formData.assignee && (!formData.requiresApproval || formData.approver);
      case 2: // Conditions
        return true;
      case 3: // Resources
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Basic Info
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Task Basic Information</h3>
              <p className="text-sm text-muted-foreground">Define the core details of your onboarding task</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="taskName">Task Name *</Label>
                <Input
                  id="taskName"
                  placeholder="e.g. Complete I-9 Form"
                  value={formData.taskName}
                  onChange={(e) => handleInputChange('taskName', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="taskType">Task Type *</Label>
                <Select value={formData.taskType} onValueChange={(value) => handleInputChange('taskType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select task type" />
                  </SelectTrigger>
                  <SelectContent>
                    {taskTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what needs to be done in this task..."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="min-h-[80px]"
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priorityLevel">Priority Level</Label>
                  <Select value={formData.priorityLevel} onValueChange={(value) => handleInputChange('priorityLevel', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityLevels.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          <span className={priority.color}>{priority.label}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">Duration</Label>
                  <Input
                    id="duration"
                    type="number"
                    min="1"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 1)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="durationUnit">Unit</Label>
                  <Select value={formData.durationUnit} onValueChange={(value) => handleInputChange('durationUnit', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hours">Hours</SelectItem>
                      <SelectItem value="days">Days</SelectItem>
                      <SelectItem value="weeks">Weeks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="requiresApproval"
                    checked={formData.requiresApproval}
                    onCheckedChange={(checked) => handleInputChange('requiresApproval', checked)}
                  />
                  <Label htmlFor="requiresApproval" className="text-sm font-normal">
                    Requires Approval
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="saveAsTemplate"
                    checked={formData.saveAsTemplate}
                    onCheckedChange={(checked) => handleInputChange('saveAsTemplate', checked)}
                  />
                  <Label htmlFor="saveAsTemplate" className="text-sm font-normal">
                    Save as Template
                  </Label>
                </div>
              </div>
            </div>
          </div>
        );

      case 1: // Assignment
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Task Assignment</h3>
              <p className="text-sm text-muted-foreground">Configure who is responsible for completing this task</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="assignee">Assignee *</Label>
                <Select value={formData.assignee} onValueChange={(value) => handleInputChange('assignee', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select who will complete this task" />
                  </SelectTrigger>
                  <SelectContent>
                    {assignees.map((assignee) => (
                      <SelectItem key={assignee.value} value={assignee.value}>
                        {assignee.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {formData.requiresApproval && (
                <div className="space-y-2">
                  <Label htmlFor="approver">Approver *</Label>
                  <Select value={formData.approver} onValueChange={(value) => handleInputChange('approver', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select who will approve this task" />
                    </SelectTrigger>
                    <SelectContent>
                      {approvers.map((approver) => (
                        <SelectItem key={approver.value} value={approver.value}>
                          {approver.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="detailedInstructions">Detailed Instructions</Label>
                <Textarea
                  id="detailedInstructions"
                  placeholder="Provide step-by-step instructions to complete this task..."
                  value={formData.detailedInstructions}
                  onChange={(e) => handleInputChange('detailedInstructions', e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          </div>
        );

      case 2: // Conditions
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Task Conditions</h3>
              <p className="text-sm text-muted-foreground">Configure when and how this task should be executed</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="makeConditional"
                  checked={formData.makeConditional}
                  onCheckedChange={(checked) => handleInputChange('makeConditional', checked)}
                />
                <Label htmlFor="makeConditional" className="text-sm font-normal">
                  Make this task conditional
                </Label>
              </div>
              
              {formData.makeConditional && (
                <div className="ml-6 p-4 border rounded-lg bg-muted/50">
                  <p className="text-sm text-muted-foreground">
                    Conditional logic will be configured in future updates.
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      case 3: // Resources
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Resources & Tags</h3>
              <p className="text-sm text-muted-foreground">Add helpful resources and organize with tags</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Resources</Label>
                <p className="text-sm text-muted-foreground">Add links, documents, or tools needed for this task</p>
                <Input placeholder="Enter resource URL or description" />
                <Button variant="outline" size="sm" className="mt-2">Add Resource</Button>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <p className="text-sm text-muted-foreground">Add tags to organize and categorize tasks</p>
                <Input placeholder="Enter tag name" />
                <Button variant="outline" size="sm" className="mt-2">Add Tag</Button>
              </div>

              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Task Preview</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Task:</strong> {formData.taskName || 'Untitled Task'}</p>
                  <p><strong>Type:</strong> {taskTypes.find(t => t.value === formData.taskType)?.label || 'Not selected'}</p>
                  <p><strong>Assignee:</strong> {assignees.find(a => a.value === formData.assignee)?.label || 'Not assigned'}</p>
                  <p><strong>Priority:</strong> {priorityLevels.find(p => p.value === formData.priorityLevel)?.label}</p>
                  <p><strong>Duration:</strong> {formData.duration} {formData.durationUnit}</p>
                  {formData.requiresApproval && (
                    <p><strong>Approver:</strong> {approvers.find(a => a.value === formData.approver)?.label || 'Not selected'}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Task
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Create a new task for your onboarding workflow
          </p>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors
                ${index <= currentStep 
                  ? 'border-primary bg-primary text-primary-foreground' 
                  : 'border-muted-foreground bg-background'
                }
              `}>
                <step.icon className="h-4 w-4" />
              </div>
              <div className="ml-2 text-sm">
                <p className={`font-medium ${index <= currentStep ? 'text-foreground' : 'text-muted-foreground'}`}>
                  {step.title}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  w-8 h-0.5 mx-4 transition-colors
                  ${index < currentStep ? 'bg-primary' : 'bg-muted'}
                `} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            Previous
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleSubmit}
                className="gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Create Task
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canContinue()}
                className="gap-2"
              >
                Continue to {steps[currentStep + 1]?.title}
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WorkflowBuilder;