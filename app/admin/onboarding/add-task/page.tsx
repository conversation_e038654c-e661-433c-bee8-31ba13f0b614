"use client";
import React, { useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, X, FileText, User, Settings, Files, Plus, Eye } from 'lucide-react';

const AddTaskPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic-info');
  const [formData, setFormData] = useState({
    taskName: '',
    taskType: '',
    description: '',
    priorityLevel: 'medium',
    duration: 1,
    durationUnit: 'days',
    requiresApproval: false,
    saveAsTemplate: false,
    templateName: '',
    assignee: '',
    approver: '',
    detailedInstructions: '',
    makeConditional: false,
    conditionType: '',
    conditionValue: '',
    resources: [] as string[],
    tags: [] as string[],
  });

  const [newResource, setNewResource] = useState('');
  const [newTag, setNewTag] = useState('');

  const tabs = [
    { id: 'basic-info', label: 'Basic Info', icon: FileText },
    { id: 'assignment', label: 'Assignment', icon: User },
    { id: 'conditions', label: 'Conditions', icon: Settings },
    { id: 'resources', label: 'Resources', icon: Files }
  ];

  const taskTypes = [
    { value: 'document-submission', label: 'Document Submission' },
    { value: 'equipment-setup', label: 'Equipment Setup' },
    { value: 'training', label: 'Training' },
    { value: 'meeting', label: 'Meeting' },
    { value: 'review', label: 'Review' },
    { value: 'other', label: 'Other' }
  ];

  const priorityLevels = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  const assignees = [
    { value: 'hr-team', label: 'HR Team' },
    { value: 'it-team', label: 'IT Team' },
    { value: 'manager', label: 'Direct Manager' },
    { value: 'new-employee', label: 'New Employee' },
    { value: 'admin', label: 'Admin' }
  ];

  const approvers = [
    { value: 'john-manager', label: 'John Smith (Manager)' },
    { value: 'sarah-hr', label: 'Sarah Johnson (HR Director)' },
    { value: 'mike-it', label: 'Mike Chen (IT Manager)' },
    { value: 'lisa-admin', label: 'Lisa Brown (Admin)' }
  ];

  const conditionTypes = [
    { value: 'role', label: 'Role', description: 'Based on employee role/position', examples: ['Manager', 'Developer', 'Designer', 'Analyst'] },
    { value: 'department', label: 'Department', description: 'Based on department/team', examples: ['Engineering', 'Marketing', 'Sales', 'HR'] },
    { value: 'location', label: 'Location', description: 'Based on work location', examples: ['Remote', 'New York', 'San Francisco', 'London'] },
    { value: 'employment-type', label: 'Employment Type', description: 'Based on employment status', examples: ['Full-Time', 'Part-Time', 'Contract', 'Intern'] }
  ];

  const handleInputChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSubmit = useCallback(() => {
    try {
      console.log('Task created:', formData);
      router.push('/admin/onboarding');
    } catch (error) {
      console.error('Error saving task:', error);
    }
  }, [formData, router]);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  const canContinueToAssignment = useMemo(() => {
    return formData.taskName && formData.taskType;
  }, [formData.taskName, formData.taskType]);

  const canCreateTask = useMemo(() => {
    return formData.taskName && formData.taskType && formData.assignee && 
           (!formData.requiresApproval || formData.approver);
  }, [formData.taskName, formData.taskType, formData.assignee, formData.requiresApproval, formData.approver]);

  const handleContinueToAssignment = () => {
    if (canContinueToAssignment) {
      setActiveTab('assignment');
    }
  };

  const addResource = () => {
    if (newResource.trim()) {
      setFormData(prev => ({
        ...prev,
        resources: [...prev.resources, newResource.trim()]
      }));
      setNewResource('');
    }
  };

  const addTag = () => {
    if (newTag.trim()) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeResource = (index: number) => {
    setFormData(prev => ({
      ...prev,
      resources: prev.resources.filter((_, i) => i !== index)
    }));
  };

  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }));
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic-info':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Task Basic Information</CardTitle>
              <p className="text-muted-foreground">Define the core details of your onboarding task</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="taskName">Task Name *</Label>
                  <Input
                    id="taskName"
                    placeholder="e.g. Complete I-9 Form"
                    value={formData.taskName}
                    onChange={(e) => handleInputChange('taskName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taskType">Task Type *</Label>
                  <Select value={formData.taskType} onValueChange={(value) => handleInputChange('taskType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select task type" />
                    </SelectTrigger>
                    <SelectContent>
                      {taskTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what needs to be done in this task..."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priorityLevel">Priority Level</Label>
                  <Select value={formData.priorityLevel} onValueChange={(value) => handleInputChange('priorityLevel', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityLevels.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration</Label>
                  <Input
                    id="duration"
                    type="number"
                    min="1"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="durationUnit">Unit</Label>
                  <Select value={formData.durationUnit} onValueChange={(value) => handleInputChange('durationUnit', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hours">Hours</SelectItem>
                      <SelectItem value="days">Days</SelectItem>
                      <SelectItem value="weeks">Weeks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="requiresApproval"
                    checked={formData.requiresApproval}
                    onCheckedChange={(checked) => handleInputChange('requiresApproval', checked)}
                  />
                  <Label htmlFor="requiresApproval" className="text-sm font-normal">
                    Requires Approval
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="saveAsTemplate"
                    checked={formData.saveAsTemplate}
                    onCheckedChange={(checked) => handleInputChange('saveAsTemplate', checked)}
                  />
                  <Label htmlFor="saveAsTemplate" className="text-sm font-normal">
                    Save as Template
                  </Label>
                </div>
                {formData.saveAsTemplate && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="templateName">Template Name</Label>
                    <Input
                      id="templateName"
                      placeholder="Enter template name"
                      value={formData.templateName}
                      onChange={(e) => handleInputChange('templateName', e.target.value)}
                    />
                  </div>
                )}
              </div>

              <div className="flex justify-end pt-4">
                <Button 
                  onClick={handleContinueToAssignment}
                  disabled={!canContinueToAssignment}
                  className="gap-2"
                >
                  Continue to Assignment
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'assignment':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Task Assignment</CardTitle>
              <p className="text-muted-foreground">Assign the task to a specific person and provide instructions</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="assignee">Assignee *</Label>
                  <Select value={formData.assignee} onValueChange={(value) => handleInputChange('assignee', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select who will complete this task" />
                    </SelectTrigger>
                    <SelectContent>
                      {assignees.map((assignee) => (
                        <SelectItem key={assignee.value} value={assignee.value}>
                          {assignee.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {formData.requiresApproval && (
                  <div className="space-y-2">
                    <Label htmlFor="approver">Approver *</Label>
                    <Select value={formData.approver} onValueChange={(value) => handleInputChange('approver', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select who will approve this task" />
                      </SelectTrigger>
                      <SelectContent>
                        {approvers.map((approver) => (
                          <SelectItem key={approver.value} value={approver.value}>
                            {approver.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="detailedInstructions">Detailed Instructions</Label>
                <Textarea
                  id="detailedInstructions"
                  placeholder="Provide step-by-step instructions to complete this task..."
                  value={formData.detailedInstructions}
                  onChange={(e) => handleInputChange('detailedInstructions', e.target.value)}
                  className="min-h-[120px]"
                />
              </div>

              <div className="flex justify-between pt-6 border-t">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmit}
                  disabled={!canCreateTask}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  Create Task
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'conditions':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Task Conditions</CardTitle>
              <p className="text-muted-foreground">Configure when and how this task should be displayed or assigned</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="makeConditional"
                  checked={formData.makeConditional}
                  onCheckedChange={(checked) => {
                    handleInputChange('makeConditional', checked);
                    // Reset condition fields when unchecked
                    if (!checked) {
                      handleInputChange('conditionType', '');
                      handleInputChange('conditionValue', '');
                    }
                  }}
                />
                <Label htmlFor="makeConditional" className="text-sm font-normal">
                  Make this task conditional
                </Label>
              </div>
              
              {formData.makeConditional && (
                <div className="ml-6 p-6 border rounded-lg bg-muted/50 space-y-6">
                  <div>
                    <h4 className="font-medium mb-2">Conditional Logic</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Set conditions that determine when this task should appear for users. This allows you to tailor onboarding tasks based on specific employee attributes.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="conditionType">Condition Type</Label>
                      <Select 
                        value={formData.conditionType} 
                        onValueChange={(value) => {
                          handleInputChange('conditionType', value);
                          // Reset condition value when type changes
                          handleInputChange('conditionValue', '');
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select criteria for condition" />
                        </SelectTrigger>
                        <SelectContent>
                          {conditionTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex flex-col">
                                <span className="font-medium">{type.label}</span>
                                <span className="text-xs text-muted-foreground">{type.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="conditionValue">Condition Value</Label>
                      <Input
                        id="conditionValue"
                        placeholder={
                          formData.conditionType 
                            ? `e.g. ${conditionTypes.find(t => t.value === formData.conditionType)?.examples.join(', ')}`
                            : "Enter trigger value"
                        }
                        value={formData.conditionValue}
                        onChange={(e) => handleInputChange('conditionValue', e.target.value)}
                        disabled={!formData.conditionType}
                      />
                    </div>
                  </div>

                  {formData.conditionType && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h5 className="font-medium text-blue-900 mb-2">
                        📌 How this works:
                      </h5>
                      <p className="text-sm text-blue-800">
                        This task will only appear for users whose{' '}
                        <span className="font-medium">
                          {conditionTypes.find(t => t.value === formData.conditionType)?.label.toLowerCase()}
                        </span>
                        {formData.conditionValue && (
                          <>
                            {' '}matches "<span className="font-medium">{formData.conditionValue}</span>"
                          </>
                        )}
                        . For example:
                      </p>
                      <ul className="text-sm text-blue-700 mt-2 ml-4 space-y-1">
                        {formData.conditionType === 'role' && (
                          <li>• Only assign security training to managers</li>
                        )}
                        {formData.conditionType === 'department' && (
                          <li>• Only show engineering setup tasks to engineering team</li>
                        )}
                        {formData.conditionType === 'location' && (
                          <li>• Only assign office tour to on-site employees</li>
                        )}
                        {formData.conditionType === 'employment-type' && (
                          <li>• Only show benefits enrollment to full-time employees</li>
                        )}
                      </ul>
                    </div>
                  )}

                  {formData.conditionType && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Example Values for {conditionTypes.find(t => t.value === formData.conditionType)?.label}:</Label>
                      <div className="flex flex-wrap gap-2">
                        {conditionTypes.find(t => t.value === formData.conditionType)?.examples.map((example, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => handleInputChange('conditionValue', example)}
                            className="px-3 py-1 text-xs bg-white border border-border rounded-full hover:bg-accent transition-colors"
                          >
                            {example}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-between pt-6 border-t">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmit}
                  disabled={!canCreateTask}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  Create Task
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'resources':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Resources & Tags Section */}
            <Card>
              <CardHeader>
                <CardTitle>Resources & Tags</CardTitle>
                <p className="text-muted-foreground">Add helpful resources and organize with tags</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Resources */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Resources</Label>
                    <p className="text-xs text-muted-foreground mb-3">
                      Add resource URLs, documents, or tool descriptions needed to complete the onboarding task
                    </p>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Enter resource URL or description"
                        value={newResource}
                        onChange={(e) => setNewResource(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addResource()}
                        className="flex-1"
                      />
                      <Button 
                        onClick={addResource}
                        disabled={!newResource.trim()}
                        size="sm"
                        className="gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        Add
                      </Button>
                    </div>
                    {formData.resources.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {formData.resources.map((resource, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <span className="text-sm">{resource}</span>
                            <Button
                              onClick={() => removeResource(index)}
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  <div>
                    <Label className="text-sm font-medium">Tags</Label>
                    <p className="text-xs text-muted-foreground mb-3">
                      Add tags to organize and categorize tasks (e.g., by department, priority)
                    </p>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Enter tag name"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                        className="flex-1"
                      />
                      <Button 
                        onClick={addTag}
                        disabled={!newTag.trim()}
                        size="sm"
                        className="gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        Add
                      </Button>
                    </div>
                    {formData.tags.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {formData.tags.map((tag, index) => (
                          <div key={index} className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">
                            <span>{tag}</span>
                            <Button
                              onClick={() => removeTag(index)}
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-primary/20"
                            >
                              <X className="h-2 w-2" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button 
                    onClick={handleSubmit}
                    disabled={!canCreateTask}
                    className="gap-2"
                  >
                    <Save className="h-4 w-4" />
                    Create Task
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Task Preview Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Task Preview
                </CardTitle>
                <p className="text-muted-foreground">Review how the task will look once created</p>
              </CardHeader>
              <CardContent>
                {!canCreateTask ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                      <X className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium text-foreground mb-2">Incomplete Task</h3>
                    <p className="text-muted-foreground text-sm">
                      Complete the required fields to see the task preview
                    </p>
                    <div className="mt-4 text-left">
                      <p className="text-xs text-muted-foreground mb-2">Missing required fields:</p>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {!formData.taskName && <li>• Task Name</li>}
                        {!formData.taskType && <li>• Task Type</li>}
                        {!formData.assignee && <li>• Assignee</li>}
                        {formData.requiresApproval && !formData.approver && <li>• Approver</li>}
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Task Name</Label>
                      <p className="text-sm text-muted-foreground">{formData.taskName}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Type</Label>
                      <p className="text-sm text-muted-foreground">
                        {taskTypes.find(t => t.value === formData.taskType)?.label}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Priority</Label>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-600">
                        {priorityLevels.find(p => p.value === formData.priorityLevel)?.label}
                      </span>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Assignee</Label>
                      <p className="text-sm text-muted-foreground">
                        {assignees.find(a => a.value === formData.assignee)?.label}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Duration</Label>
                      <p className="text-sm text-muted-foreground">{formData.duration} {formData.durationUnit}</p>
                    </div>
                    {formData.requiresApproval && formData.approver && (
                      <div>
                        <Label className="text-sm font-medium">Approver</Label>
                        <p className="text-sm text-muted-foreground">
                          {approvers.find(a => a.value === formData.approver)?.label}
                        </p>
                      </div>
                    )}
                    {formData.description && (
                      <div>
                        <Label className="text-sm font-medium">Description</Label>
                        <p className="text-sm text-muted-foreground">{formData.description}</p>
                      </div>
                    )}
                    {formData.makeConditional && formData.conditionType && formData.conditionValue && (
                      <div>
                        <Label className="text-sm font-medium">Conditional Logic</Label>
                        <div className="text-sm text-muted-foreground">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600">
                            {conditionTypes.find(t => t.value === formData.conditionType)?.label}: {formData.conditionValue}
                          </span>
                        </div>
                      </div>
                    )}
                    {formData.resources.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium">Resources</Label>
                        <div className="text-sm text-muted-foreground space-y-1">
                          {formData.resources.map((resource, index) => (
                            <div key={index}>• {resource}</div>
                          ))}
                        </div>
                      </div>
                    )}
                    {formData.tags.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium">Tags</Label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {formData.tags.map((tag, index) => (
                            <span key={index} className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Workflow Builder
            </Button>
          </div>
          <h1 className="text-2xl font-semibold text-foreground">Add New Task</h1>
          <p className="text-muted-foreground">Create a new task for your onboarding workflow</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="gap-2">
            <Save className="h-4 w-4" />
            Save Workflow
          </Button>
          <Button className="gap-2">
            <X className="h-4 w-4" />
            New Workflow
          </Button>
        </div>
      </div>

      {/* Tabbed Navigation */}
      <div className="border-b border-border mb-6">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="w-full">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default AddTaskPage;