"use client";
import OnboardingDashboard from "@/components/admin/onboarding/OnboardingDashboard";
import WorkflowBuilder from "@/components/admin/onboarding/WorkflowBuilder";
import StartOnboarding from "@/components/admin/onboarding/StartOnboarding";
import { useState } from "react";

const Index = () => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'workflow-builder' | 'start-onboarding'>('dashboard');

  if (currentView === 'workflow-builder') {
    return <WorkflowBuilder />;
  }

  if (currentView === 'start-onboarding') {
    return <StartOnboarding />;
  }

  return (
    <div className="">
      <OnboardingDashboard 
        onNavigateToWorkflowBuilder={() => setCurrentView('workflow-builder')}
        onNavigateToStartOnboarding={() => setCurrentView('start-onboarding')}
      />
    </div>
  );
};

export default Index;