"use client";
import { useState } from "react";
import { ChevronLeft, ChevronRight, Play, X, Calendar, User, Settings, CheckCircle } from "lucide-react";

const StartOnboarding = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);

  const steps = [
    { id: 'employee', title: 'Select Employee', icon: User },
    { id: 'workflow', title: 'Choose Workflow', icon: Settings },
    { id: 'settings', title: 'Configure Settings', icon: Calendar },
    { id: 'review', title: 'Review & Start', icon: CheckCircle }
  ];

  const employees = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      empId: 'EMP123461',
      department: 'Engineering',
      role: 'Software Engineer',
      hireDate: '2024-01-15',
      status: 'Pending Onboarding'
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<PERSON><PERSON>@company.com',
      empId: 'EMP123462',
      department: 'Marketing',
      role: 'Marketing Manager',
      hireDate: '2024-01-20',
      status: 'Pending Onboarding'
    },
    {
      id: '3',
      name: 'David Kim',
      email: '<EMAIL>',
      empId: 'EMP123463',
      department: 'Sales',
      role: 'Sales Representative',
      hireDate: '2024-01-25',
      status: 'Pending Onboarding'
    }
  ];

  const workflows = [
    {
      id: 'standard',
      title: 'Standard Onboarding',
      description: 'Default workflow for full-time employees',
      category: 'Full-Time',
      tasks: 8,
      duration: '5 days',
      recommended: true
    },
    {
      id: 'contractor',
      title: 'Contractor Onboarding',
      description: 'Simplified workflow for contractors',
      category: 'Contract',
      tasks: 5,
      duration: '2 days',
      recommended: false
    },
    {
      id: 'executive',
      title: 'Executive Onboarding',
      description: 'Comprehensive workflow for leadership',
      category: 'Executive',
      tasks: 12,
      duration: '10 days',
      recommended: false
    },
    {
      id: 'intern',
      title: 'Intern Onboarding',
      description: 'Tailored workflow for interns and students',
      category: 'Intern',
      tasks: 6,
      duration: '3 days',
      recommended: false
    }
  ];

  const canProceed = () => {
    switch (currentStep) {
      case 0: return selectedEmployee !== null;
      case 1: return selectedWorkflow !== null;
      case 2: return true; // Settings step
      case 3: return true; // Review step
      default: return false;
    }
  };

  const isComplete = () => {
    return selectedEmployee && selectedWorkflow;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-foreground">Select Employee for Onboarding</h2>
              <p className="text-muted-foreground mt-1">Choose the employee who will go through the onboarding process</p>
            </div>
            
            <div className="relative">
              <input
                type="text"
                placeholder="Search by name, email, or employee ID..."
                className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {employees.map((employee) => (
                <div
                  key={employee.id}
                  onClick={() => setSelectedEmployee(employee.id)}
                  className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedEmployee === employee.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border bg-card hover:border-primary/50'
                  }`}
                >
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-medium text-foreground">{employee.name}</h3>
                      <p className="text-sm text-muted-foreground">{employee.email}</p>
                      <p className="text-sm text-muted-foreground">{employee.empId}</p>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center text-sm">
                        <span className="text-muted-foreground">•</span>
                        <span className="ml-2 text-foreground">{employee.department}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-muted-foreground">•</span>
                        <span className="ml-2 text-foreground">{employee.role}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-muted-foreground">•</span>
                        <span className="ml-2 text-foreground">Hire Date: {employee.hireDate}</span>
                      </div>
                    </div>

                    <div className="pt-2">
                      <span className="inline-block px-3 py-1 text-xs font-medium bg-warning/10 text-warning rounded-full">
                        {employee.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-foreground">Choose Onboarding Workflow</h2>
              <p className="text-muted-foreground mt-1">Select the appropriate workflow template for this employee</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {workflows.map((workflow) => (
                <div
                  key={workflow.id}
                  onClick={() => setSelectedWorkflow(workflow.id)}
                  className={`p-6 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedWorkflow === workflow.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border bg-card hover:border-primary/50'
                  }`}
                >
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-medium text-foreground">{workflow.title}</h3>
                        <p className="text-sm text-muted-foreground mt-1">{workflow.description}</p>
                      </div>
                      {workflow.recommended && (
                        <span className="px-2 py-1 text-xs font-medium bg-success/10 text-success rounded">
                          Recommended
                        </span>
                      )}
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Category:</span>
                        <span className="text-sm font-medium text-foreground">{workflow.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Tasks:</span>
                        <span className="text-sm font-medium text-foreground">{workflow.tasks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Duration:</span>
                        <span className="text-sm font-medium text-foreground">{workflow.duration}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-foreground">Configure Onboarding Settings</h2>
              <p className="text-muted-foreground mt-1">Set up timing, assignments, and additional preferences</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Start Date <span className="text-destructive">*</span>
                </label>
                <input
                  type="text"
                  placeholder="dd-mm-yyyy"
                  className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Priority Level</label>
                <select className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring">
                  <option>Medium Priority</option>
                  <option>High Priority</option>
                  <option>Low Priority</option>
                </select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium text-foreground">
                  Assign Onboarding Manager (Optional)
                </label>
                <select className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring">
                  <option>Select manager</option>
                  <option>Sarah Johnson</option>
                  <option>David Taylor</option>
                  <option>Lisa Wilson</option>
                </select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium text-foreground">Additional Notes</label>
                <textarea
                  rows={4}
                  placeholder="Add any special instructions or notes for this onboarding process..."
                  className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring resize-none"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-foreground">Review Onboarding Configuration</h2>
              <p className="text-muted-foreground mt-1">Verify all details before starting the onboarding process</p>
            </div>

            {isComplete() ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-success" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Configuration Complete</h3>
                <p className="text-muted-foreground mb-6">Ready to start the onboarding process</p>
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
                  <Play className="w-4 h-4 inline-block mr-2" />
                  Start Onboarding
                </button>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <X className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Incomplete Configuration</h3>
                <p className="text-muted-foreground mb-4">Please complete all required fields in the previous tabs</p>
                <ul className="text-sm text-muted-foreground space-y-1 mb-6">
                  {!selectedEmployee && <li>• Select an employee</li>}
                  {!selectedWorkflow && <li>• Choose a workflow</li>}
                  <li>• Set a start date</li>
                </ul>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b border-border px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Start New Onboarding</h1>
            <p className="text-muted-foreground">Initialize onboarding process for new employees</p>
          </div>
          <div className="flex gap-3">
            <button className="px-4 py-2 border border-input rounded-lg text-foreground hover:bg-accent transition-colors">
              Cancel
            </button>
            <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
              <Play className="w-4 h-4 inline-block mr-2" />
              Start Onboarding
            </button>
          </div>
        </div>
      </div>

      {/* Stepper */}
      <div className="bg-card border-b border-border px-6 py-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {steps.map((step, index) => {
            const StepIcon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            const isAccessible = index <= currentStep;

            return (
              <div key={step.id} className="flex items-center">
                <button
                  onClick={() => isAccessible && setCurrentStep(index)}
                  disabled={!isAccessible}
                  className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : isCompleted
                      ? 'bg-success/10 text-success hover:bg-success/20'
                      : isAccessible
                      ? 'text-foreground hover:bg-accent'
                      : 'text-muted-foreground cursor-not-allowed'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    isActive
                      ? 'bg-primary-foreground text-primary'
                      : isCompleted
                      ? 'bg-success text-success-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    <StepIcon className="w-4 h-4" />
                  </div>
                  <span className="font-medium">{step.title}</span>
                </button>

                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    index < currentStep ? 'bg-success' : 'bg-border'
                  }`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-8">
        <div className="max-w-6xl mx-auto">
          {renderStepContent()}
        </div>
      </div>

      {/* Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border px-6 py-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <button
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
            className="flex items-center gap-2 px-4 py-2 text-foreground hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </button>

          <div className="flex items-center gap-2">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep
                    ? 'bg-primary'
                    : index < currentStep
                    ? 'bg-success'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>

          <button
            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
            disabled={currentStep === steps.length - 1 || !canProceed()}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StartOnboarding;