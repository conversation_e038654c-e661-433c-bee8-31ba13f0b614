"use client"

import { motion } from "framer-motion"
import { AdminLayout } from "@/components/admin/AdminLayout"
import {
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveDemo,
  OverviewStatsGrid,
  EmployeeAttendanceGrid,
  CabManagementGrid,
  FinancialAlertsGrid,
  EmployeeBirthdayCard,
  TicketStatusCard,
  AnniversaryCard,
  ClaimsCard,
  MedicalAssistanceCard,
  RoleManagementTable,
  EmployeeHighlightCard
} from "@/components/admin"

export default function AdminDashboard() {
  return (
    <AdminLayout>
      <div className="space-y-8 w-full max-w-none">
        {/* Overview Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="w-full"
        >
          <h2 className="font-poppins font-bold text-xl text-gray-900 mb-6 ml-3">
            Overview
          </h2>
          <OverviewStatsGrid />
        </motion.section>

        {/* Employee Attendance Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="w-full"
        >
          <EmployeeAttendanceGrid />
        </motion.section>

        {/* Financial Alerts Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="w-full"
        >
          <FinancialAlertsGrid />
        </motion.section>

        {/* Employee Cards & Claims Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="w-full"
        >
          <h2 className="font-poppins font-bold text-xl text-gray-900 my-6 text-center">
            Employee Highlights
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <EmployeeHighlightCard delay={0}>
              <EmployeeBirthdayCard />
            </EmployeeHighlightCard>

            <EmployeeHighlightCard delay={0.2}>
              <AnniversaryCard />
            </EmployeeHighlightCard>

            <EmployeeHighlightCard delay={0.1}>
              <TicketStatusCard />
            </EmployeeHighlightCard>

            <EmployeeHighlightCard delay={0.3}>
              <ClaimsCard />
            </EmployeeHighlightCard>
          </div>
        </motion.section>

        {/* Medical Assistance & Role Management Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="w-full"
        >
          <h2 className="font-poppins font-bold text-xl text-gray-900 mb-6 text-center mt-4">
            Management & Operations
          </h2>
          <ResponsiveGrid
            cols={{ mobile: 1, tablet: 1, desktop: 3, wide: 3 }}
            gap="gap-4 lg:gap-6"
          >
            <ResponsiveCard>
              <MedicalAssistanceCard />
            </ResponsiveCard>
            <ResponsiveCard span={{ desktop: 2, wide: 2 }}>
              <RoleManagementTable />
            </ResponsiveCard>
          </ResponsiveGrid>
        </motion.section>
      </div>
    </AdminLayout>
  );
}
