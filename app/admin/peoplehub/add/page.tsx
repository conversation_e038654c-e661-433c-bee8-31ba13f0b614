"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

const employeeSchema = z.object({
  firstName: z.string().min(2, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(2, "Last name is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  gender: z.string().min(1, "Gender is required"),
  maritalStatus: z.string().min(1, "Marital status is required"),
  contactNumber: z.string().min(10, "Valid contact number required"),
  email: z.string().email("Invalid email address"),
  emergencyContactName: z.string().min(2, "Emergency contact name is required"),
  relationship: z.string().min(1, "Relationship is required"),
  emergencyPhone: z.string().min(10, "Valid emergency contact required"),
  currentAddress: z.string().min(1, "Current address is required"),
  permanentAddress: z.string().min(1, "Permanent address is required"),
  // Employment Information
  jobTitle: z.string().min(1, "Job title is required"),
  department: z.string().min(1, "Department is required"),
  employmentType: z.string().min(1, "Employment type is required"),
  hireDate: z.string().min(1, "Hire date is required"),
  workLocation: z.string().min(1, "Work location is required"),
  reportingManager: z.string().min(1, "Reporting manager is required"),
  salaryGrade: z.string().min(1, "Salary grade is required"),
  // Additional Information
  education: z.string().min(1, "Education history is required"),
  certifications: z.string().optional(),
  skills: z.string().min(1, "Skills are required"),
  previousExperience: z.string().optional(),
  bankAccountNumber: z.string().min(1, "Bank account number is required"),
  bankName: z.string().min(1, "Bank name is required"),
  bankBranch: z.string().min(1, "Bank branch is required"),
  routingNumber: z.string().min(1, "Routing number is required"),
})

type EmployeeFormData = z.infer<typeof employeeSchema>

export default function AddEmployeePage() {
  const router = useRouter()
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
  })

  const onSubmit = async (data: EmployeeFormData) => {
    try {
      console.log(data)
      router.push("/admin/peoplehub")
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <AdminLayout>
      <div className="max-w-6xl mx-auto pt-2 pb-10">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 ml-3">Add New Employee</h1>
            <p className="text-gray-500 text-sm ml-3">Create a new employee record with complete information</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button
              type="submit"
              form="employee-form"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Create Employee
            </Button>
          </div>
        </div>

        <form id="employee-form" onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Personal Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Personal Information</h2>
            <p className="text-sm text-gray-500 mb-6">Basic personal details and contact information</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">First Name *</label>
                <Input {...register("firstName")} />
                {errors.firstName && <span className="text-sm text-red-500">{errors.firstName.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Middle Name</label>
                <Input {...register("middleName")} />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Last Name *</label>
                <Input {...register("lastName")} />
                {errors.lastName && <span className="text-sm text-red-500">{errors.lastName.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Date of Birth *</label>
                <Input type="date" {...register("dateOfBirth")} />
                {errors.dateOfBirth && <span className="text-sm text-red-500">{errors.dateOfBirth.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Gender *</label>
                <select {...register("gender")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                {errors.gender && <span className="text-sm text-red-500">{errors.gender.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Marital Status *</label>
                <select {...register("maritalStatus")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select</option>
                  <option value="single">Single</option>
                  <option value="married">Married</option>
                  <option value="divorced">Divorced</option>
                </select>
                {errors.maritalStatus && <span className="text-sm text-red-500">{errors.maritalStatus.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Contact Number *</label>
                <Input {...register("contactNumber")} placeholder="91-9500599500" />
                {errors.contactNumber && <span className="text-sm text-red-500">{errors.contactNumber.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Email Address *</label>
                <Input type="email" {...register("email")} placeholder="<EMAIL>" />
                {errors.email && <span className="text-sm text-red-500">{errors.email.message}</span>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Emergency Contact Name</label>
                <Input {...register("emergencyContactName")} />
                {errors.emergencyContactName && (
                  <span className="text-sm text-red-500">{errors.emergencyContactName.message}</span>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Relationship</label>
                <Input {...register("relationship")} placeholder="Spouse" />
                {errors.relationship && <span className="text-sm text-red-500">{errors.relationship.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Emergency Contact Phone</label>
                <Input {...register("emergencyPhone")} placeholder="91-9500000000" />
                {errors.emergencyPhone && <span className="text-sm text-red-500">{errors.emergencyPhone.message}</span>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Current Address</label>
                <textarea
                  {...register("currentAddress")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="NH boys hostel,102"
                />
                {errors.currentAddress && <span className="text-sm text-red-500">{errors.currentAddress.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Permanent Address</label>
                <textarea
                  {...register("permanentAddress")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="Andhra pradesh, Markapur"
                />
                {errors.permanentAddress && <span className="text-sm text-red-500">{errors.permanentAddress.message}</span>}
              </div>
            </div>
          </div>

          {/* Employment Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Employment Information</h2>
            <p className="text-sm text-gray-500 mb-6">Job details and organizational information</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Job Title *</label>
                <Input {...register("jobTitle")} placeholder="Software Engineer" />
                {errors.jobTitle && <span className="text-sm text-red-500">{errors.jobTitle.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Department *</label>
                <select {...register("department")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select Department</option>
                  <option value="engineering">Engineering</option>
                  <option value="hr">Human Resources</option>
                  <option value="sales">Sales</option>
                </select>
                {errors.department && <span className="text-sm text-red-500">{errors.department.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Employment Type *</label>
                <select {...register("employmentType")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select Type</option>
                  <option value="fulltime">Full Time</option>
                  <option value="parttime">Part Time</option>
                  <option value="contract">Contract</option>
                </select>
                {errors.employmentType && <span className="text-sm text-red-500">{errors.employmentType.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Hire Date *</label>
                <Input type="date" {...register("hireDate")} />
                {errors.hireDate && <span className="text-sm text-red-500">{errors.hireDate.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Work Location *</label>
                <select {...register("workLocation")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select Location</option>
                  <option value="bangalore">Bangalore</option>
                  <option value="remote">Remote</option>
                </select>
                {errors.workLocation && <span className="text-sm text-red-500">{errors.workLocation.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Reporting Manager</label>
                <Input {...register("reportingManager")} placeholder="Johnson" />
                {errors.reportingManager && <span className="text-sm text-red-500">{errors.reportingManager.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Salary Grade</label>
                <select {...register("salaryGrade")} className="w-full h-10 rounded-md border border-input bg-background px-3">
                  <option value="">Select Grade</option>
                  <option value="L1">L1</option>
                  <option value="L2">L2</option>
                  <option value="L3">L3</option>
                </select>
                {errors.salaryGrade && <span className="text-sm text-red-500">{errors.salaryGrade.message}</span>}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Additional Information</h2>
            <p className="text-sm text-gray-500 mb-6">Education, skills, and banking details</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Education History</label>
                <textarea
                  {...register("education")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="Bachelors in computer science"
                />
                {errors.education && <span className="text-sm text-red-500">{errors.education.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Certifications</label>
                <textarea
                  {...register("certifications")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="Aws certified solutions, 2023"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Skills</label>
                <textarea
                  {...register("skills")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="Java script, React,Node js"
                />
                {errors.skills && <span className="text-sm text-red-500">{errors.skills.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Previous Work Experience</label>
                <textarea
                  {...register("previousExperience")}
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2"
                  placeholder="Software solutions at Tech corp 2020-2024"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Bank Account Number</label>
                <Input {...register("bankAccountNumber")} placeholder="*********" />
                {errors.bankAccountNumber && <span className="text-sm text-red-500">{errors.bankAccountNumber.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Bank Name</label>
                <Input {...register("bankName")} placeholder="Indian bank" />
                {errors.bankName && <span className="text-sm text-red-500">{errors.bankName.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Bank Branch</label>
                <Input {...register("bankBranch")} placeholder="Downtown branch" />
                {errors.bankBranch && <span className="text-sm text-red-500">{errors.bankBranch.message}</span>}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Routing Number / IFSC</label>
                <Input {...register("routingNumber")} placeholder="********" />
                {errors.routingNumber && <span className="text-sm text-red-500">{errors.routingNumber.message}</span>}
              </div>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}
