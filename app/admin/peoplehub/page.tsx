"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Eye,
  Edit,
  Search,
  Users,
  TrendingUp,
  Layers,
  MapPin,
} from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

const departments = ["All", "IT", "HR", "Sales"];
const statuses = ["All", "Active", "Terminated", "On leave"];
const locations = ["All", "New York Office", "Remote", "San Francisco Office"];

const employees = [
  {
    id: "EMP1001",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "IT",
    role: "Senior Developer",
    location: "New York Office",
    status: "Active",
    hireDate: "15/03/2022",
    manager: "<PERSON>",
  },
  {
    id: "EMP1002",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "HR",
    role: "HR Manager",
    location: "Remote",
    status: "Terminated",
    hireDate: "22/01/2021",
    manager: "<PERSON>",
  },
  {
    id: "EMP1003",
    name: "David Lee",
    email: "<EMAIL>",
    department: "Sales",
    role: "Sales Representative",
    location: "San Francisco Office",
    status: "On leave",
    hireDate: "08/09/2023",
    manager: "Lisa Wilson",
  },
];

function getStatusBadge(status: string) {
  switch (status) {
    case "Active":
      return <Badge className="bg-green-100 text-green-700">Active</Badge>;
    case "Terminated":
      return <Badge className="bg-red-100 text-red-600">Terminated</Badge>;
    case "On leave":
      return <Badge className="bg-yellow-100 text-yellow-700">On leave</Badge>;
    default:
      return <Badge>{status}</Badge>;
  }
}

export default function PeopleHubPage() {
  const [search, setSearch] = useState("");
  const [department, setDepartment] = useState("All");
  const [status, setStatus] = useState("All");
  const [location, setLocation] = useState("All");
  const router = useRouter();

  const filtered = employees.filter(
    (emp) =>
      (department === "All" || emp.department === department) &&
      (status === "All" || emp.status === status) &&
      (location === "All" || emp.location === location) &&
      (emp.name.toLowerCase().includes(search.toLowerCase()) ||
        emp.email.toLowerCase().includes(search.toLowerCase()) ||
        emp.id.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <AdminLayout>
      <div>
        {/* Header */}
        <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="font-poppins font-semibold text-2xl md:text-3xl text-gray-900 ml-2">
              Employee Management
            </h1>
            <p className="text-gray-500 mt-1 ml-2">
              Manage employee records and information
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">Export</Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => router.push("/admin/peoplehub/add")}
            >
              + Add Employee
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
          <div className="bg-gradient-to-br from-blue-50 to-white rounded-xl shadow-sm p-6 flex flex-col border border-blue-100">
            <div className="flex justify-between items-start">
              <span className="text-gray-500 text-sm mb-2">
                Total Employees
              </span>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
            </div>
            <span className="font-bold text-3xl text-gray-900">8,547</span>
            <span className="text-green-600 text-xs mt-1 flex items-center gap-1">
              <TrendingUp className="w-3 h-3" />
              +12% from month
            </span>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-white rounded-xl shadow-sm p-6 flex flex-col border border-purple-100">
            <div className="flex justify-between items-start">
              <span className="text-gray-500 text-sm mb-2">Departments</span>
              <div className="p-2 bg-purple-100 rounded-lg">
                <Layers className="w-4 h-4 text-purple-600" />
              </div>
            </div>
            <span className="font-bold text-3xl text-gray-900">17</span>
            <span className="text-gray-400 text-xs mt-1">
              Across all locations
            </span>
          </div>

          <div className="bg-gradient-to-br from-emerald-50 to-white rounded-xl shadow-sm p-6 flex flex-col border border-emerald-100">
            <div className="flex justify-between items-start">
              <span className="text-gray-500 text-sm mb-2">Locations</span>
              <div className="p-2 bg-emerald-100 rounded-lg">
                <MapPin className="w-4 h-4 text-emerald-600" />
              </div>
            </div>
            <span className="font-bold text-3xl text-gray-900">12</span>
            <span className="text-gray-400 text-xs mt-1">
              Global Offices + remote
            </span>
          </div>
        </div>

        {/* Employee Directory */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2 ml-3">
            Employee Directory
          </h2>
          <p className="text-gray-500 text-sm mb-3 ml-3">
            Search and filter employee records with advanced criteria
          </p>

          {/* Filters */}
          <div className="bg-gray-50/50 rounded-sm px-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search by name, email, phone, or ID..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10 w-full bg-white border-gray-200 rounded-sm"
                />
              </div>
              <div className="flex gap-4 flex-wrap sm:flex-nowrap">
                <select
                  className="min-w-[120px] h-10 rounded-sm border border-gray-200 px-3 py-2 text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={department}
                  onChange={(e) => setDepartment(e.target.value)}
                >
                  <option value="" disabled>
                    Department
                  </option>
                  {departments.map((dep) => (
                    <option key={dep} value={dep}>
                      {dep}
                    </option>
                  ))}
                </select>

                <select
                  className="min-w-[120px] h-10 rounded-sm border border-gray-200 px-3 py-2 text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                >
                  <option value="" disabled>
                    Status
                  </option>
                  {statuses.map((st) => (
                    <option key={st} value={st}>
                      {st}
                    </option>
                  ))}
                </select>

                <select
                  className="min-w-[120px] h-10 rounded-sm border border-gray-200 px-3 py-2 text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                >
                  <option value="" disabled>
                    Location
                  </option>
                  {locations.map((loc) => (
                    <option key={loc} value={loc}>
                      {loc}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Employee Table */}
        <div className="bg-white rounded-xl shadow-sm overflow-x-auto">
          <div className="p-4 border-b border-gray-100">
            <span className="text-gray-500 text-sm">
              Showing {filtered.length} of {employees.length} employees
            </span>
          </div>
          <table className="min-w-full divide-y divide-gray-100">
            <thead>
              <tr className="text-left text-xs text-gray-500 uppercase tracking-wide">
                <th className="px-6 py-3">Employee</th>
                <th className="px-6 py-3">Department</th>
                <th className="px-6 py-3">Role</th>
                <th className="px-6 py-3">Location</th>
                <th className="px-6 py-3">Status</th>
                <th className="px-6 py-3">Hire Date</th>
                <th className="px-6 py-3">Manager</th>
                <th className="px-6 py-3">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {filtered.map((emp) => (
                <tr key={emp.id} className="hover:bg-gray-50 transition">
                  <td className="px-6 py-4">
                    <div>
                      <span className="font-medium text-gray-900">
                        {emp.name}
                      </span>
                      <div className="text-xs text-gray-500">{emp.email}</div>
                      <div className="text-xs text-gray-400">{emp.id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">{emp.department}</td>
                  <td className="px-6 py-4 font-semibold">{emp.role}</td>
                  <td className="px-6 py-4">{emp.location}</td>
                  <td className="px-6 py-4">{getStatusBadge(emp.status)}</td>
                  <td className="px-6 py-4">{emp.hireDate}</td>
                  <td className="px-6 py-4">{emp.manager}</td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
              {filtered.length === 0 && (
                <tr>
                  <td colSpan={8} className="text-center text-gray-400 py-8">
                    No employees found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
}
