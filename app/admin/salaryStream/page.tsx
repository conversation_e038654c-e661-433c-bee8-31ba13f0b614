"use client"

import { motion } from "framer-motion"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { SalaryStreamDashboard } from "@/components/admin/salaryStream/SalaryStreamDashboard"

export default function SalaryStreamPage() {
  return (
    <AdminLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-none"
      >
        <SalaryStreamDashboard />
      </motion.div>
    </AdminLayout>
  )
}
