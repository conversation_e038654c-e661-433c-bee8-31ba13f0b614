"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

interface IntegrationNavbarProps {
  currentPage: string
}

const navItems = [
  { id: "integration", label: "Integration", href: "/admin/talent-terminal/integration-settings" },
  { id: "notifications", label: "Notifications", href: "/admin/talent-terminal/integration-settings/notifications" },
  { id: "templates", label: "Templates", href: "/admin/talent-terminal/integration-settings/templates" },
  { id: "users", label: "Users", href: "/admin/talent-terminal/integration-settings/users" },
  { id: "security", label: "Security", href: "/admin/talent-terminal/integration-settings/security" },
  { id: "general", label: "General", href: "/admin/talent-terminal/integration-settings/general" }
]

export function IntegrationNavbar({ currentPage }: IntegrationNavbarProps) {
  const pathname = usePathname()

  return (
    <div className="border-b border-gray-200 mb-8">
      <nav className="flex space-x-8">
        {navItems.map((item) => {
          const isActive = pathname === item.href || currentPage === item.id
          
          return (
            <Link
              key={item.id}
              href={item.href}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                isActive
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {item.label}
            </Link>
          )
        })}
      </nav>
    </div>
  )
}