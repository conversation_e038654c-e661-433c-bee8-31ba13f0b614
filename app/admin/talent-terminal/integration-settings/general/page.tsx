"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "../components/IntegrationNavbar"
import { Button } from "@/components/ui/button"
import { useState } from "react"

export default function GeneralPage() {
  const [formData, setFormData] = useState({
    companyName: "Acme Corporation",
    companyWebsite: "https://acme.com",
    timeZone: "Pacific Standard Time",
    currency: "USD ($)",
    companyDescription: "We are a leading technology company focused on innovation and excellence."
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    console.log("Saving changes:", formData)
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage your hiring pipeline efficiently
          </p>
        </div>

        <IntegrationNavbar currentPage="general" />

        <div className="space-y-8">
          {/* General Settings */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">General Settings</h2>
              <p className="text-gray-500 mt-1">
                Basic System configuration
              </p>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-6">
                {/* First Row - Company Name and Website */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company Name
                    </label>
                    <input
                      type="text"
                      value={formData.companyName}
                      onChange={(e) => handleInputChange("companyName", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company Website
                    </label>
                    <input
                      type="url"
                      value={formData.companyWebsite}
                      onChange={(e) => handleInputChange("companyWebsite", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Second Row - Time Zone and Currency */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Time Zone
                    </label>
                    <select
                      value={formData.timeZone}
                      onChange={(e) => handleInputChange("timeZone", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Pacific Standard Time">Pacific Standard Time</option>
                      <option value="Mountain Standard Time">Mountain Standard Time</option>
                      <option value="Central Standard Time">Central Standard Time</option>
                      <option value="Eastern Standard Time">Eastern Standard Time</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency
                    </label>
                    <select
                      value={formData.currency}
                      onChange={(e) => handleInputChange("currency", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="USD ($)">USD ($)</option>
                      <option value="EUR (€)">EUR (€)</option>
                      <option value="GBP (£)">GBP (£)</option>
                      <option value="CAD ($)">CAD ($)</option>
                    </select>
                  </div>
                </div>

                {/* Company Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Description
                  </label>
                  <textarea
                    value={formData.companyDescription}
                    onChange={(e) => handleInputChange("companyDescription", e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                </div>

                {/* Save Button */}
                <div className="flex justify-end pt-4">
                  <Button 
                    onClick={handleSave}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2"
                  >
                    Save Changes
                  </Button>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}