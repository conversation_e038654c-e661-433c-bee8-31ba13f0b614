"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "../components/IntegrationNavbar"
import { useState } from "react"

interface NotificationSetting {
  id: string
  title: string
  description: string
  enabled: boolean
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    {
      id: "new-applications",
      title: "New Applications",
      description: "Get notified when candidates apply to your jobs",
      enabled: true
    },
    {
      id: "interview-reminders",
      title: "Interview Reminders",
      description: "Receive reminders before scheduled interviews",
      enabled: true
    },
    {
      id: "stage-updates",
      title: "Stage Updates",
      description: "Get notified when candidates move between stages",
      enabled: true
    },
    {
      id: "daily-digest",
      title: "Daily Email Digest",
      description: "Receive a daily summary of recruitment activity",
      enabled: true
    },
    {
      id: "sms-notifications",
      title: "SMS Notifications",
      description: "Receive urgent notifications via SMS",
      enabled: false
    }
  ])

  const [interviewerReminder, setInterviewerReminder] = useState("30 mins Before")
  const [dailyDigestTime, setDailyDigestTime] = useState("9:00 Am")

  const handleToggle = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, enabled: !notif.enabled } : notif
      )
    )
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage your hiring pipeline efficiently
          </p>
        </div>

        <IntegrationNavbar currentPage="notifications" />

        <div className="space-y-8">
          {/* Notification Settings */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Recruitment Dashboard</h2>
              <p className="text-gray-500 mt-1">
                Manage your hiring pipeline efficiently
              </p>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
              {notifications.map((notification) => (
                <div key={notification.id} className="p-6 flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900">{notification.title}</h3>
                    <p className="text-sm text-gray-500 mt-1">{notification.description}</p>
                  </div>
                  <button
                    onClick={() => handleToggle(notification.id)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      notification.enabled ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        notification.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </section>

          {/* Notification Timing */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Notification Timing</h2>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Interviewer Reminder
                  </label>
                  <input
                    type="text"
                    value={interviewerReminder}
                    onChange={(e) => setInterviewerReminder(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Digest Time
                  </label>
                  <input
                    type="text"
                    value={dailyDigestTime}
                    onChange={(e) => setDailyDigestTime(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}