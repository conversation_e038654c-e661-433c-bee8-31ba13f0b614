"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "./components/IntegrationNavbar"
import { Button } from "@/components/ui/button"
import { 
  Linkedin, 
  Globe, 
  Calendar, 
  Video, 
  Shield,
  Settings
} from "lucide-react"

interface Integration {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  enabled: boolean
  connected: boolean
}

interface IntegrationCardProps {
  integration: Integration
  onToggle: (id: string) => void
}

const jobBoardIntegrations = [
  {
    id: "linkedin",
    name: "LinkedIn Jobs",
    description: "Post jobs to LinkedIn",
    icon: Linkedin,
    enabled: true,
    connected: true
  },
  {
    id: "indeed",
    name: "Indeed",
    description: "Post jobs to Indeed",
    icon: Globe,
    enabled: false,
    connected: true
  },
  {
    id: "twitter",
    name: "Twitter X",
    description: "Share job postings",
    icon: Globe,
    enabled: false,
    connected: false
  }
]

const calendarIntegrations = [
  {
    id: "outlook",
    name: "Outlook Calendar",
    description: "Microsoft Outlook Integration",
    icon: Calendar,
    enabled: true,
    connected: true
  },
  {
    id: "google",
    name: "Google Calendar",
    description: "Google Calendar integration",
    icon: Calendar,
    enabled: false,
    connected: false
  }
]

const otherIntegrations = [
  {
    id: "zoom",
    name: "Zoom",
    description: "Video conferencing",
    icon: Video,
    enabled: true,
    connected: true
  },
  {
    id: "teams",
    name: "Microsoft Teams",
    description: "Video conferencing",
    icon: Video,
    enabled: false,
    connected: false
  },
  {
    id: "checkr",
    name: "Checkr",
    description: "Background checks",
    icon: Shield,
    enabled: false,
    connected: false
  }
]

function IntegrationCard({ integration, onToggle }: IntegrationCardProps) {
  const Icon = integration.icon
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
          <Icon className="w-6 h-6 text-gray-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">{integration.name}</h3>
          <p className="text-sm text-gray-500">{integration.description}</p>
          <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium ${
            integration.connected 
              ? 'bg-blue-100 text-blue-700' 
              : 'bg-gray-100 text-gray-700'
          }`}>
            {integration.connected ? 'Connected' : 'Not Connected'}
          </span>
        </div>
      </div>
      <div className="flex items-center">
        <button
          onClick={() => onToggle(integration.id)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            integration.enabled ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              integration.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
    </div>
  )
}

export default function IntegrationSettingsPage() {
  const handleToggle = (id: string) => {
    console.log(`Toggle integration: ${id}`)
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage your hiring pipeline efficiently
          </p>
        </div>

        <IntegrationNavbar currentPage="integration" />

        <div className="space-y-8">
          {/* Job Board Integrations */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Job Board Integrations</h2>
              <p className="text-gray-500 mt-1">
                Connect with external job boards to automatically publish your job postings
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {jobBoardIntegrations.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={handleToggle}
                />
              ))}
            </div>
          </section>

          {/* Calendar Integrations */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Calendar Integrations</h2>
              <p className="text-gray-500 mt-1">
                Connect your calendar for interview scheduling
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {calendarIntegrations.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={handleToggle}
                />
              ))}
            </div>
          </section>

          {/* Other Integrations */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Other Integrations</h2>
              <p className="text-gray-500 mt-1">
                Additional tools and services
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {otherIntegrations.map((integration) => (
                <IntegrationCard
                  key={integration.id}
                  integration={integration}
                  onToggle={handleToggle}
                />
              ))}
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}
