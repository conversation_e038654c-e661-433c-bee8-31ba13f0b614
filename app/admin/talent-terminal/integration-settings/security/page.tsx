"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "../components/IntegrationNavbar"
import { Button } from "@/components/ui/button"
import { useState } from "react"

export default function SecurityPage() {
  const [gdprCompliance, setGdprCompliance] = useState(true)
  const [sessionTimeout, setSessionTimeout] = useState("1 hour")
  const [dataRetention, setDataRetention] = useState("1 year")

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage your hiring pipeline efficiently
          </p>
        </div>

        <IntegrationNavbar currentPage="security" />

        <div className="space-y-8">
          {/* Security Settings */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Security Settings</h2>
              <p className="text-gray-500 mt-1">
                Configure security and privacy settings
              </p>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
              {/* Two-Factor Authentication */}
              <div className="p-6 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-500 mt-1">Add an extra layer of security to your account</p>
                </div>
                <Button variant="outline">
                  Enable 2FA
                </Button>
              </div>

              {/* Session Timeout */}
              <div className="p-6 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">Session Timeout</h3>
                  <p className="text-sm text-gray-500 mt-1">Automatically log out inactive users</p>
                </div>
                <select
                  value={sessionTimeout}
                  onChange={(e) => setSessionTimeout(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="30 minutes">30 minutes</option>
                  <option value="1 hour">1 hour</option>
                  <option value="2 hours">2 hours</option>
                  <option value="4 hours">4 hours</option>
                </select>
              </div>

              {/* Password Requirements */}
              <div className="p-6 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">Password Requirements</h3>
                  <p className="text-sm text-gray-500 mt-1">Enforce strong password policies</p>
                </div>
                <Button variant="outline">
                  Configure
                </Button>
              </div>
            </div>
          </section>

          {/* Data Privacy */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Data Privacy</h2>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
              {/* GDPR Compliance */}
              <div className="p-6 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">GDPR Compliance</h3>
                  <p className="text-sm text-gray-500 mt-1">Automatically handle data deletion requests</p>
                </div>
                <button
                  onClick={() => setGdprCompliance(!gdprCompliance)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    gdprCompliance ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      gdprCompliance ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Data Retention */}
              <div className="p-6 flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">Data Retention</h3>
                  <p className="text-sm text-gray-500 mt-1">Automatically delete old candidate data</p>
                </div>
                <select
                  value={dataRetention}
                  onChange={(e) => setDataRetention(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="6 months">6 months</option>
                  <option value="1 year">1 year</option>
                  <option value="2 years">2 years</option>
                  <option value="3 years">3 years</option>
                </select>
              </div>
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}