"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "../components/IntegrationNavbar"
import { Button } from "@/components/ui/button"
import { Edit, Trash2 } from "lucide-react"

const jobTemplates = [
  {
    id: "software-engineer",
    title: "Software Engineer",
    description: "Template for software engineering positions",
    lastModified: "15/1/2024"
  },
  {
    id: "product-manager",
    title: "Product Manager",
    description: "Template for product management roles",
    lastModified: "12/1/2024"
  }
]

const emailTemplates = [
  {
    id: "application-confirmation",
    title: "Application Confirmation",
    description: "Sent automatically when candidates apply"
  },
  {
    id: "interview-invitation",
    title: "Interview Invitation",
    description: "Sent when scheduling interviews"
  },
  {
    id: "rejection-email",
    title: "Rejection Email",
    description: "Sent when candidates are not selected"
  },
  {
    id: "offer-letter",
    title: "Offer Letter",
    description: "Sent when extending job offers"
  }
]

export default function TemplatesPage() {
  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage users and their permissions
          </p>
        </div>

        <IntegrationNavbar currentPage="templates" />

        <div className="space-y-8">
          {/* Job Posting Templates */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="font-bold text-xl text-gray-900">Job Posting Templates</h2>
                <p className="text-gray-500 mt-1">
                  Manage reusable job posting templates
                </p>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold">
                + Create Template
              </Button>
            </div>
            
            <div className="space-y-4">
              {jobTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">{template.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                      <p className="text-xs text-gray-400 mt-2">Last modified: {template.lastModified}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" className="flex items-center space-x-1">
                        <Edit className="w-4 h-4" />
                        <span>Edit</span>
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center space-x-1 text-red-600 hover:text-red-700">
                        <Trash2 className="w-4 h-4" />
                        <span>Delete</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Email Templates */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Email Templates</h2>
              <p className="text-gray-500 mt-1">
                Customize automated email communications
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {emailTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-gray-900">{template.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      Customize
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}