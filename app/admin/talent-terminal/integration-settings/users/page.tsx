"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { IntegrationNavbar } from "../components/IntegrationNavbar"
import { Button } from "@/components/ui/button"
import { Check, X } from "lucide-react"

const teamMembers = [
  {
    id: "john-smith",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    avatar: "J<PERSON>"
  },
  {
    id: "john-doe",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    avatar: "JD"
  }
]

const rolePermissions = {
  admin: [
    { permission: "Full system access", allowed: true },
    { permission: "Manage users", allowed: true },
    { permission: "Configure integrations", allowed: true }
  ],
  recruiter: [
    { permission: "Manage candidates", allowed: true },
    { permission: "Schedule interviews", allowed: true },
    { permission: "System settings", allowed: false }
  ],
  interviewer: [
    { permission: "View candidates", allowed: true },
    { permission: "Add interview notes", allowed: true },
    { permission: "Manage job postings", allowed: false }
  ]
}

export default function UsersPage() {
  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        <div className="mb-8">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Recruitment Dashboard
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Manage users and their permissions
          </p>
        </div>

        <IntegrationNavbar currentPage="users" />

        <div className="space-y-8">
          {/* Team Members */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="font-bold text-xl text-gray-900">Team Members</h2>
                <p className="text-gray-500 mt-1">
                  Manage users and their permissions
                </p>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold">
                + Invite User
              </Button>
            </div>
            
            <div className="space-y-4">
              {teamMembers.map((member) => (
                <div key={member.id} className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {member.avatar}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{member.name}</h3>
                        <p className="text-sm text-gray-500">{member.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className="px-3 py-1 bg-gray-900 text-white text-sm font-medium rounded-full">
                        {member.role}
                      </span>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Role Permissions */}
          <section>
            <div className="mb-6">
              <h2 className="font-bold text-xl text-gray-900">Role Permissions</h2>
              <p className="text-gray-500 mt-1">
                Configure what each role can access
              </p>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-gray-200">
                {/* Admin Column */}
                <div className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Admin</h3>
                  <div className="space-y-3">
                    {rolePermissions.admin.map((item, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        {item.allowed ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <X className="w-4 h-4 text-red-600" />
                        )}
                        <span className="text-sm text-gray-700">{item.permission}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recruiter Column */}
                <div className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Recruiter</h3>
                  <div className="space-y-3">
                    {rolePermissions.recruiter.map((item, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        {item.allowed ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <X className="w-4 h-4 text-red-600" />
                        )}
                        <span className="text-sm text-gray-700">{item.permission}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Interviewer Column */}
                <div className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Interviewer</h3>
                  <div className="space-y-3">
                    {rolePermissions.interviewer.map((item, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        {item.allowed ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <X className="w-4 h-4 text-red-600" />
                        )}
                        <span className="text-sm text-gray-700">{item.permission}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </AdminLayout>
  )
}