"use client"

import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"

interface JobPostingTabsProps {
  className?: string
}

const tabs = [
  { id: "basic-info", label: "Basic Info", path: "/admin/talent-terminal/job-postings" },
  { id: "details", label: "Details", path: "/admin/talent-terminal/job-postings/details" },
  { id: "questions", label: "Questions", path: "/admin/talent-terminal/job-postings/questions" },
  { id: "publish", label: "Publish", path: "/admin/talent-terminal/job-postings/publish" }
]

export function JobPostingTabs({ className }: JobPostingTabsProps) {
  const pathname = usePathname()
  const router = useRouter()

  const handleTabClick = (path: string) => {
    router.push(path)
  }

  return (
    <div className={cn("sticky top-0 z-10 bg-white border-b border-gray-200 mb-8", className)}>
      <div className="flex space-x-8 px-6 py-4">
        {tabs.map((tab) => {
          const isActive = pathname === tab.path
          return (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.path)}
              className={cn(
                "pb-2 px-1 border-b-2 font-medium text-sm transition-colors",
                isActive
                  ? "border-blue-600 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              {tab.label}
            </button>
          )
        })}
      </div>
    </div>
  )
}