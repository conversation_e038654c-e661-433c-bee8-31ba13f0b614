"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { JobPostingTabs } from "../components/JobPostingTabs"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useState } from "react"

export default function DetailsPage() {
  const [description, setDescription] = useState(
    "Join our product team to drive product strategy, work with engineering and design teams, and deliver exceptional user experiences. You'll be responsible for product roadmap planning and stakeholder management."
  )
  const [skills, setSkills] = useState<string[]>([])
  const [newSkill, setNewSkill] = useState("")

  const addSkill = () => {
    if (newSkill.trim() && !skills.includes(newSkill.trim())) {
      setSkills([...skills, newSkill.trim()])
      setNewSkill("")
    }
  }

  const removeSkill = (skillToRemove: string) => {
    setSkills(skills.filter(skill => skill !== skillToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addSkill()
    }
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        {/* Header */}
        <div className="mb-6">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Create Job Posting
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Create a new job posting and publish it across multiple platforms
          </p>
        </div>

        {/* Tab Navigation */}
        <JobPostingTabs />

        {/* Job Description Form */}
        <div className="max-w-4xl">
          <div className="mb-8">
            <h2 className="font-bold text-xl text-gray-900 mb-2">
              Job Description
            </h2>
            <p className="text-gray-500">
              Detailed description of the role and responsibilities
            </p>
          </div>

          <div className="space-y-8">
            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
              <div className="flex justify-end mt-2">
                <span className="text-sm text-gray-500">
                  {description.length}/5000 characters
                </span>
              </div>
            </div>

            {/* Required Skills */}
            <div>
              <h3 className="font-bold text-lg text-gray-900 mb-2">
                Required Skills
              </h3>
              <p className="text-gray-500 mb-4">
                Add skills and qualifications required for this position
              </p>
              
              <div className="flex gap-2 mb-4">
                <input
                  type="text"
                  placeholder="Add a skill..."
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <Button
                  onClick={addSkill}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded-lg"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              {/* Skills Display */}
              {skills.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                    >
                      {skill}
                      <button
                        onClick={() => removeSkill(skill)}
                        className="ml-2 text-gray-500 hover:text-gray-700"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}