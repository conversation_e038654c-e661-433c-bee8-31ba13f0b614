"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { JobPostingTabs } from "../components/JobPostingTabs"
import { Button } from "@/components/ui/button"
import { useState } from "react"

interface PublishingOption {
  id: string
  label: string
  description: string
  checked: boolean
}

export default function PublishPage() {
  const [publishingOptions, setPublishingOptions] = useState<PublishingOption[]>([
    {
      id: "company-career",
      label: "Company Career Page (Internal)",
      description: "",
      checked: true
    },
    {
      id: "linkedin",
      label: "LinkedIn Jobs",
      description: "",
      checked: true
    },
    {
      id: "indeed",
      label: "Indeed",
      description: "",
      checked: false
    },
    {
      id: "twitter",
      label: "Twitter/X",
      description: "",
      checked: false
    }
  ])

  const toggleOption = (id: string) => {
    setPublishingOptions(options =>
      options.map(option =>
        option.id === id ? { ...option, checked: !option.checked } : option
      )
    )
  }

  const handlePublish = () => {
    const selectedPlatforms = publishingOptions
      .filter(option => option.checked)
      .map(option => option.label)
    
    console.log("Publishing to:", selectedPlatforms)
    // Handle publish logic here
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        {/* Header */}
        <div className="mb-6">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Create Job Posting
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Create a new job posting and publish it across multiple platforms
          </p>
        </div>

        {/* Tab Navigation */}
        <JobPostingTabs />

        {/* Publishing Options */}
        <div className="max-w-4xl">
          <div className="mb-8">
            <h2 className="font-bold text-xl text-gray-900 mb-2">
              Publishing Options
            </h2>
            <p className="text-gray-500">
              Choose where to publish this job posting
            </p>
          </div>

          <div className="space-y-4 mb-8">
            {publishingOptions.map((option) => (
              <div
                key={option.id}
                className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => toggleOption(option.id)}
              >
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={option.checked}
                    onChange={() => toggleOption(option.id)}
                    className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  {option.checked && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <label className="font-medium text-gray-900 cursor-pointer">
                    {option.label}
                  </label>
                  {option.description && (
                    <p className="text-sm text-gray-500 mt-1">{option.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              onClick={handlePublish}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
            >
              Publish Job Posting
            </Button>
            <Button
              variant="outline"
              className="px-6 py-2"
            >
              Save as Draft
            </Button>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}