"use client"

import { AdminLayout } from "@/components/admin/AdminLayout"
import { JobPostingTabs } from "../components/JobPostingTabs"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, X } from "lucide-react"
import { useState } from "react"

export default function QuestionsPage() {
  const [questions, setQuestions] = useState([
    "How do you prioritize product features?",
    "Describe your experience with product analytics."
  ])
  const [newQuestion, setNewQuestion] = useState("")

  const addQuestion = () => {
    if (newQuestion.trim()) {
      setQuestions([...questions, newQuestion.trim()])
      setNewQuestion("")
    }
  }

  const removeQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addQuestion()
    }
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        {/* Header */}
        <div className="mb-6">
          <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
            Create Job Posting
          </h1>
          <p className="text-gray-500 mt-1 text-base">
            Create a new job posting and publish it across multiple platforms
          </p>
        </div>

        {/* Tab Navigation */}
        <JobPostingTabs />

        {/* Screening Questions Form */}
        <div className="max-w-4xl">
          <div className="mb-8">
            <h2 className="font-bold text-xl text-gray-900 mb-2">
              Screening Questions
            </h2>
            <p className="text-gray-500">
              Add custom questions to help screen candidates
            </p>
          </div>

          <div className="space-y-6">
            {/* Add Question Input */}
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Add a screening question..."
                value={newQuestion}
                onChange={(e) => setNewQuestion(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button
                onClick={addQuestion}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded-lg"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>

            {/* Questions List */}
            <div className="space-y-3">
              {questions.map((question, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <span className="text-gray-900">{question}</span>
                  <button
                    onClick={() => removeQuestion(index)}
                    className="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>

            {questions.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No screening questions added yet. Add your first question above.
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}