"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Search, MapPin, DollarSign, Calendar, Share2, Copy, Trash2, ExternalLink, Users, Eye, ArrowLeft } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface JobPosting {
  id: string;
  title: string;
  status: "Active" | "Draft";
  department: string;
  type: string;
  location: string;
  salary: string;
  postedDate: string;
  description: string;
  skills: string[];
  experience: string;
  screeningQuestions: string[];
  applications: number;
  views: number;
  publishedTo: string[];
}

const jobPostings: JobPosting[] = [
  {
    id: "job1",
    title: "Senior Software Engineer",
    status: "Active",
    department: "Engineering",
    type: "Full-time",
    location: "San Francisco, CA",
    salary: "$120,000 - $180,000",
    postedDate: "15/1/2024",
    description: "We're looking for a senior software engineer to join our growing team...",
    skills: ["React", "Node.js", "TypeScript", "AWS"],
    experience: "5+ years",
    screeningQuestions: [
      "Why do you want to join our company?",
      "Describe your experience with React"
    ],
    applications: 89,
    views: 1234,
    publishedTo: [
      "Company Career Page",
      "LinkedIn Jobs",
      "Indeed"
    ]
  },
  {
    id: "job2",
    title: "Senior Software Engineer",
    status: "Active",
    department: "Product",
    type: "Full-time",
    location: "Remote",
    salary: "$100,000 - $150,000",
    postedDate: "17/12/2024",
    description: "Join our product team to drive innovation and user experience...",
    skills: ["React", "Node.js", "TypeScript", "AWS"],
    experience: "3+ years",
    screeningQuestions: [
      "What's your approach to product road mapping?"
    ],
    applications: 156,
    views: 2341,
    publishedTo: [
      "Full system access",
      "Manage users",
      "Configure integrations"
    ]
  },
  {
    id: "job3",
    title: "Senior Software Engineer",
    status: "Active",
    department: "Product",
    type: "Full-time",
    location: "Remote",
    salary: "$100,000 - $150,000",
    postedDate: "17/12/2024",
    description: "Join our product team to drive innovation and user experience...",
    skills: ["React", "Node.js", "TypeScript", "AWS"],
    experience: "3+ years",
    screeningQuestions: [
      "What's your approach to product road mapping?"
    ],
    applications: 156,
    views: 2341,
    publishedTo: [
      "Full system access",
      "Manage users",
      "Configure integrations"
    ]
  }
];

export default function ManageJobsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Status");

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto pt-2 pb-10">
        {/* Header Section */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            className="p-2"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="font-bold text-2xl md:text-3xl text-gray-900">
              Manage Job Postings
            </h1>
            <p className="text-gray-500 mt-1 text-base">
              View and manage all your job postings
            </p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-grow">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search Jobs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option>All Status</option>
            <option>Active</option>
            <option>Draft</option>
          </select>
        </div>

        {/* Job Listings */}
        <div className="space-y-6">
          {jobPostings.map((job) => (
            <div key={job.id} className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Left Section - Job Details */}
                <div className="lg:w-2/3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <h2 className="font-bold text-xl text-gray-900">{job.title}</h2>
                      <span className="px-3 py-1 bg-gray-800 text-white text-xs font-medium rounded-full">
                        {job.status}
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center text-sm text-gray-500 mb-3 gap-x-4 gap-y-1">
                    <span>{job.department} • {job.type}</span>
                    <span className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" /> {job.location}
                    </span>
                    <span className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" /> {job.salary}
                    </span>
                    <span className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" /> Posted {job.postedDate}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-4">{job.description}</p>

                  <div className="mb-4">
                    <h3 className="font-medium text-gray-900 mb-2">Required Skills</h3>
                    <div className="flex flex-wrap gap-2">
                      {job.skills.map((skill, index) => (
                        <span 
                          key={index} 
                          className="px-3 py-1 bg-gray-50 border border-gray-200 rounded-full text-sm text-gray-700"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h3 className="font-medium text-gray-900 mb-1">Experience Required</h3>
                    <p className="text-gray-700">{job.experience}</p>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Screening Questions</h3>
                    <ul className="list-disc pl-5 text-gray-700 space-y-1">
                      {job.screeningQuestions.map((question, index) => (
                        <li key={index}>{question}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Right Section - Metrics and Actions */}
                <div className="lg:w-1/3">
                  <div className="flex justify-end mb-4 space-x-2">
                    <button className="p-2 text-gray-500 hover:text-gray-700">
                      <Share2 className="w-5 h-5" />
                    </button>
                    <button className="p-2 text-gray-500 hover:text-gray-700">
                      <Copy className="w-5 h-5" />
                    </button>
                    <button className="p-2 text-gray-500 hover:text-red-600">
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="flex mb-4">
                    <div className="w-1/2 bg-blue-50 p-4 rounded-l-lg flex flex-col items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600 mb-1" />
                      <span className="font-bold text-xl text-blue-600">{job.applications}</span>
                      <span className="text-xs text-blue-600">Applications</span>
                    </div>
                    <div className="w-1/2 bg-green-50 p-4 rounded-r-lg flex flex-col items-center justify-center">
                      <Eye className="w-5 h-5 text-green-600 mb-1" />
                      <span className="font-bold text-xl text-green-600">{job.views}</span>
                      <span className="text-xs text-green-600">Views</span>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    <Button className="w-full bg-gray-900 hover:bg-gray-800 text-white">
                      View Application
                    </Button>
                    <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                      <ExternalLink className="w-4 h-4" />
                      View Public Posting
                    </Button>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Published to</h3>
                    <ul className="list-disc pl-5 text-gray-700 space-y-1">
                      {job.publishedTo.map((platform, index) => (
                        <li key={index}>{platform}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </AdminLayout>
  );
}