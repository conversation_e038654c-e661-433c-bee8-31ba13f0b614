"use client";

import { AdminLayout } from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpRight, Eye, Briefcase, Users, Clock, Settings, Calendar } from "lucide-react";
import { useRouter } from "next/navigation";

const metrics = [
	{
		title: "Active Job Postings",
		value: 12,
		subtext: "+2 this week",
		icon: <Briefcase className="w-5 h-5 text-gray-400" />,
	},
	{
		title: "Total Applications",
		value: "1,247",
		subtext: "+89 this week",
		icon: <ArrowUpRight className="w-5 h-5 text-purple-500" />,
	},
	{
		title: "Total Applications",
		value: 23,
		subtext: "+5 today",
		icon: <Users className="w-5 h-5 text-gray-400" />,
	},
	{
		title: "Offers Extended",
		value: 8,
		subtext: "+3 this week",
		icon: <Users className="w-5 h-5 text-gray-400" />,
	},
];

const jobPostings = [
	{
		title: "Senior Software Engineer",
		applications: 89,
		views: 1234,
		daysAgo: "2 days ago",
		status: "Active",
	},
	{
		title: "Senior Software Engineer",
		applications: 89,
		views: 1234,
		daysAgo: "2 days ago",
		status: "Active",
	},
	{
		title: "Senior Software Engineer",
		applications: 89,
		views: 1234,
		daysAgo: "2 days ago",
		status: "Draft",
	},
];

const pipeline = [
	{ label: "Applied", value: 1247, color: "bg-blue-600" },
	{ label: "Screened", value: 423, color: "bg-yellow-500" },
	{ label: "Interviewed", value: 89, color: "bg-purple-500" },
	{ label: "Offered", value: 23, color: "bg-green-600" },
	{ label: "Hired", value: 12, color: "bg-blue-900" },
];

// Update the quickActions array to include paths
const quickActions = [
	{
		label: "Manage Job Postings",
		icon: <Briefcase className="w-5 h-5" />,
		path: "/admin/talent-terminal/manage-jobs",
	},
	{
		label: "Review candidates",
		icon: <Eye className="w-5 h-5" />,
		path: "/admin/talent-terminal/candidates",
	},
	{
		label: "Schedule Interviews",
		icon: <Calendar className="w-5 h-5" />,
		path: "/admin/talent-terminal/interviews",
	},
	{
		label: "Integration Settings",
		icon: <Settings className="w-5 h-5" />,
		path: "/admin/talent-terminal/integration-settings",
	},
];

function StatusTag({ status }: { status: string }) {
	if (status === "Active")
		return (
			<span className="px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-700">
				Active
			</span>
		);
	return (
		<span className="px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-700">
			Draft
		</span>
	);
}

export default function TalentTerminalPage() {
	const router = useRouter();

	// Find max value for pipeline bar scaling
	const maxPipeline = Math.max(...pipeline.map((p) => p.value));

	return (
		<AdminLayout>
			<div className="max-w-7xl mx-auto pt-2 pb-10">
				{/* Header */}
				<div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
					<div>
						<h1 className="font-bold text-2xl md:text-3xl text-gray-900">
							Recruitment Dashboard
						</h1>
						<p className="text-gray-500 mt-1 text-base">
							Manage your hiring pipeline efficiently
						</p>
					</div>
					<div className="flex gap-2">
						<Button
							className="bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg px-6 py-2"
							onClick={() => router.push("/admin/talent-terminal/job-postings")}
						>
							Create Job posting
						</Button>
						<Button
							variant="outline"
							className="font-semibold rounded-lg px-6 py-2 border border-gray-300 text-gray-900"
						>
							View All Candidates
						</Button>
					</div>
				</div>

				{/* Metrics Row */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
					{metrics.map((card) => (
						<div
							key={card.title}
							className="bg-white rounded-xl shadow-sm p-6 flex flex-col justify-between"
						>
							<div className="flex items-center justify-between mb-2">
								<span className="font-semibold text-gray-700">
									{card.title}
								</span>
								{card.icon}
							</div>
							<span className="font-bold text-2xl text-gray-900">
								{card.value}
							</span>
							<span className="text-sm text-gray-400 mt-2">{card.subtext}</span>
						</div>
					))}
				</div>

				{/* Main Grid */}
				<div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
					{/* Left: Recent Job Postings */}
					<section className="lg:col-span-7">
						<div className="bg-white rounded-xl shadow-sm p-6 mb-6">
							<h2 className="font-bold text-lg text-gray-900 mb-1">
								Recent Job Postings
							</h2>
							<p className="text-gray-500 mb-6">
								Track performance of your latest job postings
							</p>
							<div className="space-y-4">
								{jobPostings.map((job, idx) => (
									<div
										key={idx}
										className="flex items-center justify-between bg-white border rounded-lg shadow-sm px-5 py-4"
									>
										<div>
											<div className="font-semibold text-gray-900 text-base">
												{job.title}
											</div>
											<div className="flex items-center gap-6 mt-2 text-sm text-gray-500">
												<span className="flex items-center gap-1">
													<Users className="w-4 h-4" /> {job.applications}{" "}
													applications
												</span>
												<span className="flex items-center gap-1">
													<Eye className="w-4 h-4" /> {job.views} views
												</span>
												<span>{job.daysAgo}</span>
											</div>
										</div>
										<div className="flex items-center gap-3">
											<StatusTag status={job.status} />
											<Button
												variant="outline"
												className="font-semibold px-4 py-1 border border-gray-300 text-gray-900"
											>
												View
											</Button>
										</div>
									</div>
								))}
							</div>
						</div>
					</section>

					{/* Right Sidebar */}
					<aside className="lg:col-span-5 flex flex-col gap-8">
						{/* Hiring Pipeline */}
						<div className="bg-white rounded-xl shadow-sm p-6">
							<h2 className="font-bold text-lg text-gray-900 mb-1">
								Hiring Pipeline
							</h2>
							<p className="text-gray-500 mb-6">Candidates by stage</p>
							<div className="space-y-4">
								{pipeline.map((stage) => (
									<div key={stage.label} className="flex items-center gap-3">
										<span className="w-32 text-sm text-gray-700">
											{stage.label}
										</span>
										<div className="flex-1">
											<div
												className={`h-2 rounded-full ${stage.color}`}
												style={{
													width: `${(stage.value / maxPipeline) * 100}%`,
													minWidth: "32px",
												}}
											/>
										</div>
										<span className="text-sm font-semibold text-gray-900">
											{stage.value}
										</span>
									</div>
								))}
							</div>
						</div>
						{/* Quick Actions */}
						<div className="bg-white rounded-xl shadow-sm p-6">
							<h2 className="font-bold text-lg text-gray-900 mb-4">
								Quick Actions
							</h2>
							<div className="flex flex-col gap-2">
								{quickActions.map((action) => (
									<button
										key={action.label}
										className="flex items-center gap-3 px-4 py-3 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
										onClick={() => router.push(action.path)}
									>
										{action.icon}
										{action.label}
									</button>
								))}
							</div>
						</div>
					</aside>
				</div>
			</div>
		</AdminLayout>
	);
}