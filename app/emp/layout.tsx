import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Poppins } from "next/font/google"
import "./globals.css"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
})

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
})

export const metadata: Metadata = {
  title: "SRNR IT Solutions - HR Dashboard",
  description: "Premium HR Management Dashboard",
  generator: 'Next.js',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${poppins.variable} ${inter.variable}`}>
      <head>
        <link rel="icon" href="/srnr_logo.png" type="image/png" />
        {/* You can use favicon.ico or any other image */}
      </head>
      <body className="font-poppins antialiased">{children}</body>
    </html>
  )
}
