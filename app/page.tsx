'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { createClient } from '@/lib/supabase'

export default function HomePage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const handleRedirect = async () => {
      if (!loading) {
        if (!user) {
          router.push('/auth/login')
        } else {
          // If we have a user but no profile, try to get role from user metadata
          if (!profile && user.user_metadata?.role) {
            const role = user.user_metadata.role
            if (role === 'admin') {
              router.push('/admin')
            } else {
              router.push('/emp')
            }
          } else if (profile) {
            if (profile.role === 'admin') {
              router.push('/admin')
            } else {
              router.push('/emp')
            }
          } else {
            // Fallback: redirect to employee dashboard
            router.push('/emp')
          }
        }
      }
    }

    handleRedirect()
  }, [user, profile, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return null
}