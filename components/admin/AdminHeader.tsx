"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Bell, Menu, <PERSON><PERSON><PERSON>, User, PanelLeftClose, PanelLeft } from "lucide-react"

interface AdminHeaderProps {
  onMenuClick?: () => void
  onSidebarToggle?: () => void
  sidebarCollapsed?: boolean
  isMobile?: boolean
}

export function AdminHeader({ onMenuClick, onSidebarToggle, sidebarCollapsed, isMobile }: AdminHeaderProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showNotifications, setShowNotifications] = useState(false)

  return (
    <motion.header 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-30 shadow-sm"
    >
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Button */}
          {isMobile && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onMenuClick}
              className="p-2 rounded-xl bg-slate-100 text-slate-600 hover:bg-slate-200 transition-colors"
            >
              <Menu className="w-5 h-5" />
            </motion.button>
          )}

          {/* Desktop Sidebar Toggle */}
          {!isMobile && onSidebarToggle && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onSidebarToggle}
              className="p-2 rounded-xl bg-slate-100 text-slate-600 hover:bg-slate-200 transition-colors"
              title={sidebarCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
            >
              {sidebarCollapsed ? (
                <PanelLeft className="w-5 h-5" />
              ) : (
                <PanelLeftClose className="w-5 h-5" />
              )}
            </motion.button>
          )}

          {/* Title */}
          <div>
            <h1 className="font-poppins font-bold text-2xl lg:text-3xl bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-slate-600 text-sm hidden sm:block">
              Welcome back, Balu! Here's what's happening today.
            </p>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-3">
          {/* Search - Hidden on mobile */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="relative hidden md:block"
          >
            <input 
              type="text" 
              placeholder="Search anything..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="py-2.5 px-4 pr-10 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64 lg:w-80 bg-white/70 backdrop-blur-sm shadow-sm text-sm"
            />
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="absolute right-3 top-2.5 p-1 rounded-lg bg-blue-500 text-white"
            >
              <Search className="h-3 w-3" />
            </motion.div>
          </motion.div>

          {/* Mobile Search Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="md:hidden p-2.5 rounded-xl bg-slate-100 text-slate-600 hover:bg-slate-200 transition-colors"
          >
            <Search className="w-5 h-5" />
          </motion.button>

          {/* Notifications */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="relative"
          >
            <motion.button 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2.5 rounded-xl bg-white text-slate-600 hover:bg-slate-200 transition-colors"
            >
              <Bell className="h-5 w-5" />
              <motion.span 
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
                className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full text-xs text-white flex items-center justify-center font-bold shadow-lg"
              >
                3
              </motion.span>
            </motion.button>

            {/* Notifications Dropdown */}
            {showNotifications && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-slate-200 p-4 z-50"
              >
                <h3 className="font-semibold text-slate-800 mb-3">Notifications</h3>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                    <p className="text-sm font-medium text-slate-800">New employee onboarded</p>
                    <p className="text-xs text-slate-600 mt-1">John Doe joined the development team</p>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                    <p className="text-sm font-medium text-slate-800">Leave request pending</p>
                    <p className="text-xs text-slate-600 mt-1">Sarah Wilson requested 3 days leave</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                    <p className="text-sm font-medium text-slate-800">Monthly report ready</p>
                    <p className="text-xs text-slate-600 mt-1">October analytics report is available</p>
                  </div>
                </div>
                <button className="w-full mt-3 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium">
                  View all notifications
                </button>
              </motion.div>
            )}
          </motion.div>

          {/* User Profile */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="flex items-center space-x-3 bg-white rounded-xl p-2 hover:bg-slate-200 transition-colors cursor-pointer"
          >
            <img
              src="/emp_dummy.png"
              alt="User Profile"
              className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl object-cover shadow-lg"
            />
            <div className="hidden sm:block">
              <p className="font-semibold text-slate-800 text-sm">Balu Admin</p>
              <p className="text-slate-500 text-xs"><EMAIL></p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.header>
  )
}