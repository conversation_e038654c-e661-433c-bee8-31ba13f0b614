"use client"

import { <PERSON>actN<PERSON>, useState, useEffect, createContext, useContext } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { AdminSidebar } from "./AdminSidebar"
import { AdminHeader } from "./AdminHeader"

interface AdminLayoutProps {
  children: ReactNode
}

interface SidebarContextType {
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void
  sidebarWidth: number
  isMobile: boolean
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

export const useSidebar = () => {
  const context = useContext(SidebarContext)
  if (!context) {
    throw new Error('useSidebar must be used within AdminLayout')
  }
  return context
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  // Dynamic sidebar width based on state
  const sidebarWidth = sidebarCollapsed ? 80 : 280

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
      
      // Auto-collapse sidebar on tablet
      if (width >= 768 && width < 1024) {
        setSidebarCollapsed(true)
      }
      
      // Close mobile menu when screen becomes desktop
      if (width >= 1024) {
        setMobileMenuOpen(false)
      }
      
      // Auto-collapse on smaller desktop screens
      if (width < 1200 && width >= 1024) {
        setSidebarCollapsed(true)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  const toggleSidebar = () => {
    if (!isMobile) {
      setSidebarCollapsed(!sidebarCollapsed)
    }
  }

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  const sidebarContextValue: SidebarContextType = {
    sidebarCollapsed,
    setSidebarCollapsed,
    sidebarWidth,
    isMobile
  }

  return (
    <SidebarContext.Provider value={sidebarContextValue}>
      <div className="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
        {/* Desktop Sidebar */}
        <div className="hidden md:block relative z-30">
          <AdminSidebar 
            collapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
          />
        </div>

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40 md:hidden"
                onClick={() => setMobileMenuOpen(false)}
              />
              <motion.div
                initial={{ x: -300 }}
                animate={{ x: 0 }}
                exit={{ x: -300 }}
                transition={{ type: "spring", damping: 25, stiffness: 200 }}
                className="fixed left-0 top-0 z-50 md:hidden"
              >
                <AdminSidebar 
                  collapsed={false}
                  onToggle={() => {}}
                />
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main Content Area */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ 
            opacity: 1,
            marginLeft: isMobile ? 0 : sidebarWidth
          }}
          transition={{ 
            duration: 0.3,
            ease: "easeInOut"
          }}
          className="flex-1 flex flex-col overflow-hidden"
          style={{
            width: isMobile ? '100%' : `calc(100% - ${sidebarWidth}px)`,
            marginLeft: isMobile ? 0 : sidebarWidth
          }}
        >
          {/* Header */}
          <AdminHeader 
            onMenuClick={toggleMobileMenu}
            onSidebarToggle={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
            isMobile={isMobile}
          />

          {/* Content Container */}
          <main className="flex-1 overflow-auto bg-transparent">
            <div className="min-h-full w-full">
              <div className="p-4 sm:p-6 lg:p-8 max-w-full">
                <div className="w-full max-w-none">
                  {children}
                </div>
              </div>
            </div>
          </main>
        </motion.div>
      </div>
    </SidebarContext.Provider>
  )
}