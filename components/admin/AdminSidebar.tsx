"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/auth-context";
import {
  LayoutDashboard,
  Users,
  UserPlus,
  Calendar,
  TrendingUp,
  FileText,
  DollarSign,
  Route,
  Building,
  BarChart3,
  UserCheck,
  LogOut,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Shield,
  ClockIcon,
  CalendarDays,
  Trophy,
  FolderOpen,
  Banknote,
  Package,
  User,
} from "lucide-react";

type SidebarItem = {
  icon: React.ElementType;
  label: string;
  href: string;
  active?: boolean;
  hasSubmenu?: boolean;
  submenu?: { label: string; href: string }[];
};

const sidebarItems: SidebarItem[] = [
  { icon: LayoutDashboard, label: "Dashboard", href: "/admin", active: true },
  { icon: Users, label: "People Hub", href: "/admin/peoplehub" },
  { icon: UserPlus, label: "Onboarding", href: "/admin/onboarding" },
  { icon: Shield, label: "Duty & Downtime", href: "/admin/duty" },
  { icon: CalendarDays, label: "Leave-Management", href: "/admin/leave-management" },
  { icon: ClockIcon, label: "Attendance", href: "/admin/attendance" },
  { icon: Trophy, label: "Growth Track", href: "/admin/growth-tracking/dashboard" },
  { icon: FolderOpen, label: "Doc Vault", href: "/admin/docuvault" },
  { icon: Banknote, label: "Salary Stream", href: "/admin/salaryStream" },
  // Example with submenu:
  // {
  //   icon: Route,
  //   label: "Purpose Path",
  //   href: "/admin/purpose",
  //   hasSubmenu: true,
  //   submenu: [
  //     { label: "Subitem 1", href: "/admin/purpose/sub1" },
  //     { label: "Subitem 2", href: "/admin/purpose/sub2" },
  //   ],
  // },
  // { icon: Building, label: "Commute Console", href: "/admin/commute" },
  { icon: Package, label: "Asset Apex", href: "/admin/assets" },
  { icon: User, label: "User Management", href: "/admin/user-management" },
  { icon: UserCheck, label: "Talent Terminal", href: "/admin/talent-terminal" },
];

interface AdminSidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

export function AdminSidebar({
  collapsed = false,
  onToggle,
}: AdminSidebarProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleSubmenu = (label: string) => {
    if (!collapsed) {
      setExpandedItems((prev) =>
        prev.includes(label)
          ? prev.filter((item) => item !== label)
          : [...prev, label]
      );
    }
  };

  const isActive = (href: string) => pathname === href;

  return (
    <motion.aside
      initial={{ width: collapsed ? 80 : 280 }}
      animate={{ width: collapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="fixed left-0 top-0 z-40 h-screen bg-gradient-to-b from-white via-slate-50 to-blue-50 border-r border-gray-200 shadow-xl"
    >
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div
          className={`border-b border-gray-200 bg-gradient-to-r from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden ${
            collapsed ? "px-4 py-3" : "px-6 py-4"
          }`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5"></div>
          <div className="relative flex items-center">
            <img
              src="/srnr_logo.png"
              alt="SRNR Logo"
              className="block align-middle w-12 h-12"
              style={{ display: "block" }}
            />
            <AnimatePresence>
              {!collapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="ml-4"
                >
                  <h2 className="font-michroma font-bold text-blue-600 text-md tracking-wider leading-tight">
                    SRNR IT SOLUTIONS
                  </h2>
                  <p className="text-xs text-blue-900  mt-1 tracking-wide">
                    Admin Portal
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Toggle Button */}
          {onToggle && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onToggle}
              className={`absolute  border border-gray-200 rounded-full p-1.5 shadow-md hover:shadow-lg transition-all duration-200 ${
                collapsed ? "top-4 right-2" : "top-6 right-4"
              }`}
            >
              {collapsed ? (
                <ChevronRight className="w-2 h-2 text-gray-600" />
              ) : (
                <ChevronLeft className="w-2 h-2 text-gray-600" />
              )}
            </motion.button>
          )}
        </div>

        {/* Navigation */}
        <nav
          className={`flex-1 overflow-y-auto bg-gradient-to-b from-transparent to-blue-50/30 ${
            collapsed ? "p-2" : "p-4"
          } space-y-1`}
        >
          {sidebarItems.map((item) => (
            <div key={item.label}>
              {item.hasSubmenu && !collapsed ? (
                <div>
                  <Button
                    variant="ghost"
                    className={`w-full justify-between px-4 py-3 h-auto rounded-xl transition-all duration-200 ${
                      isActive(item.href)
                        ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg hover:from-blue-700 hover:to-blue-800"
                        : "text-black-600 hover:bg-white/80 hover:text-blue-900 hover:shadow-md"
                    }`}
                    onClick={() => toggleSubmenu(item.label)}
                  >
                    <div className="flex items-center space-x-3">
                      <item.icon className="w-5 h-5 flex-shrink-0" />
                      <span className="font-medium truncate">{item.label}</span>
                    </div>
                    {expandedItems.includes(item.label) ? (
                      <ChevronDown className="w-4 h-4 flex-shrink-0" />
                    ) : (
                      <ChevronRight className="w-4 h-4 flex-shrink-0" />
                    )}
                  </Button>
                  <AnimatePresence>
                    {expandedItems.includes(item.label) && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-6 mt-2 space-y-1 overflow-hidden"
                      >
                        {item.submenu?.map((subItem) => (
                          <Link key={subItem.href} href={subItem.href}>
                            <Button
                              variant="ghost"
                              className={`w-full justify-start px-4 py-2 h-auto rounded-lg text-sm transition-all duration-200 ${
                                isActive(subItem.href)
                                  ? "bg-blue-100 text-blue-700 shadow-sm"
                                  : "text-black-600 hover:bg-white/60 hover:shadow-sm"
                              }`}
                            >
                              {subItem.label}
                            </Button>
                          </Link>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <Link href={item.href}>
                  <motion.div
                    whileHover={{ scale: collapsed ? 1.05 : 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      variant="ghost"
                      className={`w-full transition-all duration-200 ${
                        collapsed
                          ? "justify-center p-3 h-auto rounded-xl"
                          : "justify-start space-x-3 px-4 py-3 h-auto rounded-xl"
                      } ${
                        isActive(item.href)
                          ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg hover:from-blue-700 hover:to-blue-800"
                          : "text-black-600 hover:bg-white/80 hover:text-blue-900 hover:shadow-md"
                      }`}
                      title={collapsed ? item.label : undefined}
                    >
                      <item.icon
                        className={`flex-shrink-0 ${
                          collapsed ? "w-6 h-6" : "w-5 h-5"
                        }`}
                      />
                      <AnimatePresence>
                        {!collapsed && (
                          <motion.span
                            initial={{ opacity: 0, width: 0 }}
                            animate={{ opacity: 1, width: "auto" }}
                            exit={{ opacity: 0, width: 0 }}
                            transition={{ duration: 0.2 }}
                            className="font-medium truncate"
                          >
                            {item.label}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </Button>
                  </motion.div>
                </Link>
              )}
            </div>
          ))}
        </nav>

        {/* User Profile & Logout Section */}
        <UserProfileSection collapsed={collapsed} />
      </div>
    </motion.aside>
  );
}

// User Profile Section Component
function UserProfileSection({ collapsed }: { collapsed: boolean }) {
  const { profile, signOut } = useAuth();

  const handleLogout = async () => {
    await signOut();
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`border-t border-gray-200 bg-gradient-to-r from-slate-50 to-blue-50 ${collapsed ? "p-2" : "p-4"}`}>
      {/* User Info */}
      {profile && (
        <div className={`mb-3 ${collapsed ? "flex justify-center" : ""}`}>
          {collapsed ? (
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || 'User'}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                getInitials(profile.full_name || profile.email)
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
                {profile.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name || 'User'}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  getInitials(profile.full_name || profile.email)
                )}
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {profile.full_name || 'Admin User'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {profile.role === 'admin' ? 'Administrator' : 'Employee'}
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Logout Button */}
      <motion.div
        whileHover={{ scale: collapsed ? 1.05 : 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          onClick={handleLogout}
          variant="ghost"
          className={`w-full text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 hover:shadow-md ${
            collapsed
              ? "justify-center p-3 h-auto rounded-xl"
              : "justify-start space-x-3 px-4 py-3 h-auto rounded-xl"
          }`}
          title={collapsed ? "Logout" : undefined}
        >
          <LogOut
            className={`flex-shrink-0 ${
              collapsed ? "w-6 h-6" : "w-5 h-5"
            }`}
          />
          <AnimatePresence>
            {!collapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2 }}
                className="font-medium"
              >
                Logout
              </motion.span>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>
    </div>
  );
}
