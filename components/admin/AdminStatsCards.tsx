"use client"

import { motion } from "framer-motion"
import { StatsCard, StatsGrid } from "./shared/ui"
import { Users, Bell, DollarSign, TrendingUp } from "lucide-react"
import type { AdminStats } from "./types"

interface AdminStatsCardsProps {
  stats: AdminStats
}

export function AdminStatsCards({ stats }: AdminStatsCardsProps) {
  const statsCards = [
    {
      title: "Total Employees",
      value: stats.totalEmployees.toString(),
      subtitle: `${stats.activeEmployees} Active • ${stats.totalEmployees - stats.activeEmployees} Inactive`,
      icon: <Users />,
      variant: "info" as const,
      trend: {
        value: 5.2,
        label: "from last month",
        direction: "up" as const
      }
    },
    {
      title: "Pending Approvals",
      value: stats.pendingApprovals.toString(),
      subtitle: `${stats.leaveRequests} Leave • 4 Expenses`,
      icon: <Bell />,
      variant: "warning" as const,
      trend: {
        value: -2.1,
        label: "from last week",
        direction: "down" as const
      }
    },
    {
      title: "Monthly Expenses",
      value: `$${stats.monthlyExpenses.toLocaleString()}`,
      subtitle: "Total monthly expenses",
      icon: <DollarSign />,
      variant: "success" as const,
      trend: {
        value: 8.2,
        label: "from last month",
        direction: "up" as const
      }
    },
    {
      title: "New Hires",
      value: stats.newHires.toString(),
      subtitle: "This month",
      icon: <TrendingUp />,
      variant: "default" as const,
      trend: {
        value: 12.5,
        label: "from last month",
        direction: "up" as const
      }
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <StatsGrid columns={4} gap="lg">
        {statsCards.map((card) => (
          <StatsCard
            key={card.title}
            title={card.title}
            value={card.value}
            subtitle={card.subtitle}
            icon={card.icon}
            variant={card.variant}
            trend={card.trend}
          />
        ))}
      </StatsGrid>
    </motion.div>
  )
}