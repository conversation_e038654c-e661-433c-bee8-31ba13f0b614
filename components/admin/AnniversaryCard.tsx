"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User } from "lucide-react"

interface Employee {
  name: string
  position: string
  years: number
  months: number
  avatar?: string
}

interface AnniversaryCardProps {
  employee?: Employee
}

export function AnniversaryCard({ employee }: AnniversaryCardProps) {
  const defaultEmployee: Employee = {
    name: "<PERSON><PERSON><PERSON> Brahma <PERSON>",
    position: "UI/UX Designer",
    years: 2,
    months: 3,
    avatar: "/placeholder-user.jpg"
  }

  const anniversaryEmployee = employee || defaultEmployee

  return (
    <Card className="shadow-lg border-0 h-[280px]">
      <CardContent className="p-6 h-full flex flex-col">
        <div className="text-center flex-1 flex flex-col justify-between">
          <div className="space-y-4">
            <p className="text-sm font-medium text-gray-600">Anniversary</p>
            
            <Avatar className="w-16 h-16 mx-auto border-4 border-purple-200">
              <AvatarImage src={anniversaryEmployee.avatar} />
              <AvatarFallback className="bg-purple-100 text-purple-600">
                <User className="w-8 h-8" />
              </AvatarFallback>
            </Avatar>
            
            <div>
              <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                {anniversaryEmployee.name}
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                {anniversaryEmployee.position}
              </p>
            </div>
          </div>

          <div className="mt-4">
            <div className="bg-purple-50 rounded-lg p-3">
              <p className="text-sm font-medium text-purple-800">
                {anniversaryEmployee.years} Years • {anniversaryEmployee.months} Months
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
