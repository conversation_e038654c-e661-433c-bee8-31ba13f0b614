"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Car } from "lucide-react"

interface CabStat {
  title: string
  value: number
  icon: React.ElementType
  color: string
}

interface CabManagementGridProps {
  stats?: CabStat[]
}

export function CabManagementGrid({ stats }: CabManagementGridProps) {
  const defaultStats: CabStat[] = [
    {
      title: "Employees Using Cab",
      value: 52,
      icon: Car,
      color: "bg-blue-500"
    },
    {
      title: "No of employees cab is assigned",
      value: 10,
      icon: Car,
      color: "bg-cyan-500"
    },
    {
      title: "No of employees cab is to be used",
      value: 32,
      icon: Car,
      color: "bg-purple-500"
    },
    {
      title: "Employees waiting for approval",
      value: 10,
      icon: Car,
      color: "bg-orange-500"
    }
  ]

  const cabStats = stats || defaultStats

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      {cabStats.map((stat, index) => (
        <Card key={stat.title} className="shadow-lg border-0">
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-4">{stat.title}</p>
              <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-3`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </motion.div>
  )
}