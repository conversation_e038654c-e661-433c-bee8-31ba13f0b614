"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"

interface ClaimsData {
  totalClaims: number
  pendingClaims: number
  completedClaims: number
}

interface ClaimsCardProps {
  claims?: ClaimsData
}

export function ClaimsCard({ claims }: ClaimsCardProps) {
  const defaultClaims: ClaimsData = {
    totalClaims: 55,
    pendingClaims: 15,
    completedClaims: 40
  }

  const claimsData = claims || defaultClaims
  const completionPercentage = (claimsData.completedClaims / claimsData.totalClaims) * 100

  return (
    <Card className="shadow-lg border-0 h-[280px]">
      <CardContent className="p-6 h-full flex flex-col">
        <div className="text-center flex-1 flex flex-col justify-between">
          <div className="space-y-4">
            <p className="text-sm font-medium text-gray-600">Claims</p>
            
            <div className="relative w-20 h-20 mx-auto">
              <svg
                className="w-20 h-20 transform -rotate-90"
                viewBox="0 0 80 80"
              >
                <circle
                  cx="40"
                  cy="40"
                  r="32"
                  stroke="currentColor"
                  strokeWidth="6"
                  fill="none"
                  className="text-gray-200"
                />
                <circle
                  cx="40"
                  cy="40"
                  r="32"
                  stroke="currentColor"
                  strokeWidth="6"
                  fill="none"
                  strokeDasharray={`${(completionPercentage / 100) * 201.06} 201.06`}
                  className="text-orange-500"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">
                  {claimsData.totalClaims}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">Total Claims</span>
              <span className="font-medium">{claimsData.totalClaims}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">Pending Claims</span>
              <span className="font-medium text-orange-600">
                {claimsData.pendingClaims}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
