"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import type { DepartmentStat } from "./types"

interface DepartmentStatsCardProps {
  departments: DepartmentStat[]
}

export function DepartmentStatsCard({ departments }: DepartmentStatsCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      <Card className="shadow-lg border-0 h-full">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-gray-900">
            Department Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {departments.map((dept) => (
              <div key={dept.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${dept.color}`}></div>
                  <span className="font-medium text-gray-900">{dept.name}</span>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{dept.employees}</p>
                  <p className="text-xs text-green-600">{dept.growth}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}