"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Users, UserX, CheckCircle, XCircle } from "lucide-react"

interface AttendanceStat {
  title: string
  value: number
  color: string
  icon?: React.ElementType
  isCircular?: boolean
}

interface EmployeeAttendanceGridProps {
  stats?: AttendanceStat[]
}

export function EmployeeAttendanceGrid({ stats }: EmployeeAttendanceGridProps) {
  const defaultStats: AttendanceStat[] = [
    {
      title: "Total No Of Employees Present",
      value: 32,
      color: "bg-blue-500",
      icon: Users
    },
    {
      title: "Total No Of Employees Absent", 
      value: 10,
      color: "bg-orange-500",
      icon: UserX
    },
    {
      title: "No of approved Leaves",
      value: 6,
      color: "bg-blue-500",
      isCircular: true
    },
    {
      title: "No of Non-approved Leaves",
      value: 2,
      color: "bg-green-500", 
      isCircular: true
    }
  ]

  const attendanceStats = stats || defaultStats

  const CircularProgress = ({ value, color }: { value: number; color: string }) => (
    <div className="relative w-16 h-16">
      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
        <circle
          cx="32"
          cy="32"
          r="28"
          stroke="currentColor"
          strokeWidth="4"
          fill="none"
          className="text-gray-200"
        />
        <circle
          cx="32"
          cy="32"
          r="28"
          stroke="currentColor"
          strokeWidth="4"
          fill="none"
          strokeDasharray={`${(value / 10) * 175.929} 175.929`}
          className={color.replace('bg-', 'text-')}
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-lg font-bold text-gray-900">{value.toString().padStart(2, '0')}</span>
      </div>
    </div>
  )

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      {attendanceStats.map((stat, index) => (
        <Card key={stat.title} className="shadow-lg border-0">
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-4">{stat.title}</p>
              {stat.isCircular ? (
                <div className="flex justify-center mb-2">
                  <CircularProgress value={stat.value} color={stat.color} />
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  {stat.icon && (
                    <div className={`w-8 h-8 ${stat.color} rounded-lg flex items-center justify-center mb-2`}>
                      <stat.icon className="w-5 h-5 text-white" />
                    </div>
                  )}
                  <p className="text-4xl font-bold text-gray-900">{stat.value}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </motion.div>
  )
}