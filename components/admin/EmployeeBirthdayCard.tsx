"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Gift, User } from "lucide-react"

interface BirthdayEmployee {
  name: string
  position: string
  avatar?: string
}

interface EmployeeBirthdayCardProps {
  employee?: BirthdayEmployee
}

export function EmployeeBirthdayCard({ employee }: EmployeeBirthdayCardProps) {
  const defaultEmployee: BirthdayEmployee = {
    name: "<PERSON><PERSON> Yaswanth",
    position: "UI/UX Designer",
    avatar: "/placeholder-user.jpg"
  }

  const birthdayEmployee = employee || defaultEmployee

  return (
    <Card className="shadow-lg border-0 h-[280px] bg-gradient-to-br from-blue-50 to-red-100 relative overflow-hidden">
      <CardContent className="p-6 h-full flex flex-col">
        <div className="text-center flex-1 flex flex-col justify-between">
          <div className="space-y-4">
            <Avatar className="w-16 h-16 mx-auto border-4 border-blue-200">
              <AvatarImage src={birthdayEmployee.avatar} />
              <AvatarFallback className="bg-blue-100 text-blue-600">
                <User className="w-8 h-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                {birthdayEmployee.name}
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                {birthdayEmployee.position}
              </p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-3">
              <p className="text-sm font-medium text-yellow-800">Birthday Today!</p>
            </div>
          </div>
          <div className="mt-4">
            <Button 
              size="sm" 
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            >
              Wish Him 🎉
            </Button>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="absolute top-2 right-2 text-4xl opacity-20">🎉</div>
      </CardContent>
    </Card>
  )
}