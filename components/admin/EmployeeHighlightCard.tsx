"use client"

import { ReactNode } from "react"
import { motion } from "framer-motion"

interface EmployeeHighlightCardProps {
  children: ReactNode
  delay?: number
}

export function EmployeeHighlightCard({ children, delay = 0 }: EmployeeHighlightCardProps) {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay }}
      className="w-full h-full"
    >
      <div className="h-full min-h-[280px] flex flex-col">
        {children}
      </div>
    </motion.div>
  )
}