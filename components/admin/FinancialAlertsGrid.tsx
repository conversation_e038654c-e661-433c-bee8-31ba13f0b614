"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertTriangle, DollarSign, Percent } from "lucide-react"

interface FinancialAlert {
  title: string
  amount: string
  icon: React.ElementType
  color: string
  bgColor: string
}

interface FinancialAlertsGridProps {
  alerts?: FinancialAlert[]
}

export function FinancialAlertsGrid({ alerts }: FinancialAlertsGridProps) {
  const defaultAlerts: FinancialAlert[] = [
    {
      title: "Salary Adjustment Alert",
      amount: "₹ 29,287.00",
      icon: AlertTriangle,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100"
    },
    {
      title: "TDS Deduction",
      amount: "₹ 1,800.05",
      icon: AlertTriangle,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100"
    },
    {
      title: "ESI/EPF",
      amount: "₹ 1,800.05",
      icon: Percent,
      color: "text-pink-600",
      bgColor: "bg-pink-100"
    }
  ]

  const financialAlerts = alerts || defaultAlerts

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-3 gap-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      {financialAlerts.map((alert, index) => (
        <Card key={alert.title} className="shadow-lg border-0">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className={`w-10 h-10 ${alert.bgColor} rounded-lg flex items-center justify-center`}>
                <alert.icon className={`w-5 h-5 ${alert.color}`} />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">{alert.title}</p>
                <p className="text-xs text-gray-500">Last Month</p>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900 mb-3">{alert.amount}</p>
            <Button variant="outline" size="sm" className="w-full">
              View Details
            </Button>
          </CardContent>
        </Card>
      ))}
    </motion.div>
  )
}