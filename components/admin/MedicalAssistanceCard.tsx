"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Heart, User } from "lucide-react"

interface MedicalAssistanceData {
  provider: {
    name: string
    avatar?: string
  }
  patient: {
    name: string
    avatar?: string
  }
}

interface MedicalAssistanceCardProps {
  data?: MedicalAssistanceData
}

export function MedicalAssistanceCard({ data }: MedicalAssistanceCardProps) {
  const defaultData: MedicalAssistanceData = {
    provider: {
      name: "Dr<PERSON> <PERSON>",
      avatar: "/placeholder-doctor.jpg"
    },
    patient: {
      name: "<PERSON>",
      avatar: "/placeholder-user.jpg"
    }
  }

  const medicalData = data || defaultData

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.9 }}
    >
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-lg text-gray-900">
            Medical Assistance alert
          </CardTitle>
          <p className="text-sm text-gray-600">Provider & Patient</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center space-x-3">
              <Avatar className="w-10 h-10 border-2 border-blue-200">
                <AvatarImage src={medicalData.provider.avatar} />
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  <User className="w-5 h-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-xs text-blue-600 font-medium">Provider</p>
                <p className="text-sm font-medium">Provider assigned</p>
                <p className="text-sm font-bold text-gray-900">{medicalData.provider.name}</p>
              </div>
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-3">
            <div className="flex items-center space-x-3">
              <Avatar className="w-10 h-10 border-2 border-red-200">
                <AvatarImage src={medicalData.patient.avatar} />
                <AvatarFallback className="bg-red-100 text-red-600">
                  <User className="w-5 h-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-xs text-red-600 font-medium">Patient</p>
                <p className="text-sm font-medium">Patient</p>
                <p className="text-sm font-bold text-gray-900">{medicalData.patient.name}</p>
              </div>
            </div>
          </div>

          <Button variant="outline" size="sm" className="w-full">
            View Details
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  )
}