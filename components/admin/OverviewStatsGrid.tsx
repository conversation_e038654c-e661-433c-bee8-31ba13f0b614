"use client"

import { motion } from "framer-motion"
import { StatsCard, StatsGrid } from "./shared/ui"
import { TrendingUp, Users, CheckCircle, DollarSign, UserPlus } from "lucide-react"

interface OverviewStat {
  title: string
  value: string | number
  change: string
  icon: React.ReactNode
  variant: 'default' | 'success' | 'warning' | 'error' | 'info'
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down'
  }
}

interface OverviewStatsGridProps {
  stats?: OverviewStat[]
}

export function OverviewStatsGrid({ stats }: OverviewStatsGridProps) {
  const defaultStats: OverviewStat[] = [
    {
      title: "Active Projects",
      value: 12,
      change: "+2 from last month",
      icon: <TrendingUp />,
      variant: "info",
      trend: {
        value: 16.7,
        label: "from last month",
        direction: "up"
      }
    },
    {
      title: "Total Clients",
      value: 24,
      change: "+4 from last month",
      icon: <Users />,
      variant: "success",
      trend: {
        value: 20.0,
        label: "from last month",
        direction: "up"
      }
    },
    {
      title: "Completed Projects",
      value: 42,
      change: "+6 from last month",
      icon: <CheckCircle />,
      variant: "default",
      trend: {
        value: 16.7,
        label: "from last month",
        direction: "up"
      }
    },
    {
      title: "Total Revenue",
      value: "$ 89,240",
      change: "+14% from last month",
      icon: <DollarSign />,
      variant: "warning",
      trend: {
        value: 14.0,
        label: "from last month",
        direction: "up"
      }
    },
    {
      title: "Active Onboarding",
      value: 23,
      change: "New hires this week",
      icon: <UserPlus />,
      variant: "info",
      trend: {
        value: 8.5,
        label: "from last week",
        direction: "up"
      }
    }
  ]

  const overviewStats = stats || defaultStats

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <StatsGrid columns={5} gap="lg">
        {overviewStats.map((stat) => (
          <StatsCard
            key={stat.title}
            title={stat.title}
            value={stat.value.toString()}
            subtitle={stat.change}
            icon={stat.icon}
            variant={stat.variant}
            trend={stat.trend}
          />
        ))}
      </StatsGrid>
    </motion.div>
  )
}