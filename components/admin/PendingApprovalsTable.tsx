"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Plus, CheckCircle, XCircle, Eye } from "lucide-react"
import type { PendingApproval } from "./types"

interface PendingApprovalsTableProps {
  approvals: PendingApproval[]
  onApprove?: (id: string) => void
  onReject?: (id: string) => void
  onView?: (id: string) => void
  onViewAll?: () => void
}

export function PendingApprovalsTable({ 
  approvals, 
  onApprove, 
  onReject, 
  onView, 
  onViewAll 
}: PendingApprovalsTableProps) {
  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case "Urgent":
        return <Badge variant="destructive" className="text-xs">{urgency}</Badge>
      case "High":
        return <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs">{urgency}</Badge>
      default:
        return <Badge variant="outline" className="text-xs">{urgency}</Badge>
    }
  }

  return (
    <motion.div
      className="xl:col-span-2"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <Card className="shadow-lg border-0 h-full">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="font-poppins font-bold text-xl text-gray-900">
            Pending Approvals
          </CardTitle>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={onViewAll}>
            <Plus className="w-4 h-4 mr-2" />
            View All
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {approvals.map((approval) => (
              <div 
                key={approval.id} 
                className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{approval.type}</h4>
                    {getUrgencyBadge(approval.urgency)}
                  </div>
                  <p className="text-sm text-gray-600">{approval.employee} • {approval.department}</p>
                  <p className="text-xs text-gray-500 mt-1">{approval.details}</p>
                  <p className="text-xs text-gray-400 mt-1">Requested: {approval.requestDate}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="text-green-600 border-green-200 hover:bg-green-50"
                    onClick={() => onApprove?.(approval.id)}
                  >
                    <CheckCircle className="w-4 h-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="text-red-600 border-red-200 hover:bg-red-50"
                    onClick={() => onReject?.(approval.id)}
                  >
                    <XCircle className="w-4 h-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => onView?.(approval.id)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}