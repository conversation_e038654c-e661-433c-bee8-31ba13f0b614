"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "./shared/ui"
import {
  Users,
  Calendar,
  FileText,
  BarChart3,
  Download,
  Settings
} from "lucide-react"
import type { QuickAction } from "./types"

interface QuickActionsGridProps {
  actions?: QuickAction[]
}

export function QuickActionsGrid({ actions }: QuickActionsGridProps) {
  const defaultActions: QuickAction[] = [
    { icon: Users, label: "Add Employee" },
    { icon: Calendar, label: "Schedule Review" },
    { icon: FileText, label: "Generate Report" },
    { icon: BarChart3, label: "Analytics" },
    { icon: Download, label: "Export Data" },
    { icon: Settings, label: "Settings" }
  ]

  const quickActions = actions || defaultActions

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <Card className="shadow-lg border-0">
        <CardHeader>
          <h3 className="font-poppins font-bold text-xl text-gray-900">
            Quick Actions
          </h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {quickActions.map((action, index) => (
              <Button 
                key={index}
                variant="outline" 
                className="h-20 flex flex-col space-y-2"
                onClick={action.onClick}
              >
                <action.icon className="w-6 h-6" />
                <span className="text-xs">{action.label}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}