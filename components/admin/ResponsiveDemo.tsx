"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ResponsiveGrid, ResponsiveCard, useResponsiveBreakpoint } from "./ResponsiveGrid"
import { Badge } from "@/components/ui/badge"
import { Monitor, Tablet, Smartphone, PanelLeft, PanelLeftClose } from "lucide-react"

export function ResponsiveDemo() {
  const { isMobile, isTablet, isDesktop, sidebarCollapsed, effectiveWidth } = useResponsiveBreakpoint()

  const getDeviceIcon = () => {
    if (isMobile) return <Smartphone className="w-5 h-5" />
    if (isTablet) return <Tablet className="w-5 h-5" />
    return <Monitor className="w-5 h-5" />
  }

  const getDeviceLabel = () => {
    if (isMobile) return "Mobile"
    if (isTablet) return "Tablet"
    return "Desktop"
  }

  return (
    <div className="space-y-6">
      {/* Current State Indicator */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {getDeviceIcon()}
              <span>{getDeviceLabel()}</span>
            </div>
            <div className="flex items-center gap-2">
              {sidebarCollapsed ? <PanelLeftClose className="w-5 h-5" /> : <PanelLeft className="w-5 h-5" />}
              <span>Sidebar {sidebarCollapsed ? 'Collapsed' : 'Expanded'}</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Badge variant={isMobile ? "default" : "secondary"}>
              Mobile: {isMobile ? 'Active' : 'Inactive'}
            </Badge>
            <Badge variant={isTablet ? "default" : "secondary"}>
              Tablet: {isTablet ? 'Active' : 'Inactive'}
            </Badge>
            <Badge variant={isDesktop ? "default" : "secondary"}>
              Desktop: {isDesktop ? 'Active' : 'Inactive'}
            </Badge>
            <Badge variant="outline">
              Effective Width: {effectiveWidth}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Responsive Grid Demo */}
      <div>
        <h3 className="font-semibold text-lg mb-4">Responsive Grid Behavior</h3>
        <ResponsiveGrid 
          cols={{ mobile: 1, tablet: 2, desktop: 3, wide: 4 }}
          gap="gap-4"
        >
          {Array.from({ length: 8 }, (_, i) => (
            <ResponsiveCard key={i}>
              <Card className="h-32 bg-gradient-to-br from-blue-100 to-purple-100">
                <CardContent className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {i + 1}
                    </div>
                    <div className="text-sm text-gray-600">
                      Card {i + 1}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Spanning Cards Demo */}
      <div>
        <h3 className="font-semibold text-lg mb-4">Card Spanning Behavior</h3>
        <ResponsiveGrid 
          cols={{ mobile: 1, tablet: 2, desktop: 4, wide: 6 }}
          gap="gap-4"
        >
          <ResponsiveCard span={{ tablet: 2, desktop: 2, wide: 3 }}>
            <Card className="h-40 bg-gradient-to-br from-green-100 to-emerald-100">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">
                    Wide Card
                  </div>
                  <div className="text-sm text-gray-600">
                    Spans multiple columns
                  </div>
                </div>
              </CardContent>
            </Card>
          </ResponsiveCard>
          
          <ResponsiveCard>
            <Card className="h-40 bg-gradient-to-br from-orange-100 to-red-100">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-600">
                    Regular
                  </div>
                  <div className="text-sm text-gray-600">
                    Single column
                  </div>
                </div>
              </CardContent>
            </Card>
          </ResponsiveCard>
          
          <ResponsiveCard>
            <Card className="h-40 bg-gradient-to-br from-purple-100 to-pink-100">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">
                    Regular
                  </div>
                  <div className="text-sm text-gray-600">
                    Single column
                  </div>
                </div>
              </CardContent>
            </Card>
          </ResponsiveCard>
          
          <ResponsiveCard span={{ desktop: 2, wide: 3 }}>
            <Card className="h-40 bg-gradient-to-br from-teal-100 to-cyan-100">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-bold text-teal-600">
                    Another Wide
                  </div>
                  <div className="text-sm text-gray-600">
                    Desktop+ spanning
                  </div>
                </div>
              </CardContent>
            </Card>
          </ResponsiveCard>
        </ResponsiveGrid>
      </div>
    </div>
  )
}