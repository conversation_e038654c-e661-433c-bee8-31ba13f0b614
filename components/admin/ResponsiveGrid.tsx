"use client"

import { ReactNode } from "react"
import { motion } from "framer-motion"
import { useSidebar } from "./AdminLayout"

interface ResponsiveGridProps {
  children: ReactNode
  className?: string
  cols?: {
    mobile?: number
    tablet?: number
    desktop?: number
    wide?: number
  }
  gap?: string
}

export function ResponsiveGrid({ 
  children, 
  className = "",
  cols = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    wide: 4
  },
  gap = "gap-6"
}: ResponsiveGridProps) {
  const { sidebarCollapsed, isMobile } = useSidebar()

  // Adjust grid columns based on sidebar state and screen size
  const getGridCols = () => {
    if (isMobile) return `grid-cols-${cols.mobile}`
    
    // For desktop, adjust based on sidebar state
    if (!sidebarCollapsed) {
      // Sidebar expanded - use fewer columns
      return `grid-cols-1 md:grid-cols-${cols.tablet} lg:grid-cols-${cols.desktop}`
    } else {
      // Sidebar collapsed - can use more columns
      return `grid-cols-1 md:grid-cols-${cols.tablet} lg:grid-cols-${cols.desktop} xl:grid-cols-${cols.wide}`
    }
  }

  return (
    <motion.div
      layout
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={`grid ${getGridCols()} ${gap} w-full ${className}`}
    >
      {children}
    </motion.div>
  )
}

interface ResponsiveCardProps {
  children: ReactNode
  className?: string
  span?: {
    mobile?: number
    tablet?: number
    desktop?: number
    wide?: number
  }
}

export function ResponsiveCard({ 
  children, 
  className = "",
  span = {}
}: ResponsiveCardProps) {
  const { sidebarCollapsed, isMobile } = useSidebar()

  const getSpanClasses = () => {
    const classes = []
    
    if (span.mobile) classes.push(`col-span-${span.mobile}`)
    if (span.tablet) classes.push(`md:col-span-${span.tablet}`)
    if (span.desktop) classes.push(`lg:col-span-${span.desktop}`)
    if (span.wide && !sidebarCollapsed) classes.push(`xl:col-span-${span.wide}`)
    
    return classes.join(' ')
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`${getSpanClasses()} ${className}`}
    >
      {children}
    </motion.div>
  )
}

// Utility hook for responsive breakpoints
export function useResponsiveBreakpoint() {
  const { sidebarCollapsed, isMobile } = useSidebar()
  
  return {
    isMobile,
    isTablet: !isMobile && sidebarCollapsed,
    isDesktop: !isMobile && !sidebarCollapsed,
    sidebarCollapsed,
    // Effective screen width considering sidebar
    effectiveWidth: isMobile ? 'mobile' : sidebarCollapsed ? 'wide' : 'normal'
  }
}