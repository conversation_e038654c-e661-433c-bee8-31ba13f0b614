"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTit<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit } from "lucide-react"

interface Employee {
  name: string
  email: string
  role: string
  roleColor: string
}

interface RoleManagementTableProps {
  employees?: Employee[]
}

export function RoleManagementTable({ employees }: RoleManagementTableProps) {
  const defaultEmployees: Employee[] = [
    {
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Business Analyst",
      roleColor: "bg-pink-500"
    },
    {
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Data Science",
      roleColor: "bg-green-500"
    },
    {
      name: "<PERSON><PERSON><PERSON> Syad",
      email: "<EMAIL>",
      role: "Front-end",
      roleColor: "bg-blue-500"
    }
  ]

  const roleEmployees = employees || defaultEmployees

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 1.0 }}
    >
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-lg text-gray-900">
            Role Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="font-medium">Employee Name</TableHead>
                <TableHead className="font-medium">Role</TableHead>
                <TableHead className="font-medium text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roleEmployees.map((employee) => (
                <TableRow key={employee.email}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{employee.name}</p>
                      <p className="text-sm text-gray-500">{employee.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={`${employee.roleColor} text-white`}>
                      {employee.role}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </motion.div>
  )
}