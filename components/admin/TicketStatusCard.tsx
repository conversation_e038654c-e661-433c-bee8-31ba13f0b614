"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Clock, LogOut, } from "lucide-react"

interface TicketStatus {
  lateLogins: number
  earlyLogouts: number
}

interface TicketStatusCardProps {
  status?: TicketStatus
}

export function TicketStatusCard({ status }: TicketStatusCardProps) {
  const defaultStatus: TicketStatus = {
    lateLogins: 12,
    earlyLogouts: 4
  }

  const ticketStatus = status || defaultStatus

  return (
    <Card className="shadow-lg border-0 h-auto sm:h-[280px]">
      <CardContent className="p-4 sm:p-6 h-full flex flex-col justify-between">
        <div className="flex flex-col items-center justify-center h-1/2">
          <p className="text-sm font-medium pb-9 text-gray-600 mb-2">Tickets Pending</p>
          <div className="w-full bg-gray-200 rounded-sm h-5 mb-2">
            <div
              className="bg-gradient-to-r from-orange-400 to-red-500 h-5 rounded-sm"
              style={{ width: '60%' }}
            ></div>
          </div>
        </div>
        <div className="flex flex-row items-center justify-between h-1/2 mt-2 gap-2">
          <div className="flex flex-col items-center flex-1 justify-center">
            <Clock className="w-6 h-6 text-blue-600 mb-5" />
            <span className="text-sm font-medium text-gray-700">Late Logins</span>
            <span className="text-xl font-bold text-gray-900">{ticketStatus.lateLogins}</span>
          </div>
          <div className="w-px h-12 bg-gray-200 mx-1" />
          <div className="flex flex-col items-center flex-1 justify-center">
            <LogOut className="w-6 h-6 text-red-600 mb-5" />
            <span className="text-sm font-medium text-gray-700">Early Logouts</span>
            <span className="text-xl font-bold text-gray-900">{ticketStatus.earlyLogouts.toString().padStart(2, '0')}</span>
          </div>
        </div>  
      </CardContent>
    </Card>
  )
}