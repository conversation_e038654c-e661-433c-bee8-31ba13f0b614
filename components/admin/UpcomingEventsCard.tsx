"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import type { UpcomingEvent } from "./types"

interface UpcomingEventsCardProps {
  events: UpcomingEvent[]
}

export function UpcomingEventsCard({ events }: UpcomingEventsCardProps) {
  return (
    <motion.div
      className="xl:col-span-1"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <Card className="shadow-lg border-0 h-full">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-gray-900">
            Upcoming Events
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {events.map((event, index) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-600">{event.date}</span>
                <span className="text-xs text-gray-500">{event.time}</span>
              </div>
              <p className="text-sm font-medium text-gray-900">{event.title}</p>
              <Badge variant="outline" className="text-xs mt-1">{event.type}</Badge>
            </div>
          ))}
        </CardContent>
      </Card>
    </motion.div>
  )
}