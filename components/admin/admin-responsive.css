/* Admin Dashboard Responsive Styles */

/* Ensure no horizontal overflow */
.admin-layout {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* Sidebar responsive behavior */
.admin-sidebar {
  transition: width 0.3s ease-in-out;
  will-change: width;
}

/* Main content area adjustments */
.admin-main-content {
  transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out;
  will-change: margin-left, width;
  min-width: 0; /* Prevent flex items from overflowing */
  flex: 1;
}

/* Responsive grid utilities */
.responsive-grid {
  display: grid;
  width: 100%;
  gap: 1.5rem;
}

/* Mobile-first responsive grid */
@media (max-width: 767px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .admin-main-content {
    padding: 1rem;
  }
}

/* Tablet responsive grid */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .admin-main-content {
    padding: 1.5rem;
  }
}

/* Desktop responsive grid */
@media (min-width: 1024px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .admin-main-content {
    padding: 2rem;
  }
}

/* Wide screen responsive grid (when sidebar is collapsed) */
@media (min-width: 1280px) {
  .sidebar-collapsed .responsive-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .sidebar-collapsed .responsive-grid-5 {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Employee Highlights specific grid - Always 4 columns on XL screens */
.employee-highlights-grid {
  display: grid;
  width: 100%;
  gap: 1rem;
}

@media (max-width: 639px) {
  .employee-highlights-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 640px) and (max-width: 1279px) {
  .employee-highlights-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .employee-highlights-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Ensure consistent card heights */
.employee-highlight-card {
  height: 280px;
  display: flex;
  flex-direction: column;
}

.employee-highlight-card .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.employee-highlight-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Card responsive behavior */
.responsive-card {
  min-width: 0;
  width: 100%;
  height: fit-content;
}

/* Prevent content overflow */
.admin-content-wrapper {
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: 100%;
}

/* Smooth transitions for all interactive elements */
.admin-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure proper spacing and alignment */
.admin-section {
  width: 100%;
  max-width: 100%;
  margin-bottom: 2rem;
}

/* Mobile sidebar overlay */
@media (max-width: 767px) {
  .mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
  }
  
  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .mobile-sidebar.open {
    transform: translateX(0);
  }
}

/* Prevent layout shift during transitions */
.layout-stable {
  contain: layout style;
}

/* Ensure proper text wrapping in cards */
.card-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Responsive typography */
@media (max-width: 767px) {
  .responsive-text-xl {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px) {
  .responsive-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .admin-transition,
  .admin-sidebar,
  .admin-main-content {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-sidebar {
    border-right: 2px solid;
  }
  
  .responsive-card {
    border: 1px solid;
  }
}

/* Focus management */
.admin-focusable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .admin-sidebar {
    display: none;
  }
  
  .admin-main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}