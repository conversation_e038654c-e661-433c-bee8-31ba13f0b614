"use client"

import { motion } from "framer-motion"
import { 
  Clock, 
  User, 
  <PERSON>R<PERSON>,
  CheckCircle,
  AlertTriangle,
  Calendar
} from "lucide-react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AssignmentRecord, AssignmentStatus } from "./types"
import { formatDate, formatDateTime } from "@/lib/utils"

interface AssetAssignmentHistoryProps {
  assetId: string
  assignments: AssignmentRecord[]
  loading?: boolean
}

export function AssetAssignmentHistory({ 
  assetId, 
  assignments, 
  loading = false 
}: AssetAssignmentHistoryProps) {
  const getStatusColor = (status: AssignmentStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'returned': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'lost': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getStatusIcon = (status: AssignmentStatus) => {
    switch (status) {
      case 'active': return <Clock className="w-4 h-4" />
      case 'returned': return <CheckCircle className="w-4 h-4" />
      case 'overdue': return <AlertTriangle className="w-4 h-4" />
      case 'lost': return <AlertTriangle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const getDuration = (assignedDate: string, returnedDate?: string) => {
    const start = new Date(assignedDate)
    const end = returnedDate ? new Date(returnedDate) : new Date()
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1 day'
    if (diffDays < 30) return `${diffDays} days`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months`
    return `${Math.floor(diffDays / 365)} years`
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Assignment History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Assignment History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {assignments.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <User className="w-12 h-12 mx-auto mb-4 opacity-50" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Assignment History
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              This asset has not been assigned to anyone yet.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {assignments.map((assignment, index) => (
              <motion.div
                key={assignment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="relative"
              >
                {/* Timeline connector */}
                {index < assignments.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-8 bg-gray-200 dark:bg-gray-700"></div>
                )}
                
                <div className="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                  {/* Status indicator */}
                  <div className={`p-2 rounded-full ${getStatusColor(assignment.status)}`}>
                    {getStatusIcon(assignment.status)}
                  </div>
                  
                  <div className="flex-1 space-y-3">
                    {/* Assignment details */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {assignment.assignedToName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">{assignment.assignedToName}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Assigned by {assignment.assignedByName}
                          </div>
                        </div>
                      </div>
                      
                      <Badge className={`${getStatusColor(assignment.status)} capitalize`}>
                        {assignment.status}
                      </Badge>
                    </div>

                    {/* Timeline */}
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(assignment.assignedDate)}</span>
                      {assignment.returnedDate && (
                        <>
                          <ArrowRight className="w-4 h-4" />
                          <span>{formatDate(assignment.returnedDate)}</span>
                        </>
                      )}
                      <span className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                        {getDuration(assignment.assignedDate, assignment.returnedDate)}
                      </span>
                    </div>

                    {/* Reason and notes */}
                    <div className="space-y-1">
                      <div className="text-sm">
                        <span className="font-medium">Reason:</span> {assignment.reason}
                      </div>
                      {assignment.notes && (
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Notes:</span> {assignment.notes}
                        </div>
                      )}
                    </div>

                    {/* Return details */}
                    {assignment.returnedDate && assignment.returnedByName && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-700">
                        Returned by {assignment.returnedByName} on {formatDateTime(assignment.returnedDate)}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
