"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  MoreHorizontal, 
  UserMinus, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  User
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Asset } from "./types"
import { formatDate } from "@/lib/utils"

interface AssetAssignmentsTableProps {
  assets: Asset[]
  loading: boolean
}

export function AssetAssignmentsTable({ assets, loading }: AssetAssignmentsTableProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  // Filter only assigned assets
  const assignedAssets = assets.filter(asset => asset.assignedTo && asset.assignedToName)
  
  const totalPages = Math.ceil(assignedAssets.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentAssets = assignedAssets.slice(startIndex, endIndex)

  const getAssignmentStatus = (asset: Asset) => {
    if (!asset.assignedDate) return 'unknown'
    
    const assignedDate = new Date(asset.assignedDate)
    const daysSinceAssigned = Math.floor((Date.now() - assignedDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysSinceAssigned > 365) return 'long-term'
    if (daysSinceAssigned > 90) return 'medium-term'
    return 'recent'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recent': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'medium-term': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'long-term': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'recent': return 'Recent'
      case 'medium-term': return 'Medium Term'
      case 'long-term': return 'Long Term'
      default: return 'Unknown'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Asset Assignments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Asset Assignments</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {assignedAssets.length} assigned
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Asset</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Assignment Date</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentAssets.map((asset, index) => {
                const assignmentStatus = getAssignmentStatus(asset)
                const daysSinceAssigned = asset.assignedDate 
                  ? Math.floor((Date.now() - new Date(asset.assignedDate).getTime()) / (1000 * 60 * 60 * 24))
                  : 0

                return (
                  <motion.tr
                    key={asset.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="group hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="text-lg">💻</div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {asset.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {asset.id}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {asset.assignedToName?.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">{asset.assignedToName}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            ID: {asset.assignedTo}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {asset.assignedDate ? formatDate(asset.assignedDate) : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {daysSinceAssigned > 0 ? `${daysSinceAssigned} days` : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={`capitalize ${getStatusColor(assignmentStatus)}`}>
                        {getStatusLabel(assignmentStatus)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{asset.location}</span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem className="gap-2">
                            <User className="w-4 h-4" />
                            View Assignment Details
                          </DropdownMenuItem>
                          <DropdownMenuItem className="gap-2">
                            <Clock className="w-4 h-4" />
                            Assignment History
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="gap-2 text-orange-600 dark:text-orange-400">
                            <UserMinus className="w-4 h-4" />
                            Return Asset
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                )
              })}
            </TableBody>
          </Table>
        </div>

        {assignedAssets.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <User className="w-12 h-12 mx-auto mb-4 opacity-50" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Asset Assignments
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              No assets are currently assigned to employees.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
