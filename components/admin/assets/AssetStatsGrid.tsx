"use client"

import { motion } from "framer-motion"
import { 
  Package, 
  DollarSign, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Shield,
  Wrench
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ResponsiveGrid, ResponsiveCard } from "../ResponsiveGrid"
import { AssetStats } from "./types"
import { formatCurrency, formatNumber } from "@/lib/utils"

interface AssetStatsGridProps {
  stats: AssetStats | null
  loading: boolean
}

export function AssetStatsGrid({ stats, loading }: AssetStatsGridProps) {
  if (loading) {
    return (
      <ResponsiveGrid cols={{ mobile: 1, tablet: 2, desktop: 4, wide: 5 }}>
        {Array.from({ length: 8 }).map((_, i) => (
          <ResponsiveCard key={i}>
            <Card className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          </ResponsiveCard>
        ))}
      </ResponsiveGrid>
    )
  }

  if (!stats) return null

  const statCards = [
    {
      title: "Total Assets",
      value: formatNumber(stats.totalAssets),
      icon: Package,
      color: "blue",
      trend: stats.newAssetsThisMonth > 0 ? "up" : "neutral",
      trendValue: `+${stats.newAssetsThisMonth} this month`,
      description: "All registered assets"
    },
    {
      title: "Total Value",
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: "green",
      trend: "neutral",
      description: "Current asset value"
    },
    {
      title: "Active Assets",
      value: formatNumber(stats.activeAssets),
      icon: CheckCircle,
      color: "emerald",
      percentage: Math.round((stats.activeAssets / stats.totalAssets) * 100),
      description: "Currently in use"
    },
    {
      title: "Available",
      value: formatNumber(stats.availableAssets),
      icon: Package,
      color: "blue",
      percentage: Math.round((stats.availableAssets / stats.totalAssets) * 100),
      description: "Ready for assignment"
    },
    {
      title: "Maintenance Required",
      value: formatNumber(stats.maintenanceRequired),
      icon: Wrench,
      color: stats.maintenanceRequired > 0 ? "orange" : "gray",
      urgent: stats.maintenanceOverdue,
      description: "Needs attention"
    },
    {
      title: "Warranty Expiring",
      value: formatNumber(stats.warrantyExpiring),
      icon: Shield,
      color: stats.warrantyExpiring > 0 ? "red" : "gray",
      description: "Within 30 days"
    },
    {
      title: "Average Age",
      value: `${stats.averageAssetAge.toFixed(1)} years`,
      icon: Clock,
      color: "purple",
      description: "Asset lifecycle"
    },
    {
      title: "Depreciation",
      value: formatCurrency(stats.depreciation),
      icon: TrendingDown,
      color: "red",
      trend: "down",
      description: "This year"
    }
  ]

  return (
    <ResponsiveGrid cols={{ mobile: 1, tablet: 2, desktop: 4, wide: 4 }}>
      {statCards.map((stat, index) => (
        <ResponsiveCard key={stat.title}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="asset-card relative overflow-hidden group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}>
                  <stat.icon className={`w-4 h-4 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-baseline justify-between">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stat.value}
                    </div>
                    {stat.trend && (
                      <div className="flex items-center gap-1">
                        {stat.trend === "up" && (
                          <TrendingUp className="w-3 h-3 text-green-500" />
                        )}
                        {stat.trend === "down" && (
                          <TrendingDown className="w-3 h-3 text-red-500" />
                        )}
                        {stat.trendValue && (
                          <span className={`text-xs ${
                            stat.trend === "up" ? "text-green-600" : "text-red-600"
                          }`}>
                            {stat.trendValue}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {stat.percentage !== undefined && (
                    <div className="space-y-1">
                      <Progress 
                        value={stat.percentage} 
                        className="h-2"
                      />
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {stat.percentage}% of total
                      </div>
                    </div>
                  )}

                  {stat.urgent && stat.urgent > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {stat.urgent} overdue
                    </Badge>
                  )}

                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {stat.description}
                  </p>
                </div>
              </CardContent>

              {/* Hover effect gradient */}
              <div className={`absolute inset-0 bg-gradient-to-r from-${stat.color}-500/5 to-${stat.color}-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none`} />
            </Card>
          </motion.div>
        </ResponsiveCard>
      ))}
    </ResponsiveGrid>
  )
}
