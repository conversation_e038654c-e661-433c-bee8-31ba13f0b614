"use client"

import React from 'react'
import { AssetProvider } from './context/AssetContext'
import { AssetManagementApp } from './components/AssetManagementApp'

/**
 * AssetsDashboard - Main entry point for the Asset Management System
 * Now uses proper component architecture with state management
 */
export function AssetsDashboard() {
  return (
    <AssetProvider>
      <AssetManagementApp />
    </AssetProvider>
  )
}





