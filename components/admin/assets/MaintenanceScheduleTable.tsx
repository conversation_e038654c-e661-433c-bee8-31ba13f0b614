"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  MoreHorizontal, 
  Wrench, 
  Calendar, 
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Plus
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Asset, MaintenanceRecord, MaintenanceStatus, Priority } from "./types"
import { formatDate, formatCurrency } from "@/lib/utils"

interface MaintenanceScheduleTableProps {
  assets: Asset[]
  loading: boolean
}

// Mock maintenance data
const mockMaintenanceRecords: MaintenanceRecord[] = [
  {
    id: "MNT001",
    assetId: "ASTD3458",
    type: "preventive",
    description: "Quarterly laptop maintenance and cleaning",
    scheduledDate: "2024-07-15",
    technician: "IT Support Team",
    cost: 50,
    status: "scheduled",
    priority: "normal",
    notes: "Regular maintenance check",
    createdAt: "2024-07-01T10:00:00Z",
    createdBy: "admin"
  },
  {
    id: "MNT002",
    assetId: "ASTD3458",
    type: "corrective",
    description: "Vehicle brake inspection and service",
    scheduledDate: "2024-06-28",
    completedDate: "2024-06-28",
    technician: "Auto Service Center",
    cost: 350,
    status: "overdue",
    priority: "high",
    notes: "Brake pads replacement needed",
    createdAt: "2024-06-20T09:00:00Z",
    createdBy: "admin"
  }
]

export function MaintenanceScheduleTable({ assets, loading }: MaintenanceScheduleTableProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  const totalPages = Math.ceil(mockMaintenanceRecords.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentRecords = mockMaintenanceRecords.slice(startIndex, endIndex)

  const getStatusColor = (status: MaintenanceStatus) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'in-progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getPriorityColor = (priority: Priority) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
      case 'normal': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getAssetName = (assetId: string) => {
    const asset = assets.find(a => a.id === assetId)
    return asset ? asset.name : `Asset ${assetId}`
  }

  const getMaintenanceIcon = (type: string) => {
    switch (type) {
      case 'preventive': return <Calendar className="w-4 h-4" />
      case 'corrective': return <Wrench className="w-4 h-4" />
      case 'emergency': return <AlertTriangle className="w-4 h-4" />
      default: return <Wrench className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Maintenance Schedule</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {mockMaintenanceRecords.length} records
            </Badge>
            <Button size="sm" className="gap-2">
              <Plus className="w-4 h-4" />
              Schedule Maintenance
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Asset</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Scheduled Date</TableHead>
                <TableHead>Technician</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentRecords.map((record, index) => (
                <motion.tr
                  key={record.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="group hover:bg-gray-50 dark:hover:bg-gray-800/50"
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="text-lg">💻</div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {getAssetName(record.assetId)}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {record.assetId}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getMaintenanceIcon(record.type)}
                      <Badge variant="outline" className="capitalize">
                        {record.type}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {record.description}
                      </div>
                      {record.notes && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {record.notes}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(record.scheduledDate)}
                    </div>
                    {record.completedDate && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Completed: {formatDate(record.completedDate)}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarFallback className="text-xs">
                          {record.technician?.split(' ').map(n => n[0]).join('') || 'T'}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{record.technician || 'Unassigned'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={`capitalize ${getPriorityColor(record.priority)}`}>
                      {record.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={`capitalize ${getStatusColor(record.status)}`}>
                      {record.status.replace('-', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {record.cost ? formatCurrency(record.cost) : 'TBD'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem className="gap-2">
                          <Calendar className="w-4 h-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <Wrench className="w-4 h-4" />
                          Edit Maintenance
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <User className="w-4 h-4" />
                          Assign Technician
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {record.status === 'scheduled' && (
                          <DropdownMenuItem className="gap-2 text-green-600 dark:text-green-400">
                            <CheckCircle className="w-4 h-4" />
                            Mark Complete
                          </DropdownMenuItem>
                        )}
                        {record.status !== 'cancelled' && (
                          <DropdownMenuItem className="gap-2 text-red-600 dark:text-red-400">
                            <AlertTriangle className="w-4 h-4" />
                            Cancel Maintenance
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </motion.tr>
              ))}
            </TableBody>
          </Table>
        </div>

        {mockMaintenanceRecords.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <Wrench className="w-12 h-12 mx-auto mb-4 opacity-50" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Maintenance Records
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              No maintenance activities have been scheduled yet.
            </p>
            <Button className="gap-2">
              <Plus className="w-4 h-4" />
              Schedule First Maintenance
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
