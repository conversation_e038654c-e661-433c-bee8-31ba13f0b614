# Asset Management System

A comprehensive, enterprise-grade asset management system built with React, TypeScript, and Tailwind CSS. This system provides complete asset lifecycle management including inventory tracking, assignments, maintenance scheduling, and reporting.

## 🚀 Features

### Core Functionality
- **Asset Inventory Management** - Complete CRUD operations for assets
- **Assignment Tracking** - Assign assets to employees, departments, or projects
- **Maintenance Scheduling** - Preventive and corrective maintenance management
- **Reporting & Analytics** - Comprehensive dashboards and reports
- **Employee Portal** - Self-service portal for employees

### Technical Features
- **TypeScript** - Full type safety and IntelliSense support
- **Responsive Design** - Mobile-first approach with adaptive layouts
- **Accessibility** - WCAG 2.1 compliant with keyboard navigation
- **State Management** - Context API with custom hooks
- **Component Architecture** - Modular, reusable components
- **Form Validation** - Comprehensive client-side validation
- **Loading States** - Skeleton loaders and loading indicators
- **Error Handling** - Graceful error states and user feedback

## 📁 Project Structure

```
components/admin/assets/
├── AssetsDashboard.tsx          # Main entry point
├── README.md                    # This file
├── components/
│   ├── index.ts                 # Component exports
│   ├── AssetManagementApp.tsx   # Main application component
│   ├── ui/                      # Reusable UI components
│   │   ├── StatsCard.tsx        # Statistics display cards
│   │   ├── NavigationTabs.tsx   # Tab navigation component
│   │   ├── DataTable.tsx        # Data table with sorting/pagination
│   │   └── ActionButton.tsx     # Button component with variants
│   ├── views/                   # Main view components
│   │   ├── DashboardView.tsx    # Dashboard overview
│   │   ├── InventoryView.tsx    # Asset inventory management
│   │   ├── AssignmentsView.tsx  # Assignment tracking
│   │   ├── MaintenanceView.tsx  # Maintenance scheduling
│   │   ├── ReportsView.tsx      # Analytics and reporting
│   │   └── EmployeePortalView.tsx # Employee self-service
│   └── dialogs/                 # Modal dialog components
│       ├── AddAssetDialog.tsx   # Add new asset form
│       ├── CreateAssignmentDialog.tsx # Create assignment form
│       ├── ScheduleMaintenanceDialog.tsx # Schedule maintenance form
│       └── RequestRepairDialog.tsx # Request repair form
├── context/
│   └── AssetContext.tsx         # Global state management
├── hooks/
│   └── useAssetManagement.ts    # Custom hook for asset operations
└── types/
    └── index.ts                 # TypeScript type definitions
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ 
- React 18+
- TypeScript 4.9+
- Tailwind CSS 3+

### Dependencies
```json
{
  "dependencies": {
    "react": "^18.0.0",
    "lucide-react": "^0.263.1",
    "@radix-ui/react-dialog": "^1.0.4"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0"
  }
}
```

### Usage
```tsx
import { AssetsDashboard } from '@/components/admin/assets/AssetsDashboard'

function App() {
  return <AssetsDashboard />
}
```

## 🎨 Component API

### AssetsDashboard
Main entry point that provides the complete asset management system.

```tsx
<AssetsDashboard />
```

### Individual Components
```tsx
import { 
  StatsCard, 
  DataTable, 
  NavigationTabs,
  DashboardView,
  InventoryView 
} from '@/components/admin/assets/components'

// Stats Card
<StatsCard
  title="Total Assets"
  value={1247}
  subtitle="+12% from last month"
  trend={{ value: 12, direction: 'up', label: 'from last month' }}
  variant="success"
/>

// Data Table
<DataTable
  data={assets}
  columns={columns}
  loading={false}
  sortable
  selectable
  onRowClick={handleRowClick}
/>

// Navigation Tabs
<NavigationTabs
  tabs={tabItems}
  activeTab="dashboard"
  onTabChange={handleTabChange}
/>
```

## 🔧 State Management

The system uses React Context API for state management with the following structure:

```tsx
interface AppState {
  assets: AssetState
  assignments: AssignmentState
  maintenance: MaintenanceState
  ui: UIState
  user: UserState
}
```

### Using the Hook
```tsx
import { useAssetManagement } from '@/components/admin/assets/hooks/useAssetManagement'

function MyComponent() {
  const {
    state,
    stats,
    filteredAssets,
    addAsset,
    createAssignment,
    scheduleMaintenance
  } = useAssetManagement()

  // Use the state and actions
}
```

## 📱 Responsive Design

The system is built mobile-first with responsive breakpoints:

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: 1024px+

### Mobile Features
- Collapsible navigation
- Card-based layouts for tables
- Touch-friendly interactions
- Optimized form layouts

## ♿ Accessibility

- **WCAG 2.1 AA** compliant
- **Keyboard navigation** support
- **Screen reader** optimized
- **Focus management** in modals
- **ARIA labels** and descriptions
- **Color contrast** compliance

### Keyboard Shortcuts
- `Tab` - Navigate between elements
- `Enter/Space` - Activate buttons
- `Escape` - Close modals
- `Arrow Keys` - Navigate tabs

## 🎯 TypeScript Support

Comprehensive TypeScript definitions for all components and data structures:

```tsx
interface Asset {
  id: string
  name: string
  type: AssetType
  status: AssetStatus
  condition: AssetCondition
  // ... more properties
}

interface AssetFormData {
  name: string
  type: AssetType
  purchaseCost: number
  // ... more properties
}
```

## 🧪 Testing

The components are designed to be testable with proper data-testid attributes:

```tsx
// Example test
import { render, screen } from '@testing-library/react'
import { AssetsDashboard } from './AssetsDashboard'

test('renders dashboard', () => {
  render(<AssetsDashboard />)
  expect(screen.getByTestId('asset-dashboard')).toBeInTheDocument()
})
```

## 🔄 Data Flow

1. **Context Provider** wraps the application
2. **Custom Hook** provides state and actions
3. **Components** consume state via hook
4. **Actions** update state through reducer
5. **UI** re-renders based on state changes

## 🎨 Theming

The system uses Tailwind CSS with a consistent design system:

### Colors
- **Primary**: Blue (600, 700, 800)
- **Success**: Green (600, 700, 800)
- **Warning**: Yellow (600, 700, 800)
- **Error**: Red (600, 700, 800)
- **Gray Scale**: Gray (50-900)

### Typography
- **Headings**: font-semibold, font-bold
- **Body**: font-normal, font-medium
- **Sizes**: text-xs to text-3xl

## 🚀 Performance

- **Code Splitting** - Lazy loading of components
- **Memoization** - React.memo for expensive components
- **Virtual Scrolling** - For large data sets
- **Optimistic Updates** - Immediate UI feedback
- **Skeleton Loading** - Better perceived performance

## 🔮 Future Enhancements

- [ ] Real-time notifications
- [ ] Advanced filtering and search
- [ ] Bulk operations
- [ ] File attachments
- [ ] Audit trail
- [ ] Integration APIs
- [ ] Mobile app
- [ ] Offline support

## 📄 License

This component system is part of the larger application and follows the same licensing terms.

## 🤝 Contributing

1. Follow the established component patterns
2. Maintain TypeScript strict mode compliance
3. Add proper accessibility attributes
4. Include responsive design considerations
5. Write comprehensive JSDoc comments
6. Add data-testid attributes for testing

## 📞 Support

For questions or issues related to the Asset Management System, please refer to the main application documentation or contact the development team.
