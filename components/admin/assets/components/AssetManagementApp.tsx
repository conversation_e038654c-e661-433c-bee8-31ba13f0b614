/**
 * AssetManagementApp Component
 * Main application component that orchestrates all views and dialogs
 */

import React from 'react'
import { TabNavigation } from '@/components/admin/shared/ui'
import { DashboardView } from './views/DashboardView'
import { InventoryView } from './views/InventoryView'
import { AssignmentsView } from './views/AssignmentsView'
import { MaintenanceView } from './views/MaintenanceView'
import { ReportsView } from './views/ReportsView'
import { EmployeePortalView } from './views/EmployeePortalView'
import { AddAssetDialog } from './dialogs/AddAssetDialog'
import { CreateAssignmentDialog } from './dialogs/CreateAssignmentDialog'
import { ScheduleMaintenanceDialog } from './dialogs/ScheduleMaintenanceDialog'
import { RequestRepairDialog } from './dialogs/RequestRepairDialog'
import { useAssetManagement } from '../hooks/useAssetManagement'
import { TabItem, TabType } from '../types'
import { Assignment } from '../types/index'

import {
  LayoutDashboard,
  Package,
  Users,
  Wrench,
  FileText,
  UserCheck,
  Search,
  Bell
} from 'lucide-react'

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Main Asset Management Application
 */
export const AssetManagementApp: React.FC = () => {
  const {
    state,
    stats,
    filteredAssets,
    availableAssets,
    overdueAssignments,
    overdueMaintenance,
    unreadNotifications,
    setActiveTab,
    addAsset,
    createAssignment,
    scheduleMaintenance,
    setAssetSearch,
    setAssetFilters,
    openDialog,
    closeDialog,
    addNotification
  } = useAssetManagement()

  // Tab configuration
  const tabs: TabItem[] = [
    {
      id: 'dashboard' as TabType,
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-4 h-4" />
    },
    {
      id: 'inventory' as TabType,
      label: 'Inventory',
      icon: <Package className="w-4 h-4" />,
      badge: filteredAssets.length
    },
    {
      id: 'assignments' as TabType,
      label: 'Assignments',
      icon: <Users className="w-4 h-4" />,
      badge: overdueAssignments.length,
      badgeColor: overdueAssignments.length > 0 ? 'error' : 'default'
    },
    {
      id: 'maintenance' as TabType,
      label: 'Maintenance',
      icon: <Wrench className="w-4 h-4" />,
      badge: overdueMaintenance.length,
      badgeColor: overdueMaintenance.length > 0 ? 'error' : 'default'
    },
    {
      id: 'reports' as TabType,
      label: 'Reports',
      icon: <FileText className="w-4 h-4" />
    },
    {
      id: 'employeeportal' as TabType,
      label: 'Employee Portal',
      icon: <UserCheck className="w-4 h-4" />
    }
  ]

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as TabType)
  }

  // Handle asset form submission
  const handleAddAsset = async (data: any) => {
    try {
      await addAsset(data)
      addNotification({
        type: 'success',
        title: 'Asset Added',
        message: `Asset "${data.name}" has been successfully added to inventory.`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to add asset. Please try again.'
      })
    }
  }

  // Handle assignment form submission
  const handleCreateAssignment = async (data: any) => {
    try {
      await createAssignment(data)
      addNotification({
        type: 'success',
        title: 'Assignment Created',
        message: `Asset has been successfully assigned to ${data.assigneeName}.`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to create assignment. Please try again.'
      })
    }
  }

  // Handle maintenance form submission
  const handleScheduleMaintenance = async (data: any) => {
    try {
      await scheduleMaintenance(data)
      addNotification({
        type: 'success',
        title: 'Maintenance Scheduled',
        message: `Maintenance has been scheduled for ${data.scheduledDate}.`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to schedule maintenance. Please try again.'
      })
    }
  }

  // Handle repair request submission
  const handleRequestRepair = async (data: any) => {
    try {
      // Convert repair request to maintenance record
      const maintenanceData = {
        assetId: data.assetId,
        type: 'corrective' as const,
        frequency: 'one-time' as const,
        scheduledDate: new Date().toISOString().split('T')[0],
        technicianName: 'To be assigned',
        priority: data.priority,
        estimatedCost: data.estimatedCost,
        description: data.problemDescription,
        notes: `Repair request by: ${data.requestedBy}. Urgency: ${data.urgency}`,
        warrantyStatus: 'unknown' as const
      }
      
      await scheduleMaintenance(maintenanceData)
      addNotification({
        type: 'success',
        title: 'Repair Request Submitted',
        message: 'Your repair request has been submitted and will be reviewed shortly.'
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to submit repair request. Please try again.'
      })
    }
  }

  // Render current view
  const renderCurrentView = () => {
    switch (state.ui.activeTab) {
      case 'dashboard':
        return (
          <DashboardView
            stats={stats}
            loading={state.assets.loading}
            error={state.assets.error}
          />
        )
      
      case 'inventory':
        return (
          <InventoryView
            assets={state.assets.items}
            loading={state.assets.loading}
            error={state.assets.error}
            searchQuery={state.assets.searchQuery}
            filters={state.assets.filters}
            onSearch={setAssetSearch}
            onFilterChange={setAssetFilters}
            onAddAsset={() => openDialog('addAsset')}
            onEditAsset={(asset) => {
              // TODO: Implement edit functionality
              console.log('Edit asset:', asset)
            }}
            onDeleteAsset={(asset) => {
              // TODO: Implement delete functionality
              console.log('Delete asset:', asset)
            }}
            onViewAsset={(asset) => {
              // TODO: Implement view functionality
              console.log('View asset:', asset)
            }}
            onImport={() => {
              // TODO: Implement import functionality
              console.log('Import assets')
            }}
            onExport={() => {
              // TODO: Implement export functionality
              console.log('Export assets')
            }}
          />
        )
      
      case 'assignments':
        return (
          <AssignmentsView
            assignments={state.assignments.items}
            assets={state.assets.items}
            loading={state.assignments.loading}
            error={state.assignments.error}
            onCreateAssignment={() => openDialog('createAssignment')}
            onViewAssignment={(assignment) => {
              // TODO: Implement view functionality
              console.log('View assignment:', assignment)
            }}
            onReturnAsset={(assignment) => {
              // TODO: Implement return functionality
              console.log('Return asset:', assignment)
            }}
            onEditAssignment={(assignment) => {
              // TODO: Implement edit functionality
              console.log('Edit assignment:', assignment)
            }}
          />
        )
      
      case 'maintenance':
        return (
          <MaintenanceView
            maintenanceRecords={state.maintenance.items}
            assets={state.assets.items}
            loading={state.maintenance.loading}
            error={state.maintenance.error}
            onScheduleMaintenance={() => openDialog('scheduleMaintenance')}
            onRequestRepair={() => openDialog('requestRepair')}
            onViewMaintenance={(record) => {
              // TODO: Implement view functionality
              console.log('View maintenance:', record)
            }}
            onCompleteMaintenance={(record) => {
              // TODO: Implement complete functionality
              console.log('Complete maintenance:', record)
            }}
            onCancelMaintenance={(record) => {
              // TODO: Implement cancel functionality
              console.log('Cancel maintenance:', record)
            }}
          />
        )
      
      case 'reports':
        return (
          <ReportsView
            stats={stats}
            loading={state.assets.loading || state.assignments.loading || state.maintenance.loading}
            error={state.assets.error || state.assignments.error || state.maintenance.error}
            onExport={(format) => {
              // TODO: Implement export functionality
              console.log('Export reports as:', format)
              addNotification({
                type: 'info',
                title: 'Export Started',
                message: `Generating ${format.toUpperCase()} report...`
              })
            }}
          />
        )
      
      case 'employeeportal':
        return (
          <EmployeePortalView
            userAssignments={state.assignments.items.filter((a: Assignment) => a.assigneeId === state.user.id)}
            availableAssets={availableAssets}
            loading={state.assignments.loading}
            error={state.assignments.error}
            onRequestAsset={() => openDialog('createAssignment')}
            onReportIssue={(assignment) => {
              // TODO: Implement report issue functionality
              console.log('Report issue for assignment:', assignment)
              addNotification({
                type: 'info',
                title: 'Issue Reported',
                message: 'Your issue has been reported and will be reviewed.'
              })
            }}
            onReturnAsset={(assignment) => {
              // TODO: Implement return asset functionality
              console.log('Return asset:', assignment)
              addNotification({
                type: 'success',
                title: 'Return Initiated',
                message: 'Asset return process has been started.'
              })
            }}
            onViewAssignment={(assignment) => {
              // TODO: Implement view assignment functionality
              console.log('View assignment:', assignment)
            }}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className="">
      

      {/* Content */}
      <div className="">
        {/* Navigation Tabs */}
        <div className="mb-6">
          <TabNavigation
            tabs={tabs}
            activeTab={state.ui.activeTab}
            onTabChange={handleTabChange}
            size="md"
            variant="default"
            // fullWidth={false}
            // scrollable={true}
            loading={state.assets.loading || state.assignments.loading || state.maintenance.loading}
          />
        </div>

        {/* Current View */}
        {renderCurrentView()}
      </div>

      {/* Dialogs */}
      <AddAssetDialog
        open={state.ui.dialogs.addAsset}
        onOpenChange={(open) => open ? openDialog('addAsset') : closeDialog('addAsset')}
        onSubmit={handleAddAsset}
        loading={state.assets.loading}
      />

      <CreateAssignmentDialog
        open={state.ui.dialogs.createAssignment}
        onOpenChange={(open) => open ? openDialog('createAssignment') : closeDialog('createAssignment')}
        onSubmit={handleCreateAssignment}
        assets={availableAssets}
        loading={state.assignments.loading}
      />

      <ScheduleMaintenanceDialog
        open={state.ui.dialogs.scheduleMaintenance}
        onOpenChange={(open) => open ? openDialog('scheduleMaintenance') : closeDialog('scheduleMaintenance')}
        onSubmit={handleScheduleMaintenance}
        assets={state.assets.items}
        loading={state.maintenance.loading}
      />

      <RequestRepairDialog
        open={state.ui.dialogs.requestRepair}
        onOpenChange={(open) => open ? openDialog('requestRepair') : closeDialog('requestRepair')}
        onSubmit={handleRequestRepair}
        assets={state.assets.items}
        loading={state.maintenance.loading}
      />
    </div>
  )
}

export default AssetManagementApp
