/**
 * AddAssetDialog Component
 * Dialog for adding new assets with form validation and mobile responsiveness
 */

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, AssetFormData, AssetType, AssetCondition } from '../../types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { X } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface AddAssetDialogProps extends BaseComponentProps {
  /** Whether dialog is open */
  open: boolean
  /** Dialog open/close handler */
  onOpenChange: (open: boolean) => void
  /** Form submission handler */
  onSubmit: (data: AssetFormData) => Promise<void> | void
  /** Loading state */
  loading?: boolean
  /** Initial data for editing */
  initialData?: Partial<AssetFormData>
  /** Dialog title */
  title?: string
  /** Submit button text */
  submitText?: string
}

// ============================================================================
// VALIDATION
// ============================================================================

interface ValidationErrors {
  [key: string]: string
}

const validateForm = (data: AssetFormData): ValidationErrors => {
  const errors: ValidationErrors = {}

  if (!data.name.trim()) {
    errors.name = 'Asset name is required'
  }

  if (!data.type) {
    errors.type = 'Asset type is required'
  }

  if (!data.purchaseCost || data.purchaseCost <= 0) {
    errors.purchaseCost = 'Purchase cost must be greater than 0'
  }

  if (!data.vendor.trim()) {
    errors.vendor = 'Vendor is required'
  }

  if (!data.serialNumber.trim()) {
    errors.serialNumber = 'Serial number is required'
  }

  if (!data.location.trim()) {
    errors.location = 'Location is required'
  }

  return errors
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * AddAssetDialog component for creating new assets
 */
export const AddAssetDialog: React.FC<AddAssetDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  loading = false,
  initialData = {},
  title = 'Add New Asset',
  submitText = 'Add Asset',
  className,
  'data-testid': testId,
}) => {
  // Form state
  const [formData, setFormData] = useState<AssetFormData>({
    name: initialData.name || '',
    type: initialData.type || 'laptop',
    purchaseCost: initialData.purchaseCost || 0,
    vendor: initialData.vendor || '',
    serialNumber: initialData.serialNumber || '',
    location: initialData.location || '',
    model: initialData.model || '',
    category: initialData.category || '',
    warrantyExpiry: initialData.warrantyExpiry || '',
    condition: initialData.condition || 'good',
    notes: initialData.notes || ''
  })

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Handle input change
  const handleInputChange = (field: keyof AssetFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const validationErrors = validateForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      onOpenChange(false)
      // Reset form
      setFormData({
        name: '',
        type: 'laptop',
        purchaseCost: 0,
        vendor: '',
        serialNumber: '',
        location: '',
        model: '',
        category: '',
        warrantyExpiry: '',
        condition: 'good',
        notes: ''
      })
      setErrors({})
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
      setErrors({})
    }
  }

  // Asset type options
  const assetTypes: { value: AssetType; label: string }[] = [
    { value: 'laptop', label: 'Laptop' },
    { value: 'desktop', label: 'Desktop' },
    { value: 'mobile-device', label: 'Mobile Device' },
    { value: 'tablet', label: 'Tablet' },
    { value: 'furniture', label: 'Furniture' },
    { value: 'vehicle', label: 'Vehicle' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'software', label: 'Software' },
    { value: 'other', label: 'Other' }
  ]

  // Asset condition options
  const conditionOptions: { value: AssetCondition; label: string }[] = [
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
    { value: 'damaged', label: 'Damaged' }
  ]

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn('sm:max-w-[500px] max-h-[90vh] overflow-y-auto', className)} data-testid={testId}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <p className="text-sm text-gray-600">Enter the details for the new asset</p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Asset Name and Type */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Asset Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter asset name"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.name ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value as AssetType)}
                className={cn(
                  'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.type ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              >
                {assetTypes.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type}</p>
              )}
            </div>
          </div>

          {/* Purchase Cost and Vendor */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Purchase Cost *
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.purchaseCost || ''}
                onChange={(e) => handleInputChange('purchaseCost', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.purchaseCost ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.purchaseCost && (
                <p className="mt-1 text-sm text-red-600">{errors.purchaseCost}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vendor *
              </label>
              <input
                type="text"
                value={formData.vendor}
                onChange={(e) => handleInputChange('vendor', e.target.value)}
                placeholder="Vendor Name"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.vendor ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.vendor && (
                <p className="mt-1 text-sm text-red-600">{errors.vendor}</p>
              )}
            </div>
          </div>

          {/* Serial Number and Location */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Serial Number *
              </label>
              <input
                type="text"
                value={formData.serialNumber}
                onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                placeholder="Serial Number"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.serialNumber ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.serialNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.serialNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location *
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="Asset Location"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.location ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600">{errors.location}</p>
              )}
            </div>
          </div>

          {/* Model and Category */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <input
                type="text"
                value={formData.model}
                onChange={(e) => handleInputChange('model', e.target.value)}
                placeholder="Asset Model"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                placeholder="Asset Category"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Warranty Expiry and Condition */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Warranty Expiry
              </label>
              <input
                type="date"
                value={formData.warrantyExpiry}
                onChange={(e) => handleInputChange('warrantyExpiry', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Condition
              </label>
              <select
                value={formData.condition}
                onChange={(e) => handleInputChange('condition', e.target.value as AssetCondition)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              >
                {conditionOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about the asset..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              disabled={isSubmitting}
            />
          </div>

          {/* Form Actions */}
          <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 pt-4">
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </ActionButton>
            <ActionButton
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {submitText}
            </ActionButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default AddAssetDialog
