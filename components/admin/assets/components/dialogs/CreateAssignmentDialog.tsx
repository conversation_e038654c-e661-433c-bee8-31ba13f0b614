/**
 * CreateAssignmentDialog Component
 * Dialog for creating new asset assignments matching the screenshot exactly
 */

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, AssignmentFormData, Asset, AssignmentType } from '../../types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { X, Calendar } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface CreateAssignmentDialogProps extends BaseComponentProps {
  /** Whether dialog is open */
  open: boolean
  /** Dialog open/close handler */
  onOpenChange: (open: boolean) => void
  /** Form submission handler */
  onSubmit: (data: AssignmentFormData) => Promise<void> | void
  /** Available assets */
  assets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Pre-selected asset */
  selectedAsset?: Asset
}

// ============================================================================
// VALIDATION
// ============================================================================

interface ValidationErrors {
  [key: string]: string
}

const validateForm = (data: AssignmentFormData): ValidationErrors => {
  const errors: ValidationErrors = {}

  if (!data.assetId) {
    errors.assetId = 'Please select an asset'
  }

  if (!data.assigneeName.trim()) {
    errors.assigneeName = 'Assignee name is required'
  }

  if (!data.type) {
    errors.type = 'Assignment type is required'
  }

  if (!data.purpose?.trim()) {
    errors.purpose = 'Purpose is required'
  }

  if (data.purpose && data.purpose.length > 200) {
    errors.purpose = 'Purpose must be 200 characters or less'
  }

  return errors
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * CreateAssignmentDialog component for creating new assignments
 */
export const CreateAssignmentDialog: React.FC<CreateAssignmentDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  assets = [],
  loading = false,
  selectedAsset,
  className,
  'data-testid': testId,
}) => {
  // Form state
  const [formData, setFormData] = useState<AssignmentFormData>({
    assetId: selectedAsset?.id || '',
    assigneeId: '',
    assigneeName: '',
    type: 'employee',
    purpose: '',
    returnDate: '',
    notes: ''
  })

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Handle input change
  const handleInputChange = (field: keyof AssignmentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Auto-generate assigneeId from name
    if (field === 'assigneeName') {
      const id = value.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
      setFormData(prev => ({ ...prev, assigneeId: id }))
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const validationErrors = validateForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      onOpenChange(false)
      // Reset form
      setFormData({
        assetId: '',
        assigneeId: '',
        assigneeName: '',
        type: 'employee',
        purpose: '',
        returnDate: '',
        notes: ''
      })
      setErrors({})
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
      setErrors({})
    }
  }

  // Get available assets (only unassigned ones)
  const availableAssets = assets.filter(asset => asset.status === 'available')

  // Assignment type options
  const assignmentTypes: { value: AssignmentType; label: string }[] = [
    { value: 'employee', label: 'Employee' },
    { value: 'department', label: 'Department' },
    { value: 'project', label: 'Project' },
    { value: 'temporary', label: 'Temporary' }
  ]

  // Get selected asset details
  const selectedAssetDetails = assets.find(asset => asset.id === formData.assetId)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn('sm:max-w-[500px] max-h-[90vh] overflow-y-auto', className)} data-testid={testId}>
        <DialogHeader>
          <DialogTitle>Create New Assignment</DialogTitle>
          <p className="text-sm text-gray-600">Assign an asset to an employee or department</p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Asset Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Asset *
            </label>
            <select
              value={formData.assetId}
              onChange={(e) => handleInputChange('assetId', e.target.value)}
              className={cn(
                'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.assetId ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting || !!selectedAsset}
            >
              <option value="">Select asset</option>
              {availableAssets.map(asset => (
                <option key={asset.id} value={asset.id}>
                  {asset.name} ({asset.id})
                </option>
              ))}
            </select>
            {errors.assetId && (
              <p className="mt-1 text-sm text-red-600">{errors.assetId}</p>
            )}
            
            {/* Selected Asset Details */}
            {selectedAssetDetails && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{selectedAssetDetails.name}</div>
                  <div className="text-gray-600">
                    {selectedAssetDetails.model} • {selectedAssetDetails.location}
                  </div>
                  <div className="text-gray-500">
                    Serial: {selectedAssetDetails.serialNumber}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Assignee */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assignee *
            </label>
            <input
              type="text"
              value={formData.assigneeName}
              onChange={(e) => handleInputChange('assigneeName', e.target.value)}
              placeholder="Employee name or department"
              className={cn(
                'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.assigneeName ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            />
            {errors.assigneeName && (
              <p className="mt-1 text-sm text-red-600">{errors.assigneeName}</p>
            )}
          </div>

          {/* Assignment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type *
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value as AssignmentType)}
              className={cn(
                'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.type ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            >
              {assignmentTypes.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="mt-1 text-sm text-red-600">{errors.type}</p>
            )}
          </div>

          {/* Purpose */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Purpose *
            </label>
            <textarea
              value={formData.purpose}
              onChange={(e) => handleInputChange('purpose', e.target.value)}
              placeholder="Assignment purpose (max 200 characters)"
              rows={3}
              maxLength={200}
              className={cn(
                'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none',
                errors.purpose ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.purpose ? (
                <p className="text-sm text-red-600">{errors.purpose}</p>
              ) : (
                <div />
              )}
              <p className="text-xs text-gray-500">
                {formData.purpose?.length || 0}/200 characters
              </p>
            </div>
          </div>

          {/* Return Date (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Return Date (Optional)
            </label>
            <div className="relative">
              <input
                type="date"
                value={formData.returnDate}
                onChange={(e) => handleInputChange('returnDate', e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Leave empty for permanent assignment
            </p>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about the assignment..."
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              disabled={isSubmitting}
            />
          </div>

          {/* Form Actions */}
          <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 pt-4">
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </ActionButton>
            <ActionButton
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting || availableAssets.length === 0}
            >
              Create assignment
            </ActionButton>
          </div>

          {/* No assets available message */}
          {availableAssets.length === 0 && (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">
                No available assets to assign. All assets are currently assigned or unavailable.
              </p>
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateAssignmentDialog
