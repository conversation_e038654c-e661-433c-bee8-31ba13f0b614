/**
 * RequestRepairDialog Component
 * Dialog for requesting asset repairs matching the screenshot exactly
 */

import React, { useState, useRef } from 'react'
import { cn } from '@/lib/utils'
import { ActionButton } from '../ui/ActionButton'
import { Asset, RepairRequestFormData } from '../../types'
import { BaseComponentProps, PriorityLevel, UrgencyLevel } from '../../types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { X, Upload, FileText } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface RequestRepairDialogProps extends BaseComponentProps {
  /** Whether dialog is open */
  open: boolean
  /** Dialog open/close handler */
  onOpenChange: (open: boolean) => void
  /** Form submission handler */
  onSubmit: (data: RepairRequestFormData) => Promise<void> | void
  /** Available assets */
  assets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Pre-selected asset */
  selectedAsset?: Asset
}

// ============================================================================
// VALIDATION
// ============================================================================

interface ValidationErrors {
  [key: string]: string
}

const validateForm = (data: RepairRequestFormData): ValidationErrors => {
  const errors: ValidationErrors = {}

  if (!data.assetId) {
    errors.assetId = 'Please select an asset'
  }

  if (!data.requestedBy.trim()) {
    errors.requestedBy = 'Your name is required'
  }

  if (!data.problemDescription.trim()) {
    errors.problemDescription = 'Problem description is required'
  }

  if (data.problemDescription.length > 1000) {
    errors.problemDescription = 'Problem description must be 1000 characters or less'
  }

  if (data.estimatedCost && data.estimatedCost < 0) {
    errors.estimatedCost = 'Estimated cost cannot be negative'
  }

  return errors
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * RequestRepairDialog component for requesting repairs
 */
export const RequestRepairDialog: React.FC<RequestRepairDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  assets = [],
  loading = false,
  selectedAsset,
  className,
  'data-testid': testId,
}) => {
  // Form state
  const [formData, setFormData] = useState<RepairRequestFormData>({
    assetId: selectedAsset?.id || '',
    requestedBy: '',
    priority: 'normal',
    estimatedCost: 0,
    problemDescription: '',
    attachments: [],
    urgency: 'normal'
  })

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle input change
  const handleInputChange = (field: keyof RepairRequestFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    const validFiles = files.filter(file => {
      const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'].includes(file.type)
      const isValidSize = file.size <= 10 * 1024 * 1024 // 10MB
      return isValidType && isValidSize
    })

    setSelectedFiles(prev => [...prev, ...validFiles])
    setFormData(prev => ({ ...prev, attachments: [...prev.attachments || [], ...validFiles] }))
  }

  // Remove file
  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
    setFormData(prev => ({ 
      ...prev, 
      attachments: prev.attachments?.filter((_, i) => i !== index) || []
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const validationErrors = validateForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      onOpenChange(false)
      // Reset form
      setFormData({
        assetId: '',
        requestedBy: '',
        priority: 'normal',
        estimatedCost: 0,
        problemDescription: '',
        attachments: [],
        urgency: 'normal'
      })
      setSelectedFiles([])
      setErrors({})
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
      setErrors({})
    }
  }

  // Get selected asset details
  const selectedAssetDetails = assets.find(asset => asset.id === formData.assetId)

  // Options
  const priorities: { value: PriorityLevel; label: string }[] = [
    { value: 'low', label: 'Low priority' },
    { value: 'normal', label: 'Normal priority' },
    { value: 'high', label: 'High priority' },
    { value: 'critical', label: 'Critical priority' }
  ]

  const urgencyLevels: { value: UrgencyLevel; label: string }[] = [
    { value: 'normal', label: 'Normal' },
    { value: 'urgent', label: 'Urgent' },
    { value: 'critical', label: 'Critical' }
  ]

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn('sm:max-w-[500px] max-h-[90vh] overflow-y-auto', className)} data-testid={testId}>
        <DialogHeader>
          <DialogTitle>Request Repair</DialogTitle>
          <p className="text-sm text-gray-600">Submit a repair request for an asset</p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Asset Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assignee
            </label>
            <select
              value={formData.assetId}
              onChange={(e) => handleInputChange('assetId', e.target.value)}
              className={cn(
                'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.assetId ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting || !!selectedAsset}
            >
              <option value="">Select asset needing repair</option>
              {assets.map(asset => (
                <option key={asset.id} value={asset.id}>
                  {asset.name} ({asset.id})
                </option>
              ))}
            </select>
            {errors.assetId && (
              <p className="mt-1 text-sm text-red-600">{errors.assetId}</p>
            )}
          </div>

          {/* Requested By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Requested By
            </label>
            <input
              type="text"
              value={formData.requestedBy}
              onChange={(e) => handleInputChange('requestedBy', e.target.value)}
              placeholder="Your name"
              className={cn(
                'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.requestedBy ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            />
            {errors.requestedBy && (
              <p className="mt-1 text-sm text-red-600">{errors.requestedBy}</p>
            )}
          </div>

          {/* Priority and Estimated Cost */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value as PriorityLevel)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              >
                {priorities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Estimated Cost ($)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.estimatedCost || ''}
                onChange={(e) => handleInputChange('estimatedCost', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className={cn(
                  'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                  errors.estimatedCost ? 'border-red-300' : 'border-gray-300'
                )}
                disabled={isSubmitting}
              />
              {errors.estimatedCost && (
                <p className="mt-1 text-sm text-red-600">{errors.estimatedCost}</p>
              )}
            </div>
          </div>

          {/* Problem Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Problem Description *
            </label>
            <textarea
              value={formData.problemDescription}
              onChange={(e) => handleInputChange('problemDescription', e.target.value)}
              placeholder="Describe the issue in detail..."
              rows={4}
              maxLength={1000}
              className={cn(
                'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none',
                errors.problemDescription ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.problemDescription ? (
                <p className="text-sm text-red-600">{errors.problemDescription}</p>
              ) : (
                <div />
              )}
              <p className="text-xs text-gray-500">
                {formData.problemDescription?.length || 0}/1000 characters
              </p>
            </div>
          </div>

          {/* Attachments */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Attachments (Photos, Reports - Max 10MB each)
            </label>
            
            {/* File Upload Area */}
            <div 
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-1">Drop files here or click to upload</p>
              <p className="text-xs text-gray-500">JPG, PNG, PDF, DOC up to 10MB each</p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.gif,.pdf,.txt,.doc,.docx"
              onChange={handleFileSelect}
              className="hidden"
              disabled={isSubmitting}
            />

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="mt-3 space-y-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-700 truncate">{file.name}</span>
                      <span className="text-xs text-gray-500">({formatFileSize(file.size)})</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                      disabled={isSubmitting}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Urgency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Urgency
            </label>
            <select
              value={formData.urgency}
              onChange={(e) => handleInputChange('urgency', e.target.value as UrgencyLevel)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              {urgencyLevels.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Form Actions */}
          <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 pt-4">
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </ActionButton>
            <ActionButton
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Submit Request
            </ActionButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default RequestRepairDialog
