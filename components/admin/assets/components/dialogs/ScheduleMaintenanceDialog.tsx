/**
 * ScheduleMaintenanceDialog Component
 * Dialog for scheduling maintenance tasks matching the screenshot exactly
 */

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, MaintenanceFormData, Asset, MaintenanceType, MaintenanceFrequency, PriorityLevel, WarrantyStatus } from '../../types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { X, Calendar } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface ScheduleMaintenanceDialogProps extends BaseComponentProps {
  /** Whether dialog is open */
  open: boolean
  /** Dialog open/close handler */
  onOpenChange: (open: boolean) => void
  /** Form submission handler */
  onSubmit: (data: MaintenanceFormData) => Promise<void> | void
  /** Available assets */
  assets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Pre-selected asset */
  selectedAsset?: Asset
}

// ============================================================================
// VALIDATION
// ============================================================================

interface ValidationErrors {
  [key: string]: string
}

const validateForm = (data: MaintenanceFormData): ValidationErrors => {
  const errors: ValidationErrors = {}

  if (!data.assetId) {
    errors.assetId = 'Please select an asset'
  }

  if (!data.type) {
    errors.type = 'Maintenance type is required'
  }

  if (!data.scheduledDate) {
    errors.scheduledDate = 'Scheduled date is required'
  } else {
    const selectedDate = new Date(data.scheduledDate)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    if (selectedDate < today) {
      errors.scheduledDate = 'Scheduled date cannot be in the past'
    }
  }

  if (data.estimatedCost && data.estimatedCost < 0) {
    errors.estimatedCost = 'Estimated cost cannot be negative'
  }

  if (data.estimatedDowntime && data.estimatedDowntime < 0) {
    errors.estimatedDowntime = 'Estimated downtime cannot be negative'
  }

  if (data.notes && data.notes.length > 1000) {
    errors.notes = 'Notes must be 1000 characters or less'
  }

  return errors
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * ScheduleMaintenanceDialog component for scheduling maintenance
 */
export const ScheduleMaintenanceDialog: React.FC<ScheduleMaintenanceDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  assets = [],
  loading = false,
  selectedAsset,
  className,
  'data-testid': testId,
}) => {
  // Form state
  const [formData, setFormData] = useState<MaintenanceFormData>({
    assetId: selectedAsset?.id || '',
    type: 'preventive',
    frequency: 'one-time',
    scheduledDate: '',
    technicianId: '',
    technicianName: '',
    priority: 'normal',
    estimatedCost: 0,
    estimatedDowntime: 0,
    description: '',
    notes: '',
    warrantyStatus: 'not-covered',
    requiresReplacement: false
  })

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Handle input change
  const handleInputChange = (field: keyof MaintenanceFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const validationErrors = validateForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      onOpenChange(false)
      // Reset form
      setFormData({
        assetId: '',
        type: 'preventive',
        frequency: 'one-time',
        scheduledDate: '',
        technicianId: '',
        technicianName: '',
        priority: 'normal',
        estimatedCost: 0,
        estimatedDowntime: 0,
        description: '',
        notes: '',
        warrantyStatus: 'not-covered',
        requiresReplacement: false
      })
      setErrors({})
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
      setErrors({})
    }
  }

  // Get selected asset details
  const selectedAssetDetails = assets.find(asset => asset.id === formData.assetId)

  // Options
  const maintenanceTypes: { value: MaintenanceType; label: string }[] = [
    { value: 'preventive', label: 'Preventive' },
    { value: 'corrective', label: 'Corrective' },
    { value: 'emergency', label: 'Emergency' },
    { value: 'upgrade', label: 'Upgrade' }
  ]

  const frequencies: { value: MaintenanceFrequency; label: string }[] = [
    { value: 'one-time', label: 'One-time' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'annually', label: 'Annually' }
  ]

  const priorities: { value: PriorityLevel; label: string }[] = [
    { value: 'low', label: 'Low priority' },
    { value: 'normal', label: 'Normal priority' },
    { value: 'high', label: 'High priority' },
    { value: 'critical', label: 'Critical priority' }
  ]

  const warrantyStatuses: { value: WarrantyStatus; label: string }[] = [
    { value: 'covered', label: 'Covered' },
    { value: 'not-covered', label: 'Not Covered' },
    { value: 'expired', label: 'Expired' },
    { value: 'unknown', label: 'Unknown' }
  ]

  const technicians = [
    { id: 'tech-001', name: 'Auto Service Center' },
    { id: 'tech-002', name: 'IT Support Team' },
    { id: 'tech-003', name: 'Furniture Maintenance Co.' },
    { id: 'tech-004', name: 'General Maintenance' }
  ]

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn('sm:max-w-[600px] max-h-[90vh] overflow-y-auto', className)} data-testid={testId}>
        <DialogHeader>
          <DialogTitle>Schedule Maintenance</DialogTitle>
          <p className="text-sm text-gray-600">Create a new maintenance task for preventive or corrective maintenance</p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Asset ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Asset ID *
                </label>
                <select
                  value={formData.assetId}
                  onChange={(e) => handleInputChange('assetId', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    errors.assetId ? 'border-red-300' : 'border-gray-300'
                  )}
                  disabled={isSubmitting || !!selectedAsset}
                >
                  <option value="">Select asset</option>
                  {assets.map(asset => (
                    <option key={asset.id} value={asset.id}>
                      {asset.name} ({asset.id})
                    </option>
                  ))}
                </select>
                {errors.assetId && (
                  <p className="mt-1 text-sm text-red-600">{errors.assetId}</p>
                )}
              </div>

              {/* Priority Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Priority Level
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value as PriorityLevel)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting}
                >
                  {priorities.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Maintenance Type and Frequency */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maintenance Type *
                </label>
                <div className="space-y-2">
                  {maintenanceTypes.map(option => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="maintenanceType"
                        value={option.value}
                        checked={formData.type === option.value}
                        onChange={(e) => handleInputChange('type', e.target.value as MaintenanceType)}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        disabled={isSubmitting}
                      />
                      <span className="text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Frequency
                </label>
                <div className="space-y-2">
                  {frequencies.map(option => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="frequency"
                        value={option.value}
                        checked={formData.frequency === option.value}
                        onChange={(e) => handleInputChange('frequency', e.target.value as MaintenanceFrequency)}
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        disabled={isSubmitting}
                      />
                      <span className="text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Scheduled Date and Assigned Technician */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Scheduled Date *
                </label>
                <input
                  type="date"
                  value={formData.scheduledDate}
                  onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={cn(
                    'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    errors.scheduledDate ? 'border-red-300' : 'border-gray-300'
                  )}
                  disabled={isSubmitting}
                />
                {errors.scheduledDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduledDate}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Technician *
                </label>
                <select
                  value={formData.technicianId}
                  onChange={(e) => {
                    const selectedTech = technicians.find(t => t.id === e.target.value)
                    handleInputChange('technicianId', e.target.value)
                    handleInputChange('technicianName', selectedTech?.name || '')
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting}
                >
                  <option value="">Select Technician</option>
                  {technicians.map(tech => (
                    <option key={tech.id} value={tech.id}>
                      {tech.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Warranty Status */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Warranty Status
              </label>
              <select
                value={formData.warrantyStatus}
                onChange={(e) => handleInputChange('warrantyStatus', e.target.value as WarrantyStatus)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              >
                {warrantyStatuses.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              
              <label className="flex items-center mt-2">
                <input
                  type="checkbox"
                  checked={formData.requiresReplacement}
                  onChange={(e) => handleInputChange('requiresReplacement', e.target.checked)}
                  className="mr-2 text-blue-600 focus:ring-blue-500"
                  disabled={isSubmitting}
                />
                <span className="text-sm text-gray-700">Requires temporary replacement asset</span>
              </label>
            </div>

            {/* Cost and Downtime */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Cost ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.estimatedCost || ''}
                  onChange={(e) => handleInputChange('estimatedCost', parseFloat(e.target.value) || 0)}
                  placeholder="0"
                  className={cn(
                    'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    errors.estimatedCost ? 'border-red-300' : 'border-gray-300'
                  )}
                  disabled={isSubmitting}
                />
                {errors.estimatedCost && (
                  <p className="mt-1 text-sm text-red-600">{errors.estimatedCost}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Downtime (hours)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.estimatedDowntime || ''}
                  onChange={(e) => handleInputChange('estimatedDowntime', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className={cn(
                    'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    errors.estimatedDowntime ? 'border-red-300' : 'border-gray-300'
                  )}
                  disabled={isSubmitting}
                />
                {errors.estimatedDowntime && (
                  <p className="mt-1 text-sm text-red-600">{errors.estimatedDowntime}</p>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes (max 1000 characters)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional maintenance notes, special instructions, or requirements..."
              rows={4}
              maxLength={1000}
              className={cn(
                'w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none',
                errors.notes ? 'border-red-300' : 'border-gray-300'
              )}
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.notes ? (
                <p className="text-sm text-red-600">{errors.notes}</p>
              ) : (
                <div />
              )}
              <p className="text-xs text-gray-500">
                {formData.notes?.length || 0}/1000 characters
              </p>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 pt-4">
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </ActionButton>
            <ActionButton
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Schedule maintenance
            </ActionButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default ScheduleMaintenanceDialog
