/**
 * Asset Management Components - Main Export Index
 * Centralized exports for all asset management components
 */

// Main Application
export { AssetManagementApp } from './AssetManagementApp'

// UI Components
export { StatsCard, StatsGrid, StatsCardSkeleton } from './ui/StatsCard'
export { NavigationTabs } from './ui/NavigationTabs'
export { DataTable } from './ui/DataTable'
export { ActionButton, ButtonGroup } from './ui/ActionButton'

// View Components
export { DashboardView } from './views/DashboardView'
export { InventoryView } from './views/InventoryView'
export { AssignmentsView } from './views/AssignmentsView'
export { MaintenanceView } from './views/MaintenanceView'
export { ReportsView } from './views/ReportsView'
export { EmployeePortalView } from './views/EmployeePortalView'

// Dialog Components
export { AddAssetDialog } from './dialogs/AddAssetDialog'
export { CreateAssignmentDialog } from './dialogs/CreateAssignmentDialog'
export { ScheduleMaintenanceDialog } from './dialogs/ScheduleMaintenanceDialog'
export { RequestRepairDialog } from './dialogs/RequestRepairDialog'

// Types
export * from '../types'

// Context and Hooks
export { AssetProvider, useAssetContext } from '../context/AssetContext'
export { useAssetManagement } from '../hooks/useAssetManagement'
