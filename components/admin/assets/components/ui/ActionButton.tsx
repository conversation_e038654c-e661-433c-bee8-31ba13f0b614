/**
 * ActionButton Component
 * Versatile button component with loading states, icons, and accessibility
 */

import React, { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { BaseComponentProps } from '../../types'

// ============================================================================
// TYPES
// ============================================================================

export interface ActionButtonProps extends BaseComponentProps {
  /** Button content */
  children?: React.ReactNode
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'success' | 'warning'
  /** Button size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** Icon to display */
  icon?: React.ReactNode
  /** Icon position */
  iconPosition?: 'left' | 'right'
  /** Loading state */
  loading?: boolean
  /** Disabled state */
  disabled?: boolean
  /** Full width button */
  fullWidth?: boolean
  /** Button type */
  type?: 'button' | 'submit' | 'reset'
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void
  /** Tooltip text */
  tooltip?: string
  /** Badge count */
  badge?: number
  /** Badge color */
  badgeColor?: 'default' | 'success' | 'warning' | 'error'
  /** Whether button should be rounded */
  rounded?: boolean
  /** Custom loading spinner */
  loadingSpinner?: React.ReactNode
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * ActionButton component with comprehensive styling and accessibility
 */
export const ActionButton = forwardRef<HTMLButtonElement, ActionButtonProps>(({
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  fullWidth = false,
  type = 'button',
  onClick,
  tooltip,
  badge,
  badgeColor = 'default',
  rounded = false,
  loadingSpinner,
  className,
  children,
  'data-testid': testId,
  ...props
}, ref) => {
  // Get variant styles
  const getVariantStyles = () => {
    const variants = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 active:bg-blue-800',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 active:bg-gray-800',
      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500 active:bg-gray-100',
      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200',
      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800',
      success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 active:bg-green-800',
      warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800'
    }
    return variants[variant]
  }

  // Get size styles
  const getSizeStyles = () => {
    const sizes = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base',
      xl: 'px-8 py-4 text-lg'
    }
    return sizes[size]
  }

  // Get icon size
  const getIconSize = () => {
    const iconSizes = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6'
    }
    return iconSizes[size]
  }

  // Default loading spinner
  const defaultSpinner = (
    <svg
      className={cn('animate-spin', getIconSize())}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  // Badge component
  const Badge: React.FC<{ count: number; color: string }> = ({ count, color }) => {
    const badgeColors = {
      default: 'bg-gray-500',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500'
    }

    return (
      <span className={cn(
        'absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white rounded-full',
        badgeColors[color as keyof typeof badgeColors] || badgeColors.default
      )}>
        {count > 99 ? '99+' : count}
      </span>
    )
  }

  const buttonClasses = cn(
    // Base styles
    'relative inline-flex items-center justify-center font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    'transform hover:scale-105 active:scale-95',
    
    // Variant styles
    getVariantStyles(),
    
    // Size styles
    getSizeStyles(),
    
    // Shape styles
    rounded ? 'rounded-full' : 'rounded-md',
    
    // Width styles
    fullWidth && 'w-full',
    
    // Loading styles
    loading && 'cursor-wait',
    
    className
  )

  const iconClasses = cn(
    getIconSize(),
    'flex-shrink-0',
    children && iconPosition === 'left' && 'mr-2',
    children && iconPosition === 'right' && 'ml-2'
  )

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (loading || disabled) {
      event.preventDefault()
      return
    }
    onClick?.(event)
  }

  const buttonContent = (
    <>
      {/* Left icon or loading spinner */}
      {iconPosition === 'left' && (loading ? (loadingSpinner || defaultSpinner) : icon) && (
        <span className={iconClasses} aria-hidden="true">
          {loading ? (loadingSpinner || defaultSpinner) : icon}
        </span>
      )}

      {/* Button text */}
      {children && (
        <span className={cn(
          'truncate',
          loading && 'opacity-75'
        )}>
          {children}
        </span>
      )}

      {/* Right icon */}
      {iconPosition === 'right' && !loading && icon && (
        <span className={iconClasses} aria-hidden="true">
          {icon}
        </span>
      )}

      {/* Badge */}
      {badge !== undefined && badge > 0 && (
        <Badge count={badge} color={badgeColor} />
      )}
    </>
  )

  // Wrap with tooltip if provided
  if (tooltip) {
    return (
      <div className="relative group">
        <button
          ref={ref}
          type={type}
          disabled={disabled || loading}
          className={buttonClasses}
          onClick={handleClick}
          data-testid={testId}
          aria-label={tooltip}
          {...props}
        >
          {buttonContent}
        </button>
        
        {/* Tooltip */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          {tooltip}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      </div>
    )
  }

  return (
    <button
      ref={ref}
      type={type}
      disabled={disabled || loading}
      className={buttonClasses}
      onClick={handleClick}
      data-testid={testId}
      aria-busy={loading}
      {...props}
    >
      {buttonContent}
    </button>
  )
})

ActionButton.displayName = 'ActionButton'

// ============================================================================
// BUTTON GROUP
// ============================================================================

export interface ButtonGroupProps extends BaseComponentProps {
  /** Button orientation */
  orientation?: 'horizontal' | 'vertical'
  /** Button size for all buttons in group */
  size?: ActionButtonProps['size']
  /** Whether buttons should be attached */
  attached?: boolean
  /** Gap between buttons when not attached */
  gap?: 'sm' | 'md' | 'lg'
}

/**
 * ButtonGroup component for grouping related buttons
 */
export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  orientation = 'horizontal',
  size = 'md',
  attached = false,
  gap = 'md',
  className,
  children,
  'data-testid': testId,
}) => {
  const groupClasses = cn(
    'flex',
    orientation === 'vertical' ? 'flex-col' : 'flex-row',
    attached ? (
      orientation === 'vertical' ? 'divide-y divide-gray-200' : 'divide-x divide-gray-200'
    ) : (
      {
        sm: orientation === 'vertical' ? 'space-y-2' : 'space-x-2',
        md: orientation === 'vertical' ? 'space-y-3' : 'space-x-3',
        lg: orientation === 'vertical' ? 'space-y-4' : 'space-x-4'
      }[gap]
    ),
    attached && 'rounded-md overflow-hidden border border-gray-300',
    className
  )

  return (
    <div className={groupClasses} data-testid={testId} role="group">
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === ActionButton) {
          const isFirst = index === 0
          const isLast = index === React.Children.count(children) - 1

          if (attached) {
            return React.cloneElement(child, {
              ...child.props,
              size: child.props.size || size,
              className: cn(
                child.props.className,
                orientation === 'vertical' ? (
                  isFirst ? 'rounded-t-md rounded-b-none' :
                  isLast ? 'rounded-b-md rounded-t-none' :
                  'rounded-none'
                ) : (
                  isFirst ? 'rounded-l-md rounded-r-none' :
                  isLast ? 'rounded-r-md rounded-l-none' :
                  'rounded-none'
                ),
                'border-0'
              )
            })
          }

          return React.cloneElement(child, {
            ...child.props,
            size: child.props.size || size
          })
        }
        return child
      })}
    </div>
  )
}

export default ActionButton
