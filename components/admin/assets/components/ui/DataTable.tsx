/**
 * DataTable Component
 * Responsive data table with sorting, pagination, and mobile optimization
 */

import React, { useState, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { BaseComponentProps, TableColumn, PaginationProps } from '../../types'

// ============================================================================
// TYPES
// ============================================================================

export interface DataTableProps<T = any> extends BaseComponentProps {
  /** Table data */
  data: T[]
  /** Column definitions */
  columns: TableColumn<T>[]
  /** Loading state */
  loading?: boolean
  /** Empty state message */
  emptyMessage?: string
  /** Whether table is sortable */
  sortable?: boolean
  /** Default sort column */
  defaultSortKey?: string
  /** Default sort order */
  defaultSortOrder?: 'asc' | 'desc'
  /** Row selection */
  selectable?: boolean
  /** Selected row IDs */
  selectedRows?: string[]
  /** Selection change handler */
  onSelectionChange?: (selectedIds: string[]) => void
  /** Row click handler */
  onRowClick?: (row: T, index: number) => void
  /** Row key extractor */
  getRowKey?: (row: T, index: number) => string
  /** Pagination props */
  pagination?: PaginationProps
  /** Whether to show pagination */
  showPagination?: boolean
  /** Table variant */
  variant?: 'default' | 'striped' | 'bordered'
  /** Table size */
  size?: 'sm' | 'md' | 'lg'
  /** Sticky header */
  stickyHeader?: boolean
  /** Mobile responsive mode */
  mobileMode?: 'scroll' | 'stack' | 'cards'
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * DataTable component for displaying tabular data
 */
export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  sortable = true,
  defaultSortKey,
  defaultSortOrder = 'asc',
  selectable = false,
  selectedRows = [],
  onSelectionChange,
  onRowClick,
  getRowKey = (row, index) => row.id || index.toString(),
  pagination,
  showPagination = false,
  variant = 'default',
  size = 'md',
  stickyHeader = false,
  mobileMode = 'scroll',
  className,
  'data-testid': testId,
}: DataTableProps<T>) => {
  const [sortKey, setSortKey] = useState<string | null>(defaultSortKey || null)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(defaultSortOrder)

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortKey || !sortable) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortKey]
      const bValue = b[sortKey]

      if (aValue === bValue) return 0

      let comparison = 0
      if (aValue > bValue) comparison = 1
      if (aValue < bValue) comparison = -1

      return sortOrder === 'desc' ? comparison * -1 : comparison
    })
  }, [data, sortKey, sortOrder, sortable])

  // Handle sort
  const handleSort = (columnKey: string) => {
    if (!sortable) return

    if (sortKey === columnKey) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortKey(columnKey)
      setSortOrder('asc')
    }
  }

  // Handle row selection
  const handleRowSelection = (rowKey: string, checked: boolean) => {
    if (!onSelectionChange) return

    const newSelection = checked
      ? [...selectedRows, rowKey]
      : selectedRows.filter(id => id !== rowKey)

    onSelectionChange(newSelection)
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return

    const allRowKeys = sortedData.map((row, index) => getRowKey(row, index))
    onSelectionChange(checked ? allRowKeys : [])
  }

  // Get table styles
  const getTableStyles = () => {
    const baseStyles = 'w-full border-collapse'
    const variantStyles = {
      default: '',
      striped: '',
      bordered: 'border border-gray-200'
    }
    const sizeStyles = {
      sm: 'text-sm',
      md: 'text-sm',
      lg: 'text-base'
    }

    return cn(baseStyles, variantStyles[variant], sizeStyles[size])
  }

  // Get cell padding
  const getCellPadding = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2'
      case 'lg':
        return 'px-6 py-4'
      default:
        return 'px-4 py-3'
    }
  }

  // Check if all rows are selected
  const isAllSelected = selectedRows.length === sortedData.length && sortedData.length > 0
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < sortedData.length

  // Loading skeleton
  if (loading) {
    return (
      <div className={cn('bg-white rounded-lg border overflow-hidden', className)} data-testid={testId}>
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div className="border-b border-gray-200 bg-gray-50">
            <div className="flex">
              {columns.map((_, index) => (
                <div key={index} className={cn('flex-1', getCellPadding())}>
                  <div className="h-4 bg-gray-200 rounded" />
                </div>
              ))}
            </div>
          </div>
          {/* Rows skeleton */}
          {Array.from({ length: 5 }).map((_, rowIndex) => (
            <div key={rowIndex} className="border-b border-gray-200">
              <div className="flex">
                {columns.map((_, colIndex) => (
                  <div key={colIndex} className={cn('flex-1', getCellPadding())}>
                    <div className="h-4 bg-gray-200 rounded" />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Empty state
  if (sortedData.length === 0) {
    return (
      <div className={cn('bg-white rounded-lg border', className)} data-testid={testId}>
        <div className="p-12 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-lg font-medium">{emptyMessage}</p>
        </div>
      </div>
    )
  }

  // Mobile card view
  if (mobileMode === 'cards') {
    return (
      <div className={cn('space-y-4 md:hidden', className)} data-testid={testId}>
        {sortedData.map((row, index) => {
          const rowKey = getRowKey(row, index)
          const isSelected = selectedRows.includes(rowKey)

          return (
            <div
              key={rowKey}
              className={cn(
                'bg-white rounded-lg border p-4 space-y-3 transition-all duration-200',
                'transform hover:scale-105 hover:shadow-lg',
                onRowClick && 'cursor-pointer hover:bg-gray-50',
                isSelected && 'ring-2 ring-blue-500 scale-105'
              )}
              onClick={() => onRowClick?.(row, index)}
            >
              {selectable && (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => handleRowSelection(rowKey, e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              )}
              {columns.map((column) => (
                <div key={column.key} className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">{column.label}</span>
                  <span className="text-sm text-gray-900">
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </span>
                </div>
              ))}
            </div>
          )
        })}
      </div>
    )
  }

  // Desktop table view
  return (
    <div className={cn('bg-white rounded-lg border overflow-hidden', className)} data-testid={testId}>
      <div className={cn(
        'overflow-x-auto',
        mobileMode === 'scroll' && 'scrollbar-thin scrollbar-thumb-gray-300'
      )}>
        <table className={getTableStyles()}>
          {/* Header */}
          <thead className={cn(
            'bg-gray-50 border-b border-gray-200',
            stickyHeader && 'sticky top-0 z-10'
          )}>
            <tr>
              {/* Select all checkbox */}
              {selectable && (
                <th className={cn('text-left', getCellPadding())}>
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = isIndeterminate
                    }}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    aria-label="Select all rows"
                  />
                </th>
              )}

              {/* Column headers */}
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    'text-left font-medium text-gray-600',
                    getCellPadding(),
                    column.width && `w-${column.width}`,
                    column.className,
                    column.sortable !== false && sortable && 'cursor-pointer hover:bg-gray-100 select-none'
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable !== false && handleSort(column.key)}
                >
                  <div className="flex items-center gap-2">
                    <span>{column.label}</span>
                    {column.sortable !== false && sortable && (
                      <div className="flex flex-col">
                        <svg
                          className={cn(
                            'w-3 h-3',
                            sortKey === column.key && sortOrder === 'asc'
                              ? 'text-blue-600'
                              : 'text-gray-400'
                          )}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                        <svg
                          className={cn(
                            'w-3 h-3 -mt-1',
                            sortKey === column.key && sortOrder === 'desc'
                              ? 'text-blue-600'
                              : 'text-gray-400'
                          )}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="divide-y divide-gray-200">
            {sortedData.map((row, index) => {
              const rowKey = getRowKey(row, index)
              const isSelected = selectedRows.includes(rowKey)

              return (
                <tr
                  key={rowKey}
                  className={cn(
                    'hover:bg-gray-50 hover:shadow-md transition-all duration-200',
                    'transform hover:-translate-y-0.5',
                    variant === 'striped' && index % 2 === 1 && 'bg-gray-50',
                    onRowClick && 'cursor-pointer',
                    isSelected && 'bg-blue-50 shadow-sm'
                  )}
                  onClick={() => onRowClick?.(row, index)}
                >
                  {/* Selection checkbox */}
                  {selectable && (
                    <td className={getCellPadding()}>
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          e.stopPropagation()
                          handleRowSelection(rowKey, e.target.checked)
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        aria-label={`Select row ${index + 1}`}
                      />
                    </td>
                  )}

                  {/* Data cells */}
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        'text-gray-900',
                        getCellPadding(),
                        column.align === 'center' && 'text-center',
                        column.align === 'right' && 'text-right',
                        column.className
                      )}
                    >
                      {column.render ? column.render(row[column.key], row) : row[column.key]}
                    </td>
                  ))}
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {showPagination && pagination && (
        <div className="border-t border-gray-200 px-4 py-3 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
              {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
              {pagination.totalItems} results
            </div>
            <div className="flex items-center gap-2">
              <button
                type="button"
                disabled={pagination.currentPage === 1}
                onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
                className="px-3 py-1 text-sm border rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="text-sm">
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
              <button
                type="button"
                disabled={pagination.currentPage === pagination.totalPages}
                onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
                className="px-3 py-1 text-sm border rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DataTable
