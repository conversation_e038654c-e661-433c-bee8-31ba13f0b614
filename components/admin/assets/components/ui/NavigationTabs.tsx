/**
 * NavigationTabs Component
 * Responsive navigation tabs with keyboard support and accessibility
 */

import React, { useRef, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { BaseComponentProps, TabType } from '../../types'

// ============================================================================
// TYPES
// ============================================================================

export interface TabItem {
  /** Unique tab identifier */
  id: TabType
  /** Tab label */
  label: string
  /** Tab icon */
  icon?: React.ReactNode
  /** Whether tab is disabled */
  disabled?: boolean
  /** Badge count */
  badge?: number
  /** Badge color */
  badgeColor?: 'default' | 'success' | 'warning' | 'error'
}

export interface NavigationTabsProps extends BaseComponentProps {
  /** Array of tab items */
  tabs: TabItem[]
  /** Currently active tab */
  activeTab: TabType
  /** Tab change handler */
  onTabChange: (tabId: TabType) => void
  /** Tab variant */
  variant?: 'default' | 'pills' | 'underline'
  /** Tab size */
  size?: 'sm' | 'md' | 'lg'
  /** Whether tabs should be full width */
  fullWidth?: boolean
  /** Whether tabs should be scrollable on mobile */
  scrollable?: boolean
  /** Loading state */
  loading?: boolean
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * NavigationTabs component for tab navigation
 */
export const NavigationTabs: React.FC<NavigationTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  variant = 'default',
  size = 'md',
  fullWidth = false,
  scrollable = true,
  loading = false,
  className,
  'data-testid': testId,
}) => {
  const tabsRef = useRef<HTMLDivElement>(null)
  const [showScrollButtons, setShowScrollButtons] = useState(false)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  // Check if scrolling is needed
  useEffect(() => {
    const checkScroll = () => {
      if (!tabsRef.current || !scrollable) return

      const { scrollLeft, scrollWidth, clientWidth } = tabsRef.current
      const needsScroll = scrollWidth > clientWidth

      setShowScrollButtons(needsScroll)
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
    }

    checkScroll()
    window.addEventListener('resize', checkScroll)
    return () => window.removeEventListener('resize', checkScroll)
  }, [tabs, scrollable])

  // Scroll functions
  const scrollLeft = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent, tabId: TabType) => {
    const currentIndex = tabs.findIndex(tab => tab.id === tabId)
    let nextIndex = currentIndex

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1
        break
      case 'ArrowRight':
        event.preventDefault()
        nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0
        break
      case 'Home':
        event.preventDefault()
        nextIndex = 0
        break
      case 'End':
        event.preventDefault()
        nextIndex = tabs.length - 1
        break
      default:
        return
    }

    const nextTab = tabs[nextIndex]
    if (nextTab && !nextTab.disabled) {
      onTabChange(nextTab.id)
    }
  }

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'pills':
        return {
          container: 'bg-gray-100 p-1 rounded-lg',
          tab: 'rounded-md',
          active: 'bg-white text-gray-900 shadow-sm',
          inactive: 'text-gray-600 hover:text-gray-900'
        }
      case 'underline':
        return {
          container: 'border-b border-gray-200',
          tab: 'border-b-2 border-transparent',
          active: 'border-blue-500 text-blue-600',
          inactive: 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
        }
      default:
        return {
          container: 'bg-gray-100 p-1 rounded-lg',
          tab: 'rounded-md',
          active: 'bg-white text-gray-900 shadow-sm',
          inactive: 'text-gray-600 hover:text-gray-900'
        }
    }
  }

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm'
      case 'lg':
        return 'px-6 py-3 text-lg'
      default:
        return 'px-4 py-2 text-sm'
    }
  }

  const styles = getVariantStyles()

  const containerClasses = cn(
    'flex items-center',
    fullWidth ? 'w-full' : 'w-fit',
    className
  )

  const tabsContainerClasses = cn(
    'flex',
    styles.container,
    fullWidth && 'flex-1',
    scrollable && 'overflow-x-auto scrollbar-hide'
  )

  const tabClasses = (tab: TabItem, isActive: boolean) => cn(
    'flex items-center gap-2 font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    'whitespace-nowrap transform hover:scale-105 active:scale-95',
    getSizeStyles(),
    styles.tab,
    isActive ? cn(styles.active, 'scale-105 shadow-md') : styles.inactive,
    tab.disabled && 'opacity-50 cursor-not-allowed',
    fullWidth && 'flex-1 justify-center'
  )

  // Badge component
  const Badge: React.FC<{ count: number; color: string }> = ({ count, color }) => {
    const badgeColors = {
      default: 'bg-gray-500',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500'
    }

    return (
      <span className={cn(
        'inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white rounded-full',
        badgeColors[color as keyof typeof badgeColors] || badgeColors.default
      )}>
        {count > 99 ? '99+' : count}
      </span>
    )
  }

  if (loading) {
    return (
      <div className={containerClasses} data-testid={testId}>
        <div className={tabsContainerClasses}>
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className={cn(
                'animate-pulse bg-gray-200 rounded-md',
                getSizeStyles(),
                'w-24 h-8'
              )}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={containerClasses} data-testid={testId}>
      {/* Left scroll button */}
      {showScrollButtons && canScrollLeft && (
        <button
          type="button"
          onClick={scrollLeft}
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md mr-2"
          aria-label="Scroll tabs left"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}

      {/* Tabs container */}
      <div
        ref={tabsRef}
        className={tabsContainerClasses}
        onScroll={() => {
          if (!tabsRef.current) return
          const { scrollLeft, scrollWidth, clientWidth } = tabsRef.current
          setCanScrollLeft(scrollLeft > 0)
          setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
        }}
      >
        {tabs.map((tab) => {
          const isActive = tab.id === activeTab

          return (
            <button
              key={tab.id}
              type="button"
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              tabIndex={isActive ? 0 : -1}
              disabled={tab.disabled}
              className={tabClasses(tab, isActive)}
              onClick={() => !tab.disabled && onTabChange(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
            >
              {/* Icon */}
              {tab.icon && (
                <span className="flex-shrink-0" aria-hidden="true">
                  {tab.icon}
                </span>
              )}

              {/* Label */}
              <span className={cn(
                size === 'sm' && 'hidden sm:inline',
                'truncate'
              )}>
                {tab.label}
              </span>

              {/* Badge */}
              {tab.badge !== undefined && tab.badge > 0 && (
                <Badge 
                  count={tab.badge} 
                  color={tab.badgeColor || 'default'} 
                />
              )}
            </button>
          )
        })}
      </div>

      {/* Right scroll button */}
      {showScrollButtons && canScrollRight && (
        <button
          type="button"
          onClick={scrollRight}
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md ml-2"
          aria-label="Scroll tabs right"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}
    </div>
  )
}

export default NavigationTabs
