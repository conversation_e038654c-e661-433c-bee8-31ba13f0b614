/**
 * StatsCard Component
 * Displays key metrics and statistics with proper accessibility and responsive design
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { BaseComponentProps } from '../../types'

// ============================================================================
// TYPES
// ============================================================================

export interface StatsCardProps extends BaseComponentProps {
  /** Card title */
  title: string
  /** Main value to display */
  value: string | number
  /** Subtitle or additional info */
  subtitle?: string
  /** Icon component */
  icon?: React.ReactNode
  /** Trend indicator */
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  /** Card variant */
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'
  /** Loading state */
  loading?: boolean
  /** Click handler */
  onClick?: () => void
  /** Whether the card is clickable */
  clickable?: boolean
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * StatsCard component for displaying key metrics
 */
export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  variant = 'default',
  loading = false,
  onClick,
  clickable = false,
  className,
  children,
  'data-testid': testId,
  ...props
}) => {
  // Format large numbers
  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`
      }
      if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`
      }
      return val.toLocaleString()
    }
    return val
  }

  // Get trend color
  const getTrendColor = (direction: 'up' | 'down' | 'neutral'): string => {
    switch (direction) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // Get trend icon
  const getTrendIcon = (direction: 'up' | 'down' | 'neutral'): string => {
    switch (direction) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      default:
        return '→'
    }
  }

  // Get variant styles
  const getVariantStyles = (variant: string): string => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      case 'info':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  const cardClasses = cn(
    // Base styles
    'rounded-lg border p-6 transition-all duration-200',
    // Variant styles
    getVariantStyles(variant),
    // Interactive styles
    clickable && [
      'cursor-pointer hover:shadow-md hover:scale-[1.02]',
      'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
    ],
    // Loading styles
    loading && 'animate-pulse',
    className
  )

  const CardContent = () => (
    <>
      {/* Header with icon */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-600 truncate">
          {title}
        </h3>
        {icon && (
          <div className="flex-shrink-0 text-gray-400">
            {icon}
          </div>
        )}
      </div>

      {/* Main value */}
      <div className="mb-1">
        {loading ? (
          <div className="h-8 bg-gray-200 rounded animate-pulse" />
        ) : (
          <p className="text-3xl font-bold text-gray-900 truncate">
            {formatValue(value)}
          </p>
        )}
      </div>

      {/* Subtitle and trend */}
      <div className="flex items-center justify-between">
        {subtitle && (
          <p className="text-sm text-gray-600 truncate flex-1">
            {loading ? (
              <span className="inline-block h-4 w-24 bg-gray-200 rounded animate-pulse" />
            ) : (
              subtitle
            )}
          </p>
        )}
        
        {trend && !loading && (
          <div className={cn(
            'flex items-center text-sm font-medium ml-2',
            getTrendColor(trend.direction)
          )}>
            <span className="mr-1" aria-hidden="true">
              {getTrendIcon(trend.direction)}
            </span>
            <span>
              {trend.value > 0 ? '+' : ''}{trend.value}%
            </span>
            <span className="sr-only">
              {trend.direction === 'up' ? 'increased' : 
               trend.direction === 'down' ? 'decreased' : 'unchanged'} by {Math.abs(trend.value)}%
            </span>
          </div>
        )}
      </div>

      {/* Additional content */}
      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}
    </>
  )

  // Render as button if clickable
  if (clickable && onClick) {
    return (
      <button
        type="button"
        className={cardClasses}
        onClick={onClick}
        data-testid={testId}
        aria-label={`${title}: ${value}${subtitle ? `, ${subtitle}` : ''}`}
        {...props}
      >
        <CardContent />
      </button>
    )
  }

  // Render as div
  return (
    <div
      className={cardClasses}
      data-testid={testId}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      {...props}
    >
      <CardContent />
    </div>
  )
}

// ============================================================================
// LOADING SKELETON
// ============================================================================

/**
 * Loading skeleton for StatsCard
 */
export const StatsCardSkeleton: React.FC<{ className?: string }> = ({
  className
}) => (
  <div className={cn(
    'rounded-lg border border-gray-200 bg-white p-6 animate-pulse',
    'hover:shadow-lg hover:-translate-y-1 transition-all duration-200',
    className
  )}>
    <div className="flex items-center justify-between mb-2">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24 animate-pulse" />
      <div className="h-5 w-5 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded animate-pulse" />
    </div>
    <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-20 mb-1 animate-pulse" />
    <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-32 animate-pulse" />
  </div>
)

// ============================================================================
// GRID CONTAINER
// ============================================================================

export interface StatsGridProps extends BaseComponentProps {
  /** Grid columns configuration */
  columns?: 1 | 2 | 3 | 4 | 5 | 6
  /** Gap between cards */
  gap?: 'sm' | 'md' | 'lg'
  /** Loading state */
  loading?: boolean
  /** Number of skeleton cards to show when loading */
  skeletonCount?: number
  /** Child components */
  children?: React.ReactNode
}

/**
 * Grid container for StatsCard components
 */
export const StatsGrid: React.FC<StatsGridProps> = ({
  columns = 4,
  gap = 'md',
  loading = false,
  skeletonCount = 4,
  className,
  children,
  'data-testid': testId,
}) => {
  const getGridClasses = () => {
    const baseClasses = 'grid w-full'
    const columnClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 sm:grid-cols-2',
      3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    }
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-6',
      lg: 'gap-8',
    }
    
    return cn(
      baseClasses,
      columnClasses[columns],
      gapClasses[gap],
      className
    )
  }

  if (loading) {
    return (
      <div className={getGridClasses()} data-testid={testId}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <StatsCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  return (
    <div className={getGridClasses()} data-testid={testId}>
      {children}
    </div>
  )
}

export default StatsCard
