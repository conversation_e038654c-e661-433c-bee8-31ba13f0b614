/**
 * AssignmentsView Component
 * Asset assignment management with tracking and returns
 */

import React, { useMemo } from 'react'
import { cn } from '@/lib/utils'
import { DataTable } from '../ui/DataTable'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, Assignment, Asset, TableColumn } from '../../types'
import { 
  Plus, 
  Eye, 
  RotateCcw,
  User,
  Calendar,
  Clock,
  AlertTriangle
} from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface AssignmentsViewProps extends BaseComponentProps {
  /** Assignment data */
  assignments?: Assignment[]
  /** Asset data for reference */
  assets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Create assignment handler */
  onCreateAssignment?: () => void
  /** View assignment handler */
  onViewAssignment?: (assignment: Assignment) => void
  /** Return asset handler */
  onReturnAsset?: (assignment: Assignment) => void
  /** Edit assignment handler */
  onEditAssignment?: (assignment: Assignment) => void
}

// ============================================================================
// MOCK DATA
// ============================================================================

const mockAssignments: Assignment[] = [
  {
    id: 'ASG001',
    assetId: 'AST123456',
    assigneeId: 'john-smith',
    assigneeName: 'John Smith',
    type: 'employee',
    purpose: 'Daily work laptop for development tasks',
    assignmentDate: '2024-01-15',
    returnDate: '2024-12-31',
    status: 'active',
    notes: 'Includes charger and laptop bag',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    asset: {
      id: 'AST123456',
      name: 'Dell Laptop XPS 13',
      type: 'laptop',
      status: 'assigned',
      condition: 'good',
      location: 'NYC Office - Floor 3',
      purchaseCost: 1200,
      currentValue: 800,
      purchaseDate: '2023-01-15',
      vendor: 'Dell Inc.',
      serialNumber: 'DL123456789',
      model: 'XPS 13 9310',
      category: 'IT Equipment',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  },
  {
    id: 'ASG002',
    assetId: 'AST123458',
    assigneeId: 'sales-dept',
    assigneeName: 'Sales Department',
    type: 'department',
    purpose: 'Department vehicle for client visits and sales activities',
    assignmentDate: '2023-03-25',
    status: 'active',
    notes: 'Shared vehicle for sales team',
    createdAt: '2023-03-25T00:00:00Z',
    updatedAt: '2023-03-25T00:00:00Z',
    asset: {
      id: 'AST123458',
      name: 'Toyota Camry 2023',
      type: 'vehicle',
      status: 'assigned',
      condition: 'excellent',
      location: 'Parking Garage Level 1',
      purchaseCost: 26999,
      currentValue: 24000,
      purchaseDate: '2023-06-10',
      vendor: 'Toyota',
      serialNumber: 'TC2023456789',
      model: 'Camry LE',
      category: 'Vehicle',
      createdAt: '2023-06-10T00:00:00Z',
      updatedAt: '2023-06-10T00:00:00Z'
    }
  },
  {
    id: 'ASG003',
    assetId: 'AST123459',
    assigneeId: 'sarah-johnson',
    assigneeName: 'Sarah Johnson',
    type: 'employee',
    purpose: 'Mobile device for field work and communication',
    assignmentDate: '2024-02-10',
    returnDate: '2024-08-10',
    actualReturnDate: '2024-08-05',
    status: 'returned',
    notes: 'Returned in good condition',
    createdAt: '2024-02-10T00:00:00Z',
    updatedAt: '2024-08-05T00:00:00Z',
    asset: {
      id: 'AST123459',
      name: 'iPhone 14 Pro',
      type: 'mobile-device',
      status: 'available',
      condition: 'good',
      location: 'IT Department',
      purchaseCost: 999,
      currentValue: 700,
      purchaseDate: '2023-09-15',
      vendor: 'Apple Inc.',
      serialNumber: 'IP14PRO123456',
      model: 'iPhone 14 Pro 256GB',
      category: 'Mobile Device',
      createdAt: '2023-09-15T00:00:00Z',
      updatedAt: '2023-09-15T00:00:00Z'
    }
  }
]

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * AssignmentsView component for managing asset assignments
 */
export const AssignmentsView: React.FC<AssignmentsViewProps> = ({
  assignments = mockAssignments,
  assets = [],
  loading = false,
  error,
  onCreateAssignment,
  onViewAssignment,
  onReturnAsset,
  onEditAssignment,
  className,
  'data-testid': testId,
}) => {
  // Calculate assignment statistics
  const stats = useMemo(() => {
    const total = assignments.length
    const active = assignments.filter(a => a.status === 'active').length
    const overdue = assignments.filter(a => {
      if (a.status !== 'active' || !a.returnDate) return false
      return new Date(a.returnDate) < new Date()
    }).length
    const returned = assignments.filter(a => a.status === 'returned').length

    return { total, active, overdue, returned }
  }, [assignments])

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Check if assignment is overdue
  const isOverdue = (assignment: Assignment) => {
    if (assignment.status !== 'active' || !assignment.returnDate) return false
    return new Date(assignment.returnDate) < new Date()
  }

  // Get status badge variant
  const getStatusVariant = (assignment: Assignment) => {
    if (isOverdue(assignment)) return 'destructive'
    
    switch (assignment.status) {
      case 'active':
        return 'success'
      case 'returned':
        return 'secondary'
      case 'overdue':
        return 'destructive'
      case 'pending':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  // Get status display text
  const getStatusText = (assignment: Assignment) => {
    if (isOverdue(assignment)) return 'Overdue'
    return assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)
  }

  // Table columns
  const columns: TableColumn<Assignment>[] = [
    {
      key: 'id',
      label: 'Assignment ID',
      width: '120px',
      render: (value) => (
        <span className="font-mono text-sm text-gray-600">{value}</span>
      )
    },
    {
      key: 'asset',
      label: 'Asset',
      render: (_, row) => (
        <div>
          <div className="font-medium text-gray-900">{row.asset?.name || 'Unknown Asset'}</div>
          <div className="text-sm text-gray-500 font-mono">{row.assetId}</div>
        </div>
      )
    },
    {
      key: 'assigneeName',
      label: 'Assignee',
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500 capitalize">{row.type}</div>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
          {value}
        </span>
      )
    },
    {
      key: 'assignmentDate',
      label: 'Assignment Date',
      width: '140px',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{formatDate(value)}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      width: '100px',
      render: (_, row) => {
        const variant = getStatusVariant(row)
        const text = getStatusText(row)
        
        return (
          <div className="flex items-center gap-2">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              {
                'bg-green-100 text-green-800': variant === 'success',
                'bg-gray-100 text-gray-800': variant === 'secondary',
                'bg-red-100 text-red-800': variant === 'destructive',
                'bg-yellow-100 text-yellow-800': variant === 'warning'
              }
            )}>
              {text}
            </span>
            {isOverdue(row) && (
              <AlertTriangle className="w-4 h-4 text-red-500" />
            )}
          </div>
        )
      }
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '120px',
      sortable: false,
      render: (_, row) => (
        <div className="flex items-center gap-1">
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<Eye className="w-4 h-4" />}
            onClick={() => onViewAssignment?.(row)}
            tooltip="View details"
          />
          {row.status === 'active' && (
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<RotateCcw className="w-4 h-4" />}
              onClick={() => onReturnAsset?.(row)}
              tooltip="Return asset"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            />
          )}
        </div>
      )
    }
  ]

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Asset Assignments</h2>
          <p className="text-gray-600">Track asset assignments and returns</p>
        </div>
        
        <ActionButton
          variant="primary"
          size="sm"
          icon={<Plus className="w-4 h-4" />}
          onClick={onCreateAssignment}
        >
          New Assignment
        </ActionButton>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Total Assignments</div>
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Active</div>
          <div className="text-2xl font-bold text-green-600">{stats.active}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Overdue</div>
          <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Returned</div>
          <div className="text-2xl font-bold text-gray-600">{stats.returned}</div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={assignments}
        columns={columns}
        loading={loading}
        emptyMessage="No assignments found"
        getRowKey={(row) => row.id}
        sortable
        defaultSortKey="assignmentDate"
        defaultSortOrder="desc"
        mobileMode="cards"
        onRowClick={onViewAssignment}
      />

      {/* Summary */}
      {!loading && (
        <div className="text-sm text-gray-600">
          Showing {assignments.length} assignments
          {stats.overdue > 0 && (
            <span className="ml-2 text-red-600 font-medium">
              • {stats.overdue} overdue
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default AssignmentsView
