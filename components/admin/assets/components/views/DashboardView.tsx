/**
 * DashboardView Component
 * Main dashboard view with stats, recent activities, and compliance alerts
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { StatsCard, StatsGrid } from '@/components/admin/shared/ui'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, DashboardStats } from '../../types'
import { Bell, Settings, AlertTriangle } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface DashboardViewProps extends BaseComponentProps {
  /** Dashboard statistics */
  stats?: DashboardStats
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Refresh handler */
  onRefresh?: () => void
}

// ============================================================================
// MOCK DATA
// ============================================================================

const mockRecentActivities = [
  {
    id: '1',
    type: 'assignment',
    message: 'Asset AST123459 assigned to Mike Chen',
    timestamp: '2 hours ago',
    status: 'success'
  },
  {
    id: '2',
    type: 'maintenance',
    message: 'Maintenance completed for AST123456',
    timestamp: '4 hours ago',
    status: 'info'
  },
  {
    id: '3',
    type: 'warranty',
    message: 'Warranty expiring for 5 assets',
    timestamp: '1 day ago',
    status: 'warning'
  },
  {
    id: '4',
    type: 'return',
    message: 'Asset AST123457 returned by Sarah Johnson',
    timestamp: '2 days ago',
    status: 'success'
  }
]

const mockComplianceAlerts = [
  {
    id: '1',
    type: 'critical',
    message: '3 assets with expired warranties',
    description: 'Action required',
    priority: 'high'
  },
  {
    id: '2',
    type: 'warning',
    message: 'Annual audit due in 15 days',
    description: 'Schedule preparation',
    priority: 'medium'
  },
  {
    id: '3',
    type: 'overdue',
    message: '5 overdue maintenance items',
    description: 'Schedule immediately',
    priority: 'high'
  },
  {
    id: '4',
    type: 'info',
    message: 'New compliance regulations effective next month',
    description: 'Review and update policies',
    priority: 'low'
  }
]

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * DashboardView component displaying overview and key metrics
 */
export const DashboardView: React.FC<DashboardViewProps> = ({
  stats,
  loading = false,
  error,
  onRefresh,
  className,
  'data-testid': testId,
}) => {
  // Default stats if none provided
  const defaultStats: DashboardStats = {
    totalAssets: 1247,
    activeAssignments: 892,
    pendingMaintenance: 23,
    totalValue: 2400000,
    utilizationRate: 71.5,
    overdueItems: 5,
    monthlyGrowth: 12,
    maintenanceCosts: 45000
  }

  const currentStats = stats || defaultStats

  // Get status indicator color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'error':
        return 'bg-red-500'
      case 'info':
        return 'bg-blue-500'
      default:
        return 'bg-gray-500'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  if (error) {
    return (
      <div className={cn('p-6', className)} data-testid={testId}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Dashboard</h3>
          <p className="text-red-700 mb-4">{error}</p>
          {onRefresh && (
            <ActionButton
              variant="outline"
              onClick={onRefresh}
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              Try Again
            </ActionButton>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Dashboard</h2>
          <p className="text-gray-600">Overview of your asset management system</p>
        </div>
        
        <div className="flex items-center gap-3">
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Bell className="w-4 h-4" />}
            badge={3}
            badgeColor="error"
          >
            Notifications
          </ActionButton>
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Settings className="w-4 h-4" />}
          >
            Settings
          </ActionButton>
        </div>
      </div>

      {/* Stats Grid */}
      <StatsGrid columns={4} loading={loading}>
        <StatsCard
          title="Total Assets"
          value={currentStats.totalAssets}
          subtitle={`+${currentStats.monthlyGrowth}% from last month`}
          trend={{
            value: currentStats.monthlyGrowth,
            label: 'from last month',
            direction: 'up'
          }}
          variant="default"
          loading={loading}
        />

        <StatsCard
          title="Active Assignments"
          value={currentStats.activeAssignments}
          subtitle={`${currentStats.utilizationRate}% utilization rate`}
          trend={{
            value: currentStats.utilizationRate,
            label: 'utilization rate',
            direction: 'up'
          }}
          variant="success"
          loading={loading}
        />

        <StatsCard
          title="Pending Maintenance"
          value={currentStats.pendingMaintenance}
          subtitle={`${currentStats.overdueItems} overdue items`}
          trend={{
            value: -8,
            label: 'from last week',
            direction: 'down'
          }}
          variant={currentStats.overdueItems > 0 ? 'warning' : 'default'}
          loading={loading}
        />

        <StatsCard
          title="Total Value"
          value={formatCurrency(currentStats.totalValue)}
          subtitle="Current depreciated value"
          trend={{
            value: 5.2,
            label: 'from last quarter',
            direction: 'up'
          }}
          variant="info"
          loading={loading}
        />
      </StatsGrid>

      {/* Recent Activities and Compliance Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700"
            >
              View All
            </ActionButton>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">Latest asset management activities</p>
          
          <div className="space-y-4">
            {mockRecentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3">
                <div className={cn(
                  'w-2 h-2 rounded-full mt-2 flex-shrink-0',
                  getStatusColor(activity.status)
                )} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.message}
                  </p>
                  <p className="text-xs text-gray-500">{activity.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
          
          {mockRecentActivities.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No recent activities</p>
            </div>
          )}
        </div>

        {/* Compliance Alerts */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Compliance Alerts</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700"
            >
              Manage
            </ActionButton>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">Items requiring attention</p>
          
          <div className="space-y-4">
            {mockComplianceAlerts.map((alert) => (
              <div key={alert.id} className="flex items-start gap-3">
                <div className={cn(
                  'w-2 h-2 rounded-full mt-2 flex-shrink-0',
                  getPriorityColor(alert.priority)
                )} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {alert.message}
                  </p>
                  <p className="text-xs text-gray-500">{alert.description}</p>
                </div>
                {alert.priority === 'high' && (
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Urgent
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {mockComplianceAlerts.length === 0 && (
            <div className="text-center py-8">
              <div className="text-green-600 mb-2">
                <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-gray-500">All compliance requirements met</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default DashboardView
