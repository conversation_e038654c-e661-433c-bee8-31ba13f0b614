/**
 * EmployeePortalView Component
 * Self-service portal for employees to manage their assets
 */

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { ActionButton } from '../ui/ActionButton'
import { DataTable } from '../ui/DataTable'
import { BaseComponentProps, Asset, Assignment, TableColumn } from '../../types'
import { 
  Plus, 
  AlertTriangle, 
  RotateCcw, 
  User, 
  Calendar,
  Package,
  Clock,
  CheckCircle
} from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface EmployeePortalViewProps extends BaseComponentProps {
  /** Current user's assignments */
  userAssignments?: Assignment[]
  /** Available assets for request */
  availableAssets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Request asset handler */
  onRequestAsset?: () => void
  /** Report issue handler */
  onReportIssue?: (assignment: Assignment) => void
  /** Return asset handler */
  onReturnAsset?: (assignment: Assignment) => void
  /** View assignment handler */
  onViewAssignment?: (assignment: Assignment) => void
}

// ============================================================================
// MOCK DATA
// ============================================================================

const mockUserAssignments: Assignment[] = [
  {
    id: 'ASG001',
    assetId: 'AST123456',
    assigneeId: 'current-user',
    assigneeName: 'Current User',
    type: 'employee',
    purpose: 'Daily work laptop for development tasks',
    assignmentDate: '2024-01-15',
    returnDate: '2024-12-31',
    status: 'active',
    notes: 'Includes charger and laptop bag',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    asset: {
      id: 'AST123456',
      name: 'Dell Laptop XPS 13',
      type: 'laptop',
      status: 'assigned',
      condition: 'good',
      location: 'NYC Office - Floor 3',
      purchaseCost: 1200,
      currentValue: 800,
      purchaseDate: '2023-01-15',
      vendor: 'Dell Inc.',
      serialNumber: 'DL123456789',
      model: 'XPS 13 9310',
      category: 'IT Equipment',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  },
  {
    id: 'ASG003',
    assetId: 'AST123459',
    assigneeId: 'current-user',
    assigneeName: 'Current User',
    type: 'employee',
    purpose: 'Mobile device for field work and communication',
    assignmentDate: '2024-02-10',
    returnDate: '2024-08-10',
    status: 'active',
    notes: 'Company phone with data plan',
    createdAt: '2024-02-10T00:00:00Z',
    updatedAt: '2024-02-10T00:00:00Z',
    asset: {
      id: 'AST123459',
      name: 'iPhone 14 Pro',
      type: 'mobile-device',
      status: 'assigned',
      condition: 'excellent',
      location: 'Mobile',
      purchaseCost: 999,
      currentValue: 700,
      purchaseDate: '2023-09-15',
      vendor: 'Apple Inc.',
      serialNumber: 'IP14PRO123456',
      model: 'iPhone 14 Pro 256GB',
      category: 'Mobile Device',
      createdAt: '2023-09-15T00:00:00Z',
      updatedAt: '2023-09-15T00:00:00Z'
    }
  }
]

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * EmployeePortalView component for employee self-service
 */
export const EmployeePortalView: React.FC<EmployeePortalViewProps> = ({
  userAssignments = mockUserAssignments,
  availableAssets = [],
  loading = false,
  error,
  onRequestAsset,
  onReportIssue,
  onReturnAsset,
  onViewAssignment,
  className,
  'data-testid': testId,
}) => {
  const [activeSection, setActiveSection] = useState<'assets' | 'requests' | 'history'>('assets')

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Check if assignment is overdue
  const isOverdue = (assignment: Assignment) => {
    if (assignment.status !== 'active' || !assignment.returnDate) return false
    return new Date(assignment.returnDate) < new Date()
  }

  // Get status variant
  const getStatusVariant = (assignment: Assignment) => {
    if (isOverdue(assignment)) return 'destructive'
    return assignment.status === 'active' ? 'success' : 'secondary'
  }

  // Table columns for assignments
  const columns: TableColumn<Assignment>[] = [
    {
      key: 'asset',
      label: 'Asset',
      render: (_, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Package className="w-5 h-5 text-gray-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{row.asset?.name || 'Unknown Asset'}</div>
            <div className="text-sm text-gray-500">{row.asset?.model}</div>
          </div>
        </div>
      )
    },
    {
      key: 'assignmentDate',
      label: 'Assigned Date',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{formatDate(value)}</span>
        </div>
      )
    },
    {
      key: 'returnDate',
      label: 'Return Date',
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <span className={cn(
            'text-sm',
            isOverdue(row) ? 'text-red-600 font-medium' : 'text-gray-700'
          )}>
            {value ? formatDate(value) : 'Permanent'}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (_, row) => {
        const variant = getStatusVariant(row)
        const text = isOverdue(row) ? 'Overdue' : row.status
        
        return (
          <div className="flex items-center gap-2">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize',
              {
                'bg-green-100 text-green-800': variant === 'success',
                'bg-gray-100 text-gray-800': variant === 'secondary',
                'bg-red-100 text-red-800': variant === 'destructive'
              }
            )}>
              {text}
            </span>
            {isOverdue(row) && (
              <AlertTriangle className="w-4 h-4 text-red-500" />
            )}
          </div>
        )
      }
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      render: (_, row) => (
        <div className="flex items-center gap-1">
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<AlertTriangle className="w-4 h-4" />}
            onClick={() => onReportIssue?.(row)}
            tooltip="Report issue"
            className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
          />
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<RotateCcw className="w-4 h-4" />}
            onClick={() => onReturnAsset?.(row)}
            tooltip="Return asset"
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          />
        </div>
      )
    }
  ]

  if (error) {
    return (
      <div className={cn('p-6', className)} data-testid={testId}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Portal</h3>
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Employee Portal</h2>
          <p className="text-gray-600">Manage your assigned assets and requests</p>
        </div>
        
        <ActionButton
          variant="primary"
          size="sm"
          icon={<Plus className="w-4 h-4" />}
          onClick={onRequestAsset}
        >
          Request Asset
        </ActionButton>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <div className="text-sm text-gray-600">My Assets</div>
              <div className="text-2xl font-bold text-gray-900">{userAssignments.length}</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <div className="text-sm text-gray-600">Active</div>
              <div className="text-2xl font-bold text-green-600">
                {userAssignments.filter(a => a.status === 'active').length}
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <div className="text-sm text-gray-600">Overdue</div>
              <div className="text-2xl font-bold text-red-600">
                {userAssignments.filter(a => isOverdue(a)).length}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="flex gap-1 bg-gray-100 p-1 rounded-lg w-fit">
        {[
          { id: 'assets', label: 'My Assets' },
          { id: 'requests', label: 'Requests' },
          { id: 'history', label: 'History' }
        ].map((section) => (
          <button
            key={section.id}
            onClick={() => setActiveSection(section.id as any)}
            className={cn(
              'px-4 py-2 rounded-md text-sm font-medium transition-colors',
              activeSection === section.id
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            )}
          >
            {section.label}
          </button>
        ))}
      </div>

      {/* Content */}
      {activeSection === 'assets' && (
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <ActionButton
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={onRequestAsset}
              >
                <div className="flex items-center gap-3">
                  <Plus className="w-5 h-5 text-blue-600" />
                  <div className="text-left">
                    <div className="font-medium">Request Asset</div>
                    <div className="text-sm text-gray-500">Request new equipment</div>
                  </div>
                </div>
              </ActionButton>
              
              <ActionButton
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => userAssignments.length > 0 && onReportIssue?.(userAssignments[0])}
              >
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  <div className="text-left">
                    <div className="font-medium">Report Issue</div>
                    <div className="text-sm text-gray-500">Report asset problems</div>
                  </div>
                </div>
              </ActionButton>
              
              <ActionButton
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => userAssignments.length > 0 && onReturnAsset?.(userAssignments[0])}
              >
                <div className="flex items-center gap-3">
                  <RotateCcw className="w-5 h-5 text-green-600" />
                  <div className="text-left">
                    <div className="font-medium">Return Asset</div>
                    <div className="text-sm text-gray-500">Return equipment</div>
                  </div>
                </div>
              </ActionButton>
            </div>
          </div>

          {/* My Assets Table */}
          <DataTable
            data={userAssignments}
            columns={columns}
            loading={loading}
            emptyMessage="No assets assigned to you"
            getRowKey={(row) => row.id}
            sortable
            defaultSortKey="assignmentDate"
            defaultSortOrder="desc"
            mobileMode="cards"
            onRowClick={onViewAssignment}
          />
        </div>
      )}

      {activeSection === 'requests' && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Asset Requests</h3>
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">No pending requests</p>
            <ActionButton
              variant="primary"
              onClick={onRequestAsset}
              icon={<Plus className="w-4 h-4" />}
            >
              Request New Asset
            </ActionButton>
          </div>
        </div>
      )}

      {activeSection === 'history' && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Assignment History</h3>
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No assignment history available</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default EmployeePortalView
