/**
 * InventoryView Component
 * Asset inventory management with filtering, search, and actions
 */

import React, { useState, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { DataTable } from '../ui/DataTable'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, Asset, AssetFilter, TableColumn } from '../../types'
import { 
  Plus, 
  Upload, 
  Download, 
  Search, 
  Filter,
  Edit,
  Trash2,
  Eye,
  MapPin,
  User,
  Calendar
} from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface InventoryViewProps extends BaseComponentProps {
  /** Asset data */
  assets: Asset[]
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Search query */
  searchQuery?: string
  /** Active filters */
  filters?: AssetFilter
  /** Selected assets */
  selectedAssets?: string[]
  /** Search handler */
  onSearch?: (query: string) => void
  /** Filter handler */
  onFilterChange?: (filters: AssetFilter) => void
  /** Selection handler */
  onSelectionChange?: (selectedIds: string[]) => void
  /** Add asset handler */
  onAddAsset?: () => void
  /** Edit asset handler */
  onEditAsset?: (asset: Asset) => void
  /** Delete asset handler */
  onDeleteAsset?: (asset: Asset) => void
  /** View asset handler */
  onViewAsset?: (asset: Asset) => void
  /** Import handler */
  onImport?: () => void
  /** Export handler */
  onExport?: () => void
}

// ============================================================================
// MOCK DATA
// ============================================================================

const mockAssets: Asset[] = [
  {
    id: 'AST123456',
    name: 'Dell Laptop XPS 13',
    type: 'laptop',
    status: 'assigned',
    condition: 'good',
    location: 'NYC Office - Floor 3',
    assignedToName: 'John Smith',
    assignedToId: 'john-smith',
    purchaseCost: 1200,
    currentValue: 800,
    purchaseDate: '2023-01-15',
    warrantyExpiry: '2025-01-15',
    vendor: 'Dell Inc.',
    serialNumber: 'DL123456789',
    model: 'XPS 13 9310',
    category: 'IT Equipment',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2023-01-15T00:00:00Z'
  },
  {
    id: 'AST123457',
    name: 'Herman Miller Chair',
    type: 'furniture',
    status: 'available',
    condition: 'excellent',
    location: 'NYC Office - Floor 2',
    purchaseCost: 899,
    currentValue: 650,
    purchaseDate: '2023-03-20',
    warrantyExpiry: '2033-03-20',
    vendor: 'Herman Miller',
    serialNumber: 'HM987654321',
    model: 'Aeron Chair',
    category: 'Office Furniture',
    createdAt: '2023-03-20T00:00:00Z',
    updatedAt: '2023-03-20T00:00:00Z'
  },
  {
    id: 'AST123458',
    name: 'Toyota Camry 2023',
    type: 'vehicle',
    status: 'assigned',
    condition: 'excellent',
    location: 'Parking Garage Level 1',
    assignedToName: 'Sales Department',
    assignedToId: 'sales-dept',
    purchaseCost: 26999,
    currentValue: 24000,
    purchaseDate: '2023-06-10',
    warrantyExpiry: '2026-06-10',
    vendor: 'Toyota',
    serialNumber: 'TC2023456789',
    model: 'Camry LE',
    category: 'Vehicle',
    createdAt: '2023-06-10T00:00:00Z',
    updatedAt: '2023-06-10T00:00:00Z'
  }
]

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * InventoryView component for asset inventory management
 */
export const InventoryView: React.FC<InventoryViewProps> = ({
  assets = mockAssets,
  loading = false,
  error,
  searchQuery = '',
  filters = {},
  selectedAssets = [],
  onSearch,
  onFilterChange,
  onSelectionChange,
  onAddAsset,
  onEditAsset,
  onDeleteAsset,
  onViewAsset,
  onImport,
  onExport,
  className,
  'data-testid': testId,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  // Filter and search assets
  const filteredAssets = useMemo(() => {
    let filtered = [...assets]

    // Apply search
    if (localSearchQuery) {
      const query = localSearchQuery.toLowerCase()
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.id.toLowerCase().includes(query) ||
        asset.serialNumber.toLowerCase().includes(query) ||
        asset.assignedToName?.toLowerCase().includes(query) ||
        asset.location.toLowerCase().includes(query)
      )
    }

    // Apply filters
    if (filters.types?.length) {
      filtered = filtered.filter(asset => filters.types!.includes(asset.type))
    }
    if (filters.statuses?.length) {
      filtered = filtered.filter(asset => filters.statuses!.includes(asset.status))
    }
    if (filters.conditions?.length) {
      filtered = filtered.filter(asset => filters.conditions!.includes(asset.condition))
    }
    if (filters.locations?.length) {
      filtered = filtered.filter(asset => filters.locations!.includes(asset.location))
    }

    return filtered
  }, [assets, localSearchQuery, filters])

  // Handle search
  const handleSearch = (query: string) => {
    setLocalSearchQuery(query)
    onSearch?.(query)
  }

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'available':
        return 'success'
      case 'assigned':
        return 'primary'
      case 'maintenance':
        return 'warning'
      case 'retired':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  // Get condition badge variant
  const getConditionVariant = (condition: string) => {
    switch (condition) {
      case 'excellent':
        return 'success'
      case 'good':
        return 'primary'
      case 'fair':
        return 'warning'
      case 'poor':
      case 'damaged':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  // Table columns
  const columns: TableColumn<Asset>[] = [
    {
      key: 'id',
      label: 'Asset ID',
      width: '120px',
      render: (value) => (
        <span className="font-mono text-sm text-gray-600">{value}</span>
      )
    },
    {
      key: 'name',
      label: 'Name',
      render: (value, row) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{row.model}</div>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (value) => (
        <span className="capitalize text-sm text-gray-700">{value.replace('-', ' ')}</span>
      )
    },
    {
      key: 'assignedToName',
      label: 'Assigned To',
      render: (value) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{value || 'Unassigned'}</span>
        </div>
      )
    },
    {
      key: 'location',
      label: 'Location',
      render: (value) => (
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{value}</span>
        </div>
      )
    },
    {
      key: 'condition',
      label: 'Condition',
      width: '100px',
      render: (value) => (
        <span className={cn(
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize',
          {
            'bg-green-100 text-green-800': value === 'excellent',
            'bg-blue-100 text-blue-800': value === 'good',
            'bg-yellow-100 text-yellow-800': value === 'fair',
            'bg-red-100 text-red-800': value === 'poor' || value === 'damaged'
          }
        )}>
          {value}
        </span>
      )
    },
    {
      key: 'currentValue',
      label: 'Value',
      width: '120px',
      align: 'right',
      render: (value) => (
        <span className="font-medium text-gray-900">{formatCurrency(value)}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '120px',
      sortable: false,
      render: (_, row) => (
        <div className="flex items-center gap-1">
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<Eye className="w-4 h-4" />}
            onClick={() => onViewAsset?.(row)}
            tooltip="View details"
          />
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<Edit className="w-4 h-4" />}
            onClick={() => onEditAsset?.(row)}
            tooltip="Edit asset"
          />
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<Trash2 className="w-4 h-4" />}
            onClick={() => onDeleteAsset?.(row)}
            tooltip="Delete asset"
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          />
        </div>
      )
    }
  ]

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Asset Inventory</h2>
          <p className="text-gray-600">Manage your organization's assets</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Upload className="w-4 h-4" />}
            onClick={onImport}
          >
            Import
          </ActionButton>
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Download className="w-4 h-4" />}
            onClick={onExport}
          >
            Export
          </ActionButton>
          <ActionButton
            variant="primary"
            size="sm"
            icon={<Plus className="w-4 h-4" />}
            onClick={onAddAsset}
          >
            Add Asset
          </ActionButton>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search Documents, tags, or folders..."
            value={localSearchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div className="flex gap-2">
          <select 
            className="px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => {
              const types = e.target.value ? [e.target.value as any] : []
              onFilterChange?.({ ...filters, types })
            }}
          >
            <option value="">All Types</option>
            <option value="laptop">Laptop</option>
            <option value="desktop">Desktop</option>
            <option value="mobile-device">Mobile Device</option>
            <option value="furniture">Furniture</option>
            <option value="vehicle">Vehicle</option>
            <option value="equipment">Equipment</option>
          </select>
          
          <select 
            className="px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => {
              const conditions = e.target.value ? [e.target.value as any] : []
              onFilterChange?.({ ...filters, conditions })
            }}
          >
            <option value="">All Conditions</option>
            <option value="excellent">Excellent</option>
            <option value="good">Good</option>
            <option value="fair">Fair</option>
            <option value="poor">Poor</option>
            <option value="damaged">Damaged</option>
          </select>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredAssets}
        columns={columns}
        loading={loading}
        emptyMessage="No assets found"
        selectable
        selectedRows={selectedAssets}
        onSelectionChange={onSelectionChange}
        getRowKey={(row) => row.id}
        sortable
        defaultSortKey="name"
        mobileMode="cards"
      />

      {/* Summary */}
      {!loading && (
        <div className="text-sm text-gray-600">
          Showing {filteredAssets.length} of {assets.length} assets
          {selectedAssets.length > 0 && (
            <span className="ml-2">
              • {selectedAssets.length} selected
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default InventoryView
