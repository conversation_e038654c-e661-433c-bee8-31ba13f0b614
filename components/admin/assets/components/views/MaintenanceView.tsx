/**
 * MaintenanceView Component
 * Maintenance schedule management with preventive and corrective maintenance
 */

import React, { useMemo } from 'react'
import { cn } from '@/lib/utils'
import { DataTable } from '../ui/DataTable'
import { ActionButton, ButtonGroup } from '../ui/ActionButton'
import { BaseComponentProps, MaintenanceRecord, Asset, TableColumn } from '../../types'
import { 
  Plus, 
  Wrench,
  Eye, 
  Calendar,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface MaintenanceViewProps extends BaseComponentProps {
  /** Maintenance records */
  maintenanceRecords?: MaintenanceRecord[]
  /** Asset data for reference */
  assets?: Asset[]
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Schedule maintenance handler */
  onScheduleMaintenance?: () => void
  /** Request repair handler */
  onRequestRepair?: () => void
  /** View maintenance handler */
  onViewMaintenance?: (record: MaintenanceRecord) => void
  /** Complete maintenance handler */
  onCompleteMaintenance?: (record: MaintenanceRecord) => void
  /** Cancel maintenance handler */
  onCancelMaintenance?: (record: MaintenanceRecord) => void
}

// ============================================================================
// MOCK DATA
// ============================================================================

const mockMaintenanceRecords: MaintenanceRecord[] = [
  {
    id: 'MNT001',
    assetId: 'AST123458',
    type: 'preventive',
    frequency: 'quarterly',
    scheduledDate: '2024-07-15',
    technicianId: 'tech-001',
    technicianName: 'Auto Service Center',
    status: 'scheduled',
    priority: 'normal',
    estimatedCost: 500,
    estimatedDowntime: 4,
    description: 'Quarterly vehicle maintenance and inspection',
    notes: 'Oil change, tire rotation, brake inspection',
    warrantyStatus: 'covered',
    createdAt: '2024-06-15T00:00:00Z',
    updatedAt: '2024-06-15T00:00:00Z',
    asset: {
      id: 'AST123458',
      name: 'Toyota Camry 2023',
      type: 'vehicle',
      status: 'assigned',
      condition: 'excellent',
      location: 'Parking Garage Level 1',
      purchaseCost: 26999,
      currentValue: 24000,
      purchaseDate: '2023-06-10',
      vendor: 'Toyota',
      serialNumber: 'TC2023456789',
      model: 'Camry LE',
      category: 'Vehicle',
      createdAt: '2023-06-10T00:00:00Z',
      updatedAt: '2023-06-10T00:00:00Z'
    }
  },
  {
    id: 'MNT002',
    assetId: 'AST123456',
    type: 'corrective',
    frequency: 'one-time',
    scheduledDate: '2024-06-25',
    completedDate: '2024-06-26',
    technicianId: 'tech-002',
    technicianName: 'IT Support Team',
    status: 'overdue',
    priority: 'high',
    estimatedCost: 200,
    actualCost: 150,
    estimatedDowntime: 2,
    actualDowntime: 1.5,
    description: 'Laptop keyboard replacement and system update',
    notes: 'Keyboard was not responding properly, replaced with new one',
    warrantyStatus: 'not-covered',
    createdAt: '2024-06-20T00:00:00Z',
    updatedAt: '2024-06-26T00:00:00Z',
    asset: {
      id: 'AST123456',
      name: 'Dell Laptop XPS 13',
      type: 'laptop',
      status: 'assigned',
      condition: 'good',
      location: 'NYC Office - Floor 3',
      purchaseCost: 1200,
      currentValue: 800,
      purchaseDate: '2023-01-15',
      vendor: 'Dell Inc.',
      serialNumber: 'DL123456789',
      model: 'XPS 13 9310',
      category: 'IT Equipment',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  },
  {
    id: 'MNT003',
    assetId: 'AST123457',
    type: 'preventive',
    frequency: 'annually',
    scheduledDate: '2024-08-01',
    technicianId: 'tech-003',
    technicianName: 'Furniture Maintenance Co.',
    status: 'in-progress',
    priority: 'low',
    estimatedCost: 100,
    estimatedDowntime: 0.5,
    description: 'Annual chair maintenance and adjustment',
    notes: 'Check hydraulics, clean and lubricate moving parts',
    warrantyStatus: 'covered',
    createdAt: '2024-07-01T00:00:00Z',
    updatedAt: '2024-07-15T00:00:00Z',
    asset: {
      id: 'AST123457',
      name: 'Herman Miller Chair',
      type: 'furniture',
      status: 'available',
      condition: 'excellent',
      location: 'NYC Office - Floor 2',
      purchaseCost: 899,
      currentValue: 650,
      purchaseDate: '2023-03-20',
      vendor: 'Herman Miller',
      serialNumber: 'HM987654321',
      model: 'Aeron Chair',
      category: 'Office Furniture',
      createdAt: '2023-03-20T00:00:00Z',
      updatedAt: '2023-03-20T00:00:00Z'
    }
  }
]

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * MaintenanceView component for managing maintenance schedules
 */
export const MaintenanceView: React.FC<MaintenanceViewProps> = ({
  maintenanceRecords = mockMaintenanceRecords,
  assets = [],
  loading = false,
  error,
  onScheduleMaintenance,
  onRequestRepair,
  onViewMaintenance,
  onCompleteMaintenance,
  onCancelMaintenance,
  className,
  'data-testid': testId,
}) => {
  // Calculate maintenance statistics
  const stats = useMemo(() => {
    const total = maintenanceRecords.length
    const scheduled = maintenanceRecords.filter(r => r.status === 'scheduled').length
    const inProgress = maintenanceRecords.filter(r => r.status === 'in-progress').length
    const overdue = maintenanceRecords.filter(r => r.status === 'overdue').length
    const completed = maintenanceRecords.filter(r => r.status === 'completed').length
    const totalCost = maintenanceRecords.reduce((sum, r) => sum + (r.actualCost || r.estimatedCost || 0), 0)

    return { total, scheduled, inProgress, overdue, completed, totalCost }
  }, [maintenanceRecords])

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'primary'
      case 'in-progress':
        return 'warning'
      case 'completed':
        return 'success'
      case 'cancelled':
        return 'secondary'
      case 'overdue':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  // Get priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'warning'
      case 'normal':
        return 'primary'
      case 'low':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  // Table columns
  const columns: TableColumn<MaintenanceRecord>[] = [
    {
      key: 'id',
      label: 'Request ID',
      width: '120px',
      render: (value) => (
        <span className="font-mono text-sm text-gray-600">{value}</span>
      )
    },
    {
      key: 'asset',
      label: 'Asset',
      render: (_, row) => (
        <div>
          <div className="font-medium text-gray-900">{row.asset?.name || 'Unknown Asset'}</div>
          <div className="text-sm text-gray-500 font-mono">{row.assetId}</div>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
          {value}
        </span>
      )
    },
    {
      key: 'scheduledDate',
      label: 'Scheduled Date',
      width: '140px',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{formatDate(value)}</span>
        </div>
      )
    },
    {
      key: 'technicianName',
      label: 'Technician',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Wrench className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-700">{value || 'Unassigned'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      width: '100px',
      render: (value, row) => {
        const variant = getStatusVariant(value)
        
        return (
          <div className="flex items-center gap-2">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize',
              {
                'bg-blue-100 text-blue-800': variant === 'primary',
                'bg-yellow-100 text-yellow-800': variant === 'warning',
                'bg-green-100 text-green-800': variant === 'success',
                'bg-gray-100 text-gray-800': variant === 'secondary',
                'bg-red-100 text-red-800': variant === 'destructive'
              }
            )}>
              {value.replace('-', ' ')}
            </span>
            {row.priority === 'critical' && (
              <AlertTriangle className="w-4 h-4 text-red-500" />
            )}
          </div>
        )
      }
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '140px',
      sortable: false,
      render: (_, row) => (
        <div className="flex items-center gap-1">
          <ActionButton
            variant="ghost"
            size="sm"
            icon={<Eye className="w-4 h-4" />}
            onClick={() => onViewMaintenance?.(row)}
            tooltip="View details"
          />
          {row.status === 'scheduled' && (
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<CheckCircle className="w-4 h-4" />}
              onClick={() => onCompleteMaintenance?.(row)}
              tooltip="Mark complete"
              className="text-green-600 hover:text-green-700 hover:bg-green-50"
            />
          )}
          {(row.status === 'scheduled' || row.status === 'in-progress') && (
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<XCircle className="w-4 h-4" />}
              onClick={() => onCancelMaintenance?.(row)}
              tooltip="Cancel"
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            />
          )}
        </div>
      )
    }
  ]

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Maintenance Schedule</h2>
          <p className="text-gray-600">Manage preventive and corrective maintenance</p>
        </div>
        
        <ButtonGroup gap="sm">
          <ActionButton
            variant="primary"
            size="sm"
            icon={<Plus className="w-4 h-4" />}
            onClick={onScheduleMaintenance}
          >
            Schedule maintenance
          </ActionButton>
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Wrench className="w-4 h-4" />}
            onClick={onRequestRepair}
          >
            Request Repair
          </ActionButton>
        </ButtonGroup>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Total</div>
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Scheduled</div>
          <div className="text-2xl font-bold text-blue-600">{stats.scheduled}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">In Progress</div>
          <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Overdue</div>
          <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Completed</div>
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-sm text-gray-600 mb-1">Total Cost</div>
          <div className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalCost)}</div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={maintenanceRecords}
        columns={columns}
        loading={loading}
        emptyMessage="No maintenance records found"
        getRowKey={(row) => row.id}
        sortable
        defaultSortKey="scheduledDate"
        defaultSortOrder="asc"
        mobileMode="cards"
        onRowClick={onViewMaintenance}
      />

      {/* Summary */}
      {!loading && (
        <div className="text-sm text-gray-600">
          Showing {maintenanceRecords.length} maintenance records
          {stats.overdue > 0 && (
            <span className="ml-2 text-red-600 font-medium">
              • {stats.overdue} overdue
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default MaintenanceView
