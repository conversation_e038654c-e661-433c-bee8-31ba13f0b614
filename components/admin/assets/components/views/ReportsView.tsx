/**
 * ReportsView Component
 * Asset analytics and reporting dashboard
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { StatsCard, StatsGrid } from '@/components/admin/shared/ui'
import { ActionButton } from '../ui/ActionButton'
import { BaseComponentProps, DashboardStats } from '../../types'
import { Download, FileText, TrendingUp, BarChart3 } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface ReportsViewProps extends BaseComponentProps {
  /** Dashboard statistics */
  stats?: DashboardStats
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Export handler */
  onExport?: (format: 'pdf' | 'excel' | 'csv') => void
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * ReportsView component for analytics and reporting
 */
export const ReportsView: React.FC<ReportsViewProps> = ({
  stats,
  loading = false,
  error,
  onExport,
  className,
  'data-testid': testId,
}) => {
  // Mock chart data
  const utilizationData = [
    { label: 'Laptops', value: 85, color: '#3B82F6' },
    { label: 'Furniture', value: 72, color: '#10B981' },
    { label: 'Vehicles', value: 90, color: '#F59E0B' },
    { label: 'Equipment', value: 65, color: '#EF4444' }
  ]

  const maintenanceCosts = [
    { month: 'Jan', cost: 12000 },
    { month: 'Feb', cost: 15000 },
    { month: 'Mar', cost: 8000 },
    { month: 'Apr', cost: 18000 },
    { month: 'May', cost: 14000 },
    { month: 'Jun', cost: 16000 }
  ]

  if (error) {
    return (
      <div className={cn('p-6', className)} data-testid={testId}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">
            <FileText className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Reports</h3>
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Reports & Analytics</h2>
          <p className="text-gray-600">Comprehensive asset management insights</p>
        </div>
        
        <div className="flex items-center gap-2">
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Download className="w-4 h-4" />}
            onClick={() => onExport?.('pdf')}
          >
            Export PDF
          </ActionButton>
          <ActionButton
            variant="outline"
            size="sm"
            icon={<Download className="w-4 h-4" />}
            onClick={() => onExport?.('excel')}
          >
            Export Excel
          </ActionButton>
        </div>
      </div>

      {/* Key Metrics */}
      <StatsGrid columns={4} loading={loading}>
        <StatsCard
          title="Asset Utilization Rate"
          value="78.5%"
          subtitle="Average across all categories"
          icon={<TrendingUp className="w-5 h-5" />}
          trend={{
            value: 5.2,
            label: 'from last month',
            direction: 'up'
          }}
          variant="success"
          loading={loading}
        />
        
        <StatsCard
          title="Maintenance Efficiency"
          value="92%"
          subtitle="On-time completion rate"
          icon={<BarChart3 className="w-5 h-5" />}
          trend={{
            value: 3.1,
            label: 'from last month',
            direction: 'up'
          }}
          variant="info"
          loading={loading}
        />
        
        <StatsCard
          title="Cost Savings"
          value="$45,200"
          subtitle="Preventive maintenance savings"
          icon={<TrendingUp className="w-5 h-5" />}
          trend={{
            value: 12.8,
            label: 'from last quarter',
            direction: 'up'
          }}
          variant="success"
          loading={loading}
        />
        
        <StatsCard
          title="Compliance Score"
          value="96%"
          subtitle="Regulatory compliance rate"
          icon={<FileText className="w-5 h-5" />}
          trend={{
            value: 2.3,
            label: 'from last audit',
            direction: 'up'
          }}
          variant="info"
          loading={loading}
        />
      </StatsGrid>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Asset Utilization Chart */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Asset Utilization by Type</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<Download className="w-4 h-4" />}
              onClick={() => onExport?.('csv')}
            >
              Export
            </ActionButton>
          </div>
          
          {loading ? (
            <div className="h-64 bg-gray-50 rounded-lg animate-pulse" />
          ) : (
            <div className="h-64 flex items-center justify-center">
              <div className="space-y-4 w-full">
                {utilizationData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">{item.label}</span>
                    <div className="flex items-center gap-3 flex-1 ml-4">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${item.value}%`,
                            backgroundColor: item.color
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12 text-right">
                        {item.value}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Maintenance Costs Chart */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Monthly Maintenance Costs</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<Download className="w-4 h-4" />}
              onClick={() => onExport?.('csv')}
            >
              Export
            </ActionButton>
          </div>
          
          {loading ? (
            <div className="h-64 bg-gray-50 rounded-lg animate-pulse" />
          ) : (
            <div className="h-64 flex items-end justify-between gap-2 p-4">
              {maintenanceCosts.map((item, index) => {
                const maxCost = Math.max(...maintenanceCosts.map(d => d.cost))
                const height = (item.cost / maxCost) * 100
                
                return (
                  <div key={index} className="flex flex-col items-center flex-1">
                    <div className="text-xs font-medium text-gray-900 mb-1">
                      ${(item.cost / 1000).toFixed(0)}K
                    </div>
                    <div
                      className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                      style={{ height: `${height}%`, minHeight: '4px' }}
                    />
                    <div className="text-xs text-gray-600 mt-1">{item.month}</div>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Asset Distribution */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Asset Distribution</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<Download className="w-4 h-4" />}
              onClick={() => onExport?.('csv')}
            >
              Export
            </ActionButton>
          </div>
          
          {loading ? (
            <div className="h-64 bg-gray-50 rounded-lg animate-pulse" />
          ) : (
            <div className="h-64 flex items-center justify-center">
              <div className="text-center">
                <div className="w-32 h-32 mx-auto mb-4 rounded-full border-8 border-gray-200 relative">
                  <div className="absolute inset-0 rounded-full border-8 border-blue-500 border-t-transparent animate-spin" />
                </div>
                <p className="text-gray-500">Interactive pie chart placeholder</p>
                <p className="text-xs text-gray-400 mt-1">Chart library integration needed</p>
              </div>
            </div>
          )}
        </div>

        {/* Depreciation Analysis */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Asset Value Trends</h3>
            <ActionButton
              variant="ghost"
              size="sm"
              icon={<Download className="w-4 h-4" />}
              onClick={() => onExport?.('csv')}
            >
              Export
            </ActionButton>
          </div>
          
          {loading ? (
            <div className="h-64 bg-gray-50 rounded-lg animate-pulse" />
          ) : (
            <div className="h-64 flex items-center justify-center">
              <div className="text-center">
                <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Line chart placeholder</p>
                <p className="text-xs text-gray-400 mt-1">Shows asset depreciation over time</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Summary Table */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Asset Summary by Category</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Assets
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Available
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilization
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {utilizationData.map((category, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {category.label}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.floor(Math.random() * 100) + 50}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.floor(Math.random() * 80) + 30}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.floor(Math.random() * 20) + 5}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${(Math.random() * 500000 + 100000).toLocaleString('en-US', { maximumFractionDigits: 0 })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            width: `${category.value}%`,
                            backgroundColor: category.color
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {category.value}%
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default ReportsView
