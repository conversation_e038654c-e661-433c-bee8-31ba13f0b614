/**
 * AssetContext - Global state management for Asset Management System
 * Provides centralized state management using React Context API
 */

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { 
  AppState, 
  Asset, 
  Assignment, 
  MaintenanceRecord, 
  AssetFilter, 
  TabType,
  DashboardStats,
  Notification,
  AssetFormData,
  AssignmentFormData,
  MaintenanceFormData
} from '../types'

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: AppState = {
  assets: {
    items: [],
    selectedAsset: null,
    filters: {},
    searchQuery: '',
    loading: false,
    error: null,
    stats: null
  },
  assignments: {
    items: [],
    selectedAssignment: null,
    loading: false,
    error: null
  },
  maintenance: {
    items: [],
    selectedRecord: null,
    loading: false,
    error: null
  },
  ui: {
    activeTab: 'dashboard',
    sidebarOpen: false,
    theme: 'light',
    notifications: [],
    dialogs: {
      addAsset: false,
      createAssignment: false,
      scheduleMaintenance: false,
      requestRepair: false
    }
  },
  user: {
    id: 'user-1',
    name: 'balu',
    email: '<EMAIL>',
    role: 'admin',
    permissions: [
      'assets:read', 'assets:write', 'assets:delete',
      'assignments:read', 'assignments:write', 'assignments:delete',
      'maintenance:read', 'maintenance:write', 'maintenance:delete',
      'reports:read', 'reports:export'
    ],
    preferences: {
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/dd/yyyy',
      currency: 'USD',
      notifications: {
        email: true,
        push: true,
        inApp: true
      }
    }
  }
}

// ============================================================================
// ACTION TYPES
// ============================================================================

type AssetAction =
  | { type: 'ASSETS_LOADING'; payload: boolean }
  | { type: 'ASSETS_SET_ERROR'; payload: string | null }
  | { type: 'ASSETS_SET_ITEMS'; payload: Asset[] }
  | { type: 'ASSETS_ADD_ITEM'; payload: Asset }
  | { type: 'ASSETS_UPDATE_ITEM'; payload: Asset }
  | { type: 'ASSETS_DELETE_ITEM'; payload: string }
  | { type: 'ASSETS_SET_SELECTED'; payload: Asset | null }
  | { type: 'ASSETS_SET_FILTERS'; payload: AssetFilter }
  | { type: 'ASSETS_SET_SEARCH'; payload: string }
  | { type: 'ASSETS_SET_STATS'; payload: DashboardStats }
  | { type: 'ASSIGNMENTS_LOADING'; payload: boolean }
  | { type: 'ASSIGNMENTS_SET_ERROR'; payload: string | null }
  | { type: 'ASSIGNMENTS_SET_ITEMS'; payload: Assignment[] }
  | { type: 'ASSIGNMENTS_ADD_ITEM'; payload: Assignment }
  | { type: 'ASSIGNMENTS_UPDATE_ITEM'; payload: Assignment }
  | { type: 'ASSIGNMENTS_DELETE_ITEM'; payload: string }
  | { type: 'ASSIGNMENTS_SET_SELECTED'; payload: Assignment | null }
  | { type: 'MAINTENANCE_LOADING'; payload: boolean }
  | { type: 'MAINTENANCE_SET_ERROR'; payload: string | null }
  | { type: 'MAINTENANCE_SET_ITEMS'; payload: MaintenanceRecord[] }
  | { type: 'MAINTENANCE_ADD_ITEM'; payload: MaintenanceRecord }
  | { type: 'MAINTENANCE_UPDATE_ITEM'; payload: MaintenanceRecord }
  | { type: 'MAINTENANCE_DELETE_ITEM'; payload: string }
  | { type: 'MAINTENANCE_SET_SELECTED'; payload: MaintenanceRecord | null }
  | { type: 'UI_SET_ACTIVE_TAB'; payload: TabType }
  | { type: 'UI_SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'UI_SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'UI_ADD_NOTIFICATION'; payload: Notification }
  | { type: 'UI_REMOVE_NOTIFICATION'; payload: string }
  | { type: 'UI_MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'UI_SET_DIALOG'; payload: { dialog: keyof AppState['ui']['dialogs']; open: boolean } }

// ============================================================================
// REDUCER
// ============================================================================

const assetReducer = (state: AppState, action: AssetAction): AppState => {
  switch (action.type) {
    // Assets
    case 'ASSETS_LOADING':
      return {
        ...state,
        assets: { ...state.assets, loading: action.payload }
      }
    case 'ASSETS_SET_ERROR':
      return {
        ...state,
        assets: { ...state.assets, error: action.payload, loading: false }
      }
    case 'ASSETS_SET_ITEMS':
      return {
        ...state,
        assets: { ...state.assets, items: action.payload, loading: false, error: null }
      }
    case 'ASSETS_ADD_ITEM':
      return {
        ...state,
        assets: { ...state.assets, items: [...state.assets.items, action.payload] }
      }
    case 'ASSETS_UPDATE_ITEM':
      return {
        ...state,
        assets: {
          ...state.assets,
          items: state.assets.items.map(item =>
            item.id === action.payload.id ? action.payload : item
          )
        }
      }
    case 'ASSETS_DELETE_ITEM':
      return {
        ...state,
        assets: {
          ...state.assets,
          items: state.assets.items.filter(item => item.id !== action.payload)
        }
      }
    case 'ASSETS_SET_SELECTED':
      return {
        ...state,
        assets: { ...state.assets, selectedAsset: action.payload }
      }
    case 'ASSETS_SET_FILTERS':
      return {
        ...state,
        assets: { ...state.assets, filters: action.payload }
      }
    case 'ASSETS_SET_SEARCH':
      return {
        ...state,
        assets: { ...state.assets, searchQuery: action.payload }
      }
    case 'ASSETS_SET_STATS':
      return {
        ...state,
        assets: { ...state.assets, stats: action.payload }
      }

    // Assignments
    case 'ASSIGNMENTS_LOADING':
      return {
        ...state,
        assignments: { ...state.assignments, loading: action.payload }
      }
    case 'ASSIGNMENTS_SET_ERROR':
      return {
        ...state,
        assignments: { ...state.assignments, error: action.payload, loading: false }
      }
    case 'ASSIGNMENTS_SET_ITEMS':
      return {
        ...state,
        assignments: { ...state.assignments, items: action.payload, loading: false, error: null }
      }
    case 'ASSIGNMENTS_ADD_ITEM':
      return {
        ...state,
        assignments: { ...state.assignments, items: [...state.assignments.items, action.payload] }
      }
    case 'ASSIGNMENTS_UPDATE_ITEM':
      return {
        ...state,
        assignments: {
          ...state.assignments,
          items: state.assignments.items.map(item =>
            item.id === action.payload.id ? action.payload : item
          )
        }
      }
    case 'ASSIGNMENTS_DELETE_ITEM':
      return {
        ...state,
        assignments: {
          ...state.assignments,
          items: state.assignments.items.filter(item => item.id !== action.payload)
        }
      }
    case 'ASSIGNMENTS_SET_SELECTED':
      return {
        ...state,
        assignments: { ...state.assignments, selectedAssignment: action.payload }
      }

    // Maintenance
    case 'MAINTENANCE_LOADING':
      return {
        ...state,
        maintenance: { ...state.maintenance, loading: action.payload }
      }
    case 'MAINTENANCE_SET_ERROR':
      return {
        ...state,
        maintenance: { ...state.maintenance, error: action.payload, loading: false }
      }
    case 'MAINTENANCE_SET_ITEMS':
      return {
        ...state,
        maintenance: { ...state.maintenance, items: action.payload, loading: false, error: null }
      }
    case 'MAINTENANCE_ADD_ITEM':
      return {
        ...state,
        maintenance: { ...state.maintenance, items: [...state.maintenance.items, action.payload] }
      }
    case 'MAINTENANCE_UPDATE_ITEM':
      return {
        ...state,
        maintenance: {
          ...state.maintenance,
          items: state.maintenance.items.map(item =>
            item.id === action.payload.id ? action.payload : item
          )
        }
      }
    case 'MAINTENANCE_DELETE_ITEM':
      return {
        ...state,
        maintenance: {
          ...state.maintenance,
          items: state.maintenance.items.filter(item => item.id !== action.payload)
        }
      }
    case 'MAINTENANCE_SET_SELECTED':
      return {
        ...state,
        maintenance: { ...state.maintenance, selectedRecord: action.payload }
      }

    // UI
    case 'UI_SET_ACTIVE_TAB':
      return {
        ...state,
        ui: { ...state.ui, activeTab: action.payload }
      }
    case 'UI_SET_SIDEBAR_OPEN':
      return {
        ...state,
        ui: { ...state.ui, sidebarOpen: action.payload }
      }
    case 'UI_SET_THEME':
      return {
        ...state,
        ui: { ...state.ui, theme: action.payload }
      }
    case 'UI_ADD_NOTIFICATION':
      return {
        ...state,
        ui: { ...state.ui, notifications: [...state.ui.notifications, action.payload] }
      }
    case 'UI_REMOVE_NOTIFICATION':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: state.ui.notifications.filter(n => n.id !== action.payload)
        }
      }
    case 'UI_MARK_NOTIFICATION_READ':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: state.ui.notifications.map(n =>
            n.id === action.payload ? { ...n, read: true } : n
          )
        }
      }
    case 'UI_SET_DIALOG':
      return {
        ...state,
        ui: {
          ...state.ui,
          dialogs: { ...state.ui.dialogs, [action.payload.dialog]: action.payload.open }
        }
      }

    default:
      return state
  }
}

// ============================================================================
// CONTEXT
// ============================================================================

interface AssetContextType {
  state: AppState
  dispatch: React.Dispatch<AssetAction>
  
  // Asset actions
  loadAssets: () => Promise<void>
  addAsset: (data: AssetFormData) => Promise<void>
  updateAsset: (id: string, data: Partial<AssetFormData>) => Promise<void>
  deleteAsset: (id: string) => Promise<void>
  setSelectedAsset: (asset: Asset | null) => void
  setAssetFilters: (filters: AssetFilter) => void
  setAssetSearch: (query: string) => void
  
  // Assignment actions
  loadAssignments: () => Promise<void>
  createAssignment: (data: AssignmentFormData) => Promise<void>
  updateAssignment: (id: string, data: Partial<AssignmentFormData>) => Promise<void>
  deleteAssignment: (id: string) => Promise<void>
  
  // Maintenance actions
  loadMaintenance: () => Promise<void>
  scheduleMaintenance: (data: MaintenanceFormData) => Promise<void>
  updateMaintenance: (id: string, data: Partial<MaintenanceFormData>) => Promise<void>
  deleteMaintenance: (id: string) => Promise<void>
  
  // UI actions
  setActiveTab: (tab: TabType) => void
  setSidebarOpen: (open: boolean) => void
  setTheme: (theme: 'light' | 'dark') => void
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  markNotificationRead: (id: string) => void
  setDialog: (dialog: keyof AppState['ui']['dialogs'], open: boolean) => void
}

const AssetContext = createContext<AssetContextType | undefined>(undefined)

// ============================================================================
// PROVIDER
// ============================================================================

export const AssetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(assetReducer, initialState)

  // Mock API delay
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  // Asset actions
  const loadAssets = useCallback(async () => {
    dispatch({ type: 'ASSETS_LOADING', payload: true })
    try {
      await delay(1000) // Simulate API call
      // Mock data would be loaded here
      dispatch({ type: 'ASSETS_SET_ITEMS', payload: [] })
    } catch (error) {
      dispatch({ type: 'ASSETS_SET_ERROR', payload: 'Failed to load assets' })
    }
  }, [])

  const addAsset = useCallback(async (data: AssetFormData) => {
    const newAsset: Asset = {
      id: `AST${Date.now()}`,
      ...data,
      status: 'available',
      currentValue: data.purchaseCost,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    dispatch({ type: 'ASSETS_ADD_ITEM', payload: newAsset })
  }, [])

  const updateAsset = useCallback(async (id: string, data: Partial<AssetFormData>) => {
    const existingAsset = state.assets.items.find(a => a.id === id)
    if (existingAsset) {
      const updatedAsset: Asset = {
        ...existingAsset,
        ...data,
        updatedAt: new Date().toISOString()
      }
      dispatch({ type: 'ASSETS_UPDATE_ITEM', payload: updatedAsset })
    }
  }, [state.assets.items])

  const deleteAsset = useCallback(async (id: string) => {
    dispatch({ type: 'ASSETS_DELETE_ITEM', payload: id })
  }, [])

  const setSelectedAsset = useCallback((asset: Asset | null) => {
    dispatch({ type: 'ASSETS_SET_SELECTED', payload: asset })
  }, [])

  const setAssetFilters = useCallback((filters: AssetFilter) => {
    dispatch({ type: 'ASSETS_SET_FILTERS', payload: filters })
  }, [])

  const setAssetSearch = useCallback((query: string) => {
    dispatch({ type: 'ASSETS_SET_SEARCH', payload: query })
  }, [])

  // Assignment actions
  const loadAssignments = useCallback(async () => {
    dispatch({ type: 'ASSIGNMENTS_LOADING', payload: true })
    try {
      await delay(1000)
      dispatch({ type: 'ASSIGNMENTS_SET_ITEMS', payload: [] })
    } catch (error) {
      dispatch({ type: 'ASSIGNMENTS_SET_ERROR', payload: 'Failed to load assignments' })
    }
  }, [])

  const createAssignment = useCallback(async (data: AssignmentFormData) => {
    const newAssignment: Assignment = {
      id: `ASG${Date.now()}`,
      ...data,
      assignmentDate: new Date().toISOString().split('T')[0],
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    dispatch({ type: 'ASSIGNMENTS_ADD_ITEM', payload: newAssignment })
  }, [])

  const updateAssignment = useCallback(async (id: string, data: Partial<AssignmentFormData>) => {
    const existingAssignment = state.assignments.items.find(a => a.id === id)
    if (existingAssignment) {
      const updatedAssignment: Assignment = {
        ...existingAssignment,
        ...data,
        updatedAt: new Date().toISOString()
      }
      dispatch({ type: 'ASSIGNMENTS_UPDATE_ITEM', payload: updatedAssignment })
    }
  }, [state.assignments.items])

  const deleteAssignment = useCallback(async (id: string) => {
    dispatch({ type: 'ASSIGNMENTS_DELETE_ITEM', payload: id })
  }, [])

  // Maintenance actions
  const loadMaintenance = useCallback(async () => {
    dispatch({ type: 'MAINTENANCE_LOADING', payload: true })
    try {
      await delay(1000)
      dispatch({ type: 'MAINTENANCE_SET_ITEMS', payload: [] })
    } catch (error) {
      dispatch({ type: 'MAINTENANCE_SET_ERROR', payload: 'Failed to load maintenance records' })
    }
  }, [])

  const scheduleMaintenance = useCallback(async (data: MaintenanceFormData) => {
    const newRecord: MaintenanceRecord = {
      id: `MNT${Date.now()}`,
      ...data,
      status: 'scheduled',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    dispatch({ type: 'MAINTENANCE_ADD_ITEM', payload: newRecord })
  }, [])

  const updateMaintenance = useCallback(async (id: string, data: Partial<MaintenanceFormData>) => {
    const existingRecord = state.maintenance.items.find(m => m.id === id)
    if (existingRecord) {
      const updatedRecord: MaintenanceRecord = {
        ...existingRecord,
        ...data,
        updatedAt: new Date().toISOString()
      }
      dispatch({ type: 'MAINTENANCE_UPDATE_ITEM', payload: updatedRecord })
    }
  }, [state.maintenance.items])

  const deleteMaintenance = useCallback(async (id: string) => {
    dispatch({ type: 'MAINTENANCE_DELETE_ITEM', payload: id })
  }, [])

  // UI actions
  const setActiveTab = useCallback((tab: TabType) => {
    dispatch({ type: 'UI_SET_ACTIVE_TAB', payload: tab })
  }, [])

  const setSidebarOpen = useCallback((open: boolean) => {
    dispatch({ type: 'UI_SET_SIDEBAR_OPEN', payload: open })
  }, [])

  const setTheme = useCallback((theme: 'light' | 'dark') => {
    dispatch({ type: 'UI_SET_THEME', payload: theme })
  }, [])

  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      timestamp: new Date().toISOString(),
      read: false
    }
    dispatch({ type: 'UI_ADD_NOTIFICATION', payload: newNotification })
  }, [])

  const removeNotification = useCallback((id: string) => {
    dispatch({ type: 'UI_REMOVE_NOTIFICATION', payload: id })
  }, [])

  const markNotificationRead = useCallback((id: string) => {
    dispatch({ type: 'UI_MARK_NOTIFICATION_READ', payload: id })
  }, [])

  const setDialog = useCallback((dialog: keyof AppState['ui']['dialogs'], open: boolean) => {
    dispatch({ type: 'UI_SET_DIALOG', payload: { dialog, open } })
  }, [])

  const value: AssetContextType = {
    state,
    dispatch,
    loadAssets,
    addAsset,
    updateAsset,
    deleteAsset,
    setSelectedAsset,
    setAssetFilters,
    setAssetSearch,
    loadAssignments,
    createAssignment,
    updateAssignment,
    deleteAssignment,
    loadMaintenance,
    scheduleMaintenance,
    updateMaintenance,
    deleteMaintenance,
    setActiveTab,
    setSidebarOpen,
    setTheme,
    addNotification,
    removeNotification,
    markNotificationRead,
    setDialog
  }

  return (
    <AssetContext.Provider value={value}>
      {children}
    </AssetContext.Provider>
  )
}

// ============================================================================
// HOOK
// ============================================================================

export const useAssetContext = () => {
  const context = useContext(AssetContext)
  if (context === undefined) {
    throw new Error('useAssetContext must be used within an AssetProvider')
  }
  return context
}

export default AssetContext
