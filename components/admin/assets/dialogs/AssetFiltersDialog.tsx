"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Filter, 
  X, 
  RotateCcw,
  Calendar,
  DollarSign,
  MapPin,
  Tag
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AssetFilter, AssetType, AssetStatus, AssetCondition } from "../types"

interface AssetFiltersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  filters: AssetFilter
  onFiltersChange: (filters: AssetFilter) => void
}

export function AssetFiltersDialog({ 
  open, 
  onOpenChange, 
  filters, 
  onFiltersChange 
}: AssetFiltersDialogProps) {
  const [localFilters, setLocalFilters] = useState<AssetFilter>(filters)

  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const assetTypes: { value: AssetType; label: string }[] = [
    { value: 'laptop', label: 'Laptop' },
    { value: 'desktop', label: 'Desktop' },
    { value: 'monitor', label: 'Monitor' },
    { value: 'printer', label: 'Printer' },
    { value: 'phone', label: 'Phone' },
    { value: 'tablet', label: 'Tablet' },
    { value: 'server', label: 'Server' },
    { value: 'network', label: 'Network Equipment' },
    { value: 'furniture', label: 'Furniture' },
    { value: 'vehicle', label: 'Vehicle' },
    { value: 'software', label: 'Software' },
    { value: 'license', label: 'License' },
    { value: 'other', label: 'Other' },
  ]

  const assetStatuses: { value: AssetStatus; label: string }[] = [
    { value: 'active', label: 'Active' },
    { value: 'available', label: 'Available' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'retired', label: 'Retired' },
    { value: 'lost', label: 'Lost' },
    { value: 'stolen', label: 'Stolen' },
    { value: 'disposed', label: 'Disposed' },
  ]

  const assetConditions: { value: AssetCondition; label: string }[] = [
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
    { value: 'damaged', label: 'Damaged' },
  ]

  const locations = [
    'NYC Office - Floor 1',
    'NYC Office - Floor 2', 
    'NYC Office - Floor 3',
    'Remote',
    'Warehouse',
    'Parking Garage Level 1',
  ]

  const handleTypeChange = (type: AssetType, checked: boolean) => {
    const currentTypes = localFilters.type || []
    if (checked) {
      setLocalFilters({
        ...localFilters,
        type: [...currentTypes, type]
      })
    } else {
      setLocalFilters({
        ...localFilters,
        type: currentTypes.filter(t => t !== type)
      })
    }
  }

  const handleStatusChange = (status: AssetStatus, checked: boolean) => {
    const currentStatuses = localFilters.status || []
    if (checked) {
      setLocalFilters({
        ...localFilters,
        status: [...currentStatuses, status]
      })
    } else {
      setLocalFilters({
        ...localFilters,
        status: currentStatuses.filter(s => s !== status)
      })
    }
  }

  const handleConditionChange = (condition: AssetCondition, checked: boolean) => {
    const currentConditions = localFilters.condition || []
    if (checked) {
      setLocalFilters({
        ...localFilters,
        condition: [...currentConditions, condition]
      })
    } else {
      setLocalFilters({
        ...localFilters,
        condition: currentConditions.filter(c => c !== condition)
      })
    }
  }

  const handleLocationChange = (location: string, checked: boolean) => {
    const currentLocations = localFilters.location || []
    if (checked) {
      setLocalFilters({
        ...localFilters,
        location: [...currentLocations, location]
      })
    } else {
      setLocalFilters({
        ...localFilters,
        location: currentLocations.filter(l => l !== location)
      })
    }
  }

  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    setLocalFilters({
      ...localFilters,
      dateRange: {
        ...localFilters.dateRange,
        [field]: value
      }
    })
  }

  const handleCostRangeChange = (field: 'min' | 'max', value: string) => {
    const numValue = parseFloat(value) || 0
    setLocalFilters({
      ...localFilters,
      costRange: {
        ...localFilters.costRange,
        [field]: numValue
      }
    })
  }

  const applyFilters = () => {
    onFiltersChange(localFilters)
    onOpenChange(false)
  }

  const clearFilters = () => {
    const emptyFilters: AssetFilter = {}
    setLocalFilters(emptyFilters)
    onFiltersChange(emptyFilters)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (localFilters.type?.length) count++
    if (localFilters.status?.length) count++
    if (localFilters.condition?.length) count++
    if (localFilters.location?.length) count++
    if (localFilters.dateRange?.start || localFilters.dateRange?.end) count++
    if (localFilters.costRange?.min || localFilters.costRange?.max) count++
    return count
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filter Assets
          </DialogTitle>
          <DialogDescription>
            Apply filters to narrow down your asset search results.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Asset Types */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Asset Types
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {assetTypes.map((type) => (
                <div key={type.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`type-${type.value}`}
                    checked={localFilters.type?.includes(type.value) || false}
                    onCheckedChange={(checked) => 
                      handleTypeChange(type.value, checked as boolean)
                    }
                  />
                  <Label 
                    htmlFor={`type-${type.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {type.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Asset Status */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Asset Status</Label>
            <div className="grid grid-cols-3 gap-2">
              {assetStatuses.map((status) => (
                <div key={status.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${status.value}`}
                    checked={localFilters.status?.includes(status.value) || false}
                    onCheckedChange={(checked) => 
                      handleStatusChange(status.value, checked as boolean)
                    }
                  />
                  <Label 
                    htmlFor={`status-${status.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {status.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Asset Condition */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Asset Condition</Label>
            <div className="grid grid-cols-3 gap-2">
              {assetConditions.map((condition) => (
                <div key={condition.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`condition-${condition.value}`}
                    checked={localFilters.condition?.includes(condition.value) || false}
                    onCheckedChange={(checked) => 
                      handleConditionChange(condition.value, checked as boolean)
                    }
                  />
                  <Label 
                    htmlFor={`condition-${condition.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {condition.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Locations */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Locations
            </Label>
            <div className="grid grid-cols-2 gap-2">
              {locations.map((location) => (
                <div key={location} className="flex items-center space-x-2">
                  <Checkbox
                    id={`location-${location}`}
                    checked={localFilters.location?.includes(location) || false}
                    onCheckedChange={(checked) => 
                      handleLocationChange(location, checked as boolean)
                    }
                  />
                  <Label 
                    htmlFor={`location-${location}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {location}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Date Range */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Purchase Date Range
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date-start" className="text-xs text-gray-500">From</Label>
                <Input
                  id="date-start"
                  type="date"
                  value={localFilters.dateRange?.start || ''}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="date-end" className="text-xs text-gray-500">To</Label>
                <Input
                  id="date-end"
                  type="date"
                  value={localFilters.dateRange?.end || ''}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Cost Range */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Purchase Cost Range
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cost-min" className="text-xs text-gray-500">Minimum ($)</Label>
                <Input
                  id="cost-min"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={localFilters.costRange?.min || ''}
                  onChange={(e) => handleCostRangeChange('min', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="cost-max" className="text-xs text-gray-500">Maximum ($)</Label>
                <Input
                  id="cost-max"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={localFilters.costRange?.max || ''}
                  onChange={(e) => handleCostRangeChange('max', e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-6 border-t">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              className="gap-2"
            >
              <RotateCcw className="w-4 h-4" />
              Clear All
            </Button>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} active
              </Badge>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button onClick={applyFilters}>
              Apply Filters
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
