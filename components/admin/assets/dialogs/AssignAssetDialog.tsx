"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { 
  UserPlus, 
  Search, 
  Calendar,
  FileText,
  Loader2
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { AssignmentFormData, Asset } from "../types"

const assignmentSchema = z.object({
  assetId: z.string().min(1, "Asset is required"),
  assignedTo: z.string().min(1, "Employee is required"),
  reason: z.string().min(1, "Assignment reason is required"),
  notes: z.string().optional(),
  expectedReturnDate: z.string().optional(),
})

interface AssignAssetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  asset?: Asset
  onSubmit: (data: AssignmentFormData) => Promise<void>
}

// Mock employee data
const mockEmployees = [
  { id: "john-smith", name: "John Smith", department: "Engineering", email: "<EMAIL>" },
  { id: "sarah-johnson", name: "Sarah Johnson", department: "Marketing", email: "<EMAIL>" },
  { id: "mike-wilson", name: "Mike Wilson", department: "Sales", email: "<EMAIL>" },
  { id: "emily-brown", name: "Emily Brown", department: "HR", email: "<EMAIL>" },
  { id: "david-lee", name: "David Lee", department: "Finance", email: "<EMAIL>" },
]

export function AssignAssetDialog({ 
  open, 
  onOpenChange, 
  asset, 
  onSubmit 
}: AssignAssetDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [employeeSearch, setEmployeeSearch] = useState("")

  const form = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentSchema),
    defaultValues: {
      assetId: asset?.id || "",
      assignedTo: "",
      reason: "",
      notes: "",
      expectedReturnDate: "",
    },
  })

  const handleSubmit = async (data: AssignmentFormData) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
      form.reset()
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to assign asset:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const filteredEmployees = mockEmployees.filter(employee =>
    employee.name.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.department.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.email.toLowerCase().includes(employeeSearch.toLowerCase())
  )

  const assignmentReasons = [
    "New employee onboarding",
    "Equipment replacement",
    "Project assignment",
    "Temporary assignment",
    "Department transfer",
    "Remote work setup",
    "Training purposes",
    "Other"
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Assign Asset
          </DialogTitle>
          <DialogDescription>
            Assign this asset to an employee. Fill in the assignment details below.
          </DialogDescription>
        </DialogHeader>

        {asset && (
          <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="text-2xl">💻</div>
              <div>
                <h4 className="font-medium">{asset.name}</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {asset.id} • {asset.type} • {asset.location}
                </p>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {asset.condition}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {asset.status}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Employee Selection */}
            <div className="space-y-3">
              <Label>Select Employee</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search employees..."
                  value={employeeSearch}
                  onChange={(e) => setEmployeeSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <FormField
                control={form.control}
                name="assignedTo"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="max-h-40 overflow-y-auto border rounded-md">
                        {filteredEmployees.map((employee) => (
                          <div
                            key={employee.id}
                            className={`p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 border-b last:border-b-0 ${
                              field.value === employee.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                            }`}
                            onClick={() => field.onChange(employee.id)}
                          >
                            <div className="flex items-center gap-3">
                              <Avatar className="w-8 h-8">
                                <AvatarFallback className="text-xs">
                                  {employee.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <div className="font-medium text-sm">{employee.name}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {employee.department} • {employee.email}
                                </div>
                              </div>
                              {field.value === employee.id && (
                                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              )}
                            </div>
                          </div>
                        ))}
                        {filteredEmployees.length === 0 && (
                          <div className="p-3 text-center text-gray-500 dark:text-gray-400">
                            No employees found
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Assignment Reason */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assignment Reason *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select reason" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {assignmentReasons.map((reason) => (
                        <SelectItem key={reason} value={reason}>
                          {reason}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Expected Return Date */}
            <FormField
              control={form.control}
              name="expectedReturnDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expected Return Date (Optional)</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about this assignment..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Footer */}
            <div className="flex items-center justify-between pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Assign Asset
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
