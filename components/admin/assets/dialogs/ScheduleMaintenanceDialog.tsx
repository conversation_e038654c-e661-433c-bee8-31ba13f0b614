"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { 
  Wrench, 
  Calendar, 
  DollarSign,
  AlertTriangle,
  User,
  Loader2
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { MaintenanceFormData, Asset, MaintenanceType, Priority } from "../types"

const maintenanceSchema = z.object({
  assetId: z.string().min(1, "Asset is required"),
  type: z.enum(['preventive', 'corrective', 'emergency', 'upgrade', 'inspection']),
  description: z.string().min(1, "Description is required"),
  scheduledDate: z.string().min(1, "Scheduled date is required"),
  technician: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high', 'critical']),
  estimatedCost: z.number().min(0, "Cost must be positive").optional(),
  notes: z.string().optional(),
})

interface ScheduleMaintenanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  asset?: Asset
  onSubmit: (data: MaintenanceFormData) => Promise<void>
}

// Mock technician data
const mockTechnicians = [
  { id: "tech-1", name: "IT Support Team", specialization: "IT Equipment" },
  { id: "tech-2", name: "Facilities Team", specialization: "Furniture & Office" },
  { id: "tech-3", name: "Auto Service Center", specialization: "Vehicles" },
  { id: "tech-4", name: "External Contractor", specialization: "Specialized Equipment" },
]

export function ScheduleMaintenanceDialog({ 
  open, 
  onOpenChange, 
  asset, 
  onSubmit 
}: ScheduleMaintenanceDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<MaintenanceFormData>({
    resolver: zodResolver(maintenanceSchema),
    defaultValues: {
      assetId: asset?.id || "",
      type: "preventive",
      description: "",
      scheduledDate: "",
      technician: "",
      priority: "normal",
      estimatedCost: 0,
      notes: "",
    },
  })

  const handleSubmit = async (data: MaintenanceFormData) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
      form.reset()
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to schedule maintenance:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const maintenanceTypes: { value: MaintenanceType; label: string; description: string }[] = [
    { 
      value: 'preventive', 
      label: 'Preventive', 
      description: 'Regular scheduled maintenance to prevent issues' 
    },
    { 
      value: 'corrective', 
      label: 'Corrective', 
      description: 'Fix known issues or problems' 
    },
    { 
      value: 'emergency', 
      label: 'Emergency', 
      description: 'Urgent repair needed immediately' 
    },
    { 
      value: 'upgrade', 
      label: 'Upgrade', 
      description: 'Improve or enhance asset functionality' 
    },
    { 
      value: 'inspection', 
      label: 'Inspection', 
      description: 'Routine inspection and assessment' 
    },
  ]

  const priorities: { value: Priority; label: string; color: string }[] = [
    { value: 'low', label: 'Low', color: 'text-gray-600' },
    { value: 'normal', label: 'Normal', color: 'text-blue-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'critical', label: 'Critical', color: 'text-red-600' },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="w-5 h-5" />
            Schedule Maintenance
          </DialogTitle>
          <DialogDescription>
            Schedule maintenance for this asset. Fill in the details below.
          </DialogDescription>
        </DialogHeader>

        {asset && (
          <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="text-2xl">💻</div>
              <div>
                <h4 className="font-medium">{asset.name}</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {asset.id} • {asset.type} • {asset.location}
                </p>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {asset.condition}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {asset.status}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Maintenance Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Maintenance Type *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select maintenance type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {maintenanceTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-xs text-gray-500">{type.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the maintenance work to be performed..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Scheduled Date */}
              <FormField
                control={form.control}
                name="scheduledDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Scheduled Date *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            <div className={`flex items-center gap-2 ${priority.color}`}>
                              <AlertTriangle className="w-4 h-4" />
                              {priority.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              {/* Technician */}
              <FormField
                control={form.control}
                name="technician"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Technician</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select technician" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockTechnicians.map((tech) => (
                          <SelectItem key={tech.id} value={tech.name}>
                            <div>
                              <div className="font-medium">{tech.name}</div>
                              <div className="text-xs text-gray-500">{tech.specialization}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Estimated Cost */}
              <FormField
                control={form.control}
                name="estimatedCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Cost ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Additional Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes or special instructions..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Maintenance Guidelines */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Maintenance Guidelines
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Ensure asset is available on the scheduled date</li>
                <li>• Notify assigned user if asset needs to be temporarily unavailable</li>
                <li>• Document all work performed and parts replaced</li>
                <li>• Update asset condition after maintenance completion</li>
              </ul>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Schedule Maintenance
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
