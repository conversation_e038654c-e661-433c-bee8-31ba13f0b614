/**
 * useAssetManagement Hook
 * Custom hook providing convenient access to asset management functionality
 */

import { useMemo } from 'react'
import { useAssetContext } from '../context/AssetContext'
import { Asset, Assignment, MaintenanceRecord, AssetFilter, DashboardStats } from '../types/index'

// ============================================================================
// HOOK
// ============================================================================

/**
 * Custom hook for asset management operations
 */
export const useAssetManagement = () => {
  const {
    state,
    loadAssets,
    addAsset,
    updateAsset,
    deleteAsset,
    setSelectedAsset,
    setAssetFilters,
    setAssetSearch,
    loadAssignments,
    createAssignment,
    updateAssignment,
    deleteAssignment,
    loadMaintenance,
    scheduleMaintenance,
    updateMaintenance,
    deleteMaintenance,
    setActiveTab,
    setSidebarOpen,
    setTheme,
    addNotification,
    removeNotification,
    markNotificationRead,
    setDialog
  } = useAssetContext()

  // Computed values
  const computedStats = useMemo((): DashboardStats => {
    const assets = state.assets.items
    const assignments = state.assignments.items
    const maintenance = state.maintenance.items

    const totalAssets = assets.length
    const activeAssignments = assignments.filter((a: Assignment) => a.status === 'active').length
    const pendingMaintenance = maintenance.filter((m: MaintenanceRecord) => m.status === 'scheduled' || m.status === 'in-progress').length
    const totalValue = assets.reduce((sum: number, asset: Asset) => sum + asset.currentValue, 0)
    const utilizationRate = totalAssets > 0 ? (activeAssignments / totalAssets) * 100 : 0
    const overdueItems = maintenance.filter((m: MaintenanceRecord) => m.status === 'overdue').length
    const monthlyGrowth = 12 // Mock value
    const maintenanceCosts = maintenance.reduce((sum: number, m: MaintenanceRecord) => sum + (m.actualCost || m.estimatedCost || 0), 0)

    return {
      totalAssets,
      activeAssignments,
      pendingMaintenance,
      totalValue,
      utilizationRate,
      overdueItems,
      monthlyGrowth,
      maintenanceCosts
    }
  }, [state.assets.items, state.assignments.items, state.maintenance.items])

  // Filtered assets based on search and filters
  const filteredAssets = useMemo(() => {
    let filtered = [...state.assets.items]
    const { searchQuery, filters } = state.assets

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.id.toLowerCase().includes(query) ||
        asset.serialNumber.toLowerCase().includes(query) ||
        asset.assignedToName?.toLowerCase().includes(query) ||
        asset.location.toLowerCase().includes(query)
      )
    }

    // Apply filters
    if (filters.types?.length) {
      filtered = filtered.filter(asset => filters.types!.includes(asset.type))
    }
    if (filters.statuses?.length) {
      filtered = filtered.filter(asset => filters.statuses!.includes(asset.status))
    }
    if (filters.conditions?.length) {
      filtered = filtered.filter(asset => filters.conditions!.includes(asset.condition))
    }
    if (filters.locations?.length) {
      filtered = filtered.filter(asset => filters.locations!.includes(asset.location))
    }
    if (filters.assignees?.length) {
      filtered = filtered.filter(asset => 
        asset.assignedToName && filters.assignees!.includes(asset.assignedToName)
      )
    }
    if (filters.vendors?.length) {
      filtered = filtered.filter(asset => filters.vendors!.includes(asset.vendor))
    }
    if (filters.categories?.length) {
      filtered = filtered.filter(asset => 
        asset.category && filters.categories!.includes(asset.category)
      )
    }
    if (filters.dateRange) {
      const { start, end } = filters.dateRange
      filtered = filtered.filter(asset => {
        const purchaseDate = new Date(asset.purchaseDate)
        return purchaseDate >= new Date(start) && purchaseDate <= new Date(end)
      })
    }
    if (filters.valueRange) {
      const { min, max } = filters.valueRange
      filtered = filtered.filter(asset => 
        asset.currentValue >= min && asset.currentValue <= max
      )
    }

    return filtered
  }, [state.assets.items, state.assets.searchQuery, state.assets.filters])

  // Available assets (for assignments)
  const availableAssets = useMemo(() => {
    return state.assets.items.filter((asset: Asset) => asset.status === 'available')
  }, [state.assets.items])

  // Overdue assignments
  const overdueAssignments = useMemo(() => {
    return state.assignments.items.filter((assignment: Assignment) => {
      if (assignment.status !== 'active' || !assignment.returnDate) return false
      return new Date(assignment.returnDate) < new Date()
    })
  }, [state.assignments.items])

  // Overdue maintenance
  const overdueMaintenance = useMemo(() => {
    return state.maintenance.items.filter((record: MaintenanceRecord) => record.status === 'overdue')
  }, [state.maintenance.items])

  // Unread notifications
  const unreadNotifications = useMemo(() => {
    return state.ui.notifications.filter((n: any) => !n.read)
  }, [state.ui.notifications])

  // Asset by ID lookup
  const getAssetById = useMemo(() => {
    const assetMap = new Map(state.assets.items.map((asset: Asset) => [asset.id, asset]))
    return (id: string): Asset | undefined => assetMap.get(id) as Asset | undefined
  }, [state.assets.items])

  // Assignment by ID lookup
  const getAssignmentById = useMemo(() => {
    const assignmentMap = new Map(state.assignments.items.map((assignment: Assignment) => [assignment.id, assignment]))
    return (id: string): Assignment | undefined => assignmentMap.get(id) as Assignment | undefined
  }, [state.assignments.items])

  // Maintenance by ID lookup
  const getMaintenanceById = useMemo(() => {
    const maintenanceMap = new Map(state.maintenance.items.map((record: MaintenanceRecord) => [record.id, record]))
    return (id: string): MaintenanceRecord | undefined => maintenanceMap.get(id) as MaintenanceRecord | undefined
  }, [state.maintenance.items])

  // Asset utilization by type
  const assetUtilizationByType = useMemo(() => {
    const utilization: Record<string, { total: number; assigned: number; rate: number }> = {}
    
    state.assets.items.forEach((asset: Asset) => {
      if (!utilization[asset.type]) {
        utilization[asset.type] = { total: 0, assigned: 0, rate: 0 }
      }
      utilization[asset.type].total++
      if (asset.status === 'assigned') {
        utilization[asset.type].assigned++
      }
    })

    Object.keys(utilization).forEach(type => {
      const data = utilization[type]
      data.rate = data.total > 0 ? (data.assigned / data.total) * 100 : 0
    })

    return utilization
  }, [state.assets.items])

  // Maintenance costs by month
  const maintenanceCostsByMonth = useMemo(() => {
    const costs: Record<string, number> = {}
    
    state.maintenance.items.forEach((record: MaintenanceRecord) => {
      if (record.completedDate || record.actualCost) {
        const date = new Date(record.completedDate || record.scheduledDate)
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        costs[monthKey] = (costs[monthKey] || 0) + (record.actualCost || record.estimatedCost || 0)
      }
    })

    return costs
  }, [state.maintenance.items])

  // Helper functions
  const refreshData = async () => {
    await Promise.all([
      loadAssets(),
      loadAssignments(),
      loadMaintenance()
    ])
  }

  const clearFilters = () => {
    setAssetFilters({})
    setAssetSearch('')
  }

  const openDialog = (dialog: keyof typeof state.ui.dialogs) => {
    setDialog(dialog, true)
  }

  const closeDialog = (dialog: keyof typeof state.ui.dialogs) => {
    setDialog(dialog, false)
  }

  const closeAllDialogs = () => {
    Object.keys(state.ui.dialogs).forEach(dialog => {
      setDialog(dialog as keyof typeof state.ui.dialogs, false)
    })
  }

  return {
    // State
    state,
    
    // Computed values
    stats: computedStats,
    filteredAssets,
    availableAssets,
    overdueAssignments,
    overdueMaintenance,
    unreadNotifications,
    assetUtilizationByType,
    maintenanceCostsByMonth,
    
    // Lookup functions
    getAssetById,
    getAssignmentById,
    getMaintenanceById,
    
    // Asset actions
    loadAssets,
    addAsset,
    updateAsset,
    deleteAsset,
    setSelectedAsset,
    setAssetFilters,
    setAssetSearch,
    
    // Assignment actions
    loadAssignments,
    createAssignment,
    updateAssignment,
    deleteAssignment,
    
    // Maintenance actions
    loadMaintenance,
    scheduleMaintenance,
    updateMaintenance,
    deleteMaintenance,
    
    // UI actions
    setActiveTab,
    setSidebarOpen,
    setTheme,
    addNotification,
    removeNotification,
    markNotificationRead,
    setDialog,
    
    // Helper functions
    refreshData,
    clearFilters,
    openDialog,
    closeDialog,
    closeAllDialogs
  }
}

export default useAssetManagement
