"use client"

import { useState, useEffect, useCallback } from "react"
import { Asset, AssetFilter, AssetStats } from "../types"

// Mock data for demonstration
const mockAssets: Asset[] = [
  {
    id: "ASTD3458",
    name: "Dell Laptop XPS 13",
    type: "laptop",
    serialNumber: "DL123456789",
    model: "XPS 13",
    manufacturer: "Dell",
    purchaseDate: "2024-01-15",
    purchaseCost: 1299.99,
    currentValue: 1100.00,
    condition: "good",
    status: "active",
    location: "NYC Office - Floor 3",
    assignedTo: "john-smith",
    assignedToName: "<PERSON>",
    assignedDate: "2024-01-16",
    warrantyExpiry: "2027-01-15",
    description: "High-performance laptop for development work",
    vendor: "Dell Technologies",
    category: "IT Equipment",
    subcategory: "Laptops",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-16T14:30:00Z",
    createdBy: "admin",
    updatedBy: "admin"
  },
  {
    id: "ASTD3457",
    name: "<PERSON>",
    type: "furniture",
    serialNumber: "HM987654321",
    model: "Aeron",
    manufacturer: "<PERSON>",
    purchaseDate: "2023-03-25",
    purchaseCost: 899.99,
    currentValue: 750.00,
    condition: "excellent",
    status: "active",
    location: "NYC Office - Floor 2",
    assignedTo: "sarah-johnson",
    assignedToName: "Sarah Johnson",
    assignedDate: "2023-03-26",
    description: "Ergonomic office chair",
    vendor: "Herman Miller",
    category: "Furniture",
    subcategory: "Chairs",
    createdAt: "2023-03-25T09:00:00Z",
    updatedAt: "2023-03-26T11:15:00Z",
    createdBy: "admin",
    updatedBy: "admin"
  },
  {
    id: "ASTD3458",
    name: "Toyota Camry 2023",
    type: "vehicle",
    serialNumber: "TC2023001",
    model: "Camry",
    manufacturer: "Toyota",
    purchaseDate: "2023-03-25",
    purchaseCost: 28999.99,
    currentValue: 26500.00,
    condition: "excellent",
    status: "active",
    location: "Parking Garage Level 1",
    assignedTo: "sales-dept",
    assignedToName: "Sales Department",
    assignedDate: "2023-03-26",
    warrantyExpiry: "2026-03-25",
    description: "Company vehicle for sales team",
    vendor: "Toyota Dealership",
    category: "Vehicles",
    subcategory: "Sedans",
    createdAt: "2023-03-25T08:00:00Z",
    updatedAt: "2023-03-26T10:00:00Z",
    createdBy: "admin",
    updatedBy: "admin"
  }
]

const mockStats: AssetStats = {
  totalAssets: 156,
  totalValue: 2450000,
  activeAssets: 142,
  availableAssets: 14,
  assignedAssets: 128,
  maintenanceRequired: 8,
  warrantyExpiring: 12,
  depreciation: 125000,
  newAssetsThisMonth: 15,
  assetsByType: {
    laptop: 45,
    desktop: 23,
    monitor: 67,
    printer: 8,
    phone: 34,
    tablet: 12,
    server: 6,
    network: 15,
    furniture: 89,
    vehicle: 4,
    software: 23,
    license: 45,
    other: 12
  },
  assetsByStatus: {
    active: 142,
    inactive: 8,
    maintenance: 4,
    retired: 2,
    lost: 0,
    stolen: 0,
    disposed: 0,
    available: 14
  },
  assetsByCondition: {
    excellent: 67,
    good: 78,
    fair: 9,
    poor: 2,
    damaged: 0
  },
  topCategories: [
    { name: "IT Equipment", count: 89, value: 890000 },
    { name: "Furniture", count: 45, value: 450000 },
    { name: "Vehicles", count: 4, value: 120000 },
    { name: "Software", count: 18, value: 85000 }
  ],
  maintenanceOverdue: 3,
  averageAssetAge: 2.3
}

export function useAssets(filters: AssetFilter = {}, searchQuery: string = "") {
  const [assets, setAssets] = useState<Asset[]>([])
  const [stats, setStats] = useState<AssetStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const filterAssets = useCallback((allAssets: Asset[], filters: AssetFilter, search: string) => {
    let filtered = [...allAssets]

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filtered = filtered.filter(asset => 
        asset.name.toLowerCase().includes(searchLower) ||
        asset.serialNumber?.toLowerCase().includes(searchLower) ||
        asset.assignedToName?.toLowerCase().includes(searchLower) ||
        asset.location.toLowerCase().includes(searchLower)
      )
    }

    // Apply type filter
    if (filters.type && filters.type.length > 0) {
      filtered = filtered.filter(asset => filters.type!.includes(asset.type))
    }

    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(asset => filters.status!.includes(asset.status))
    }

    // Apply condition filter
    if (filters.condition && filters.condition.length > 0) {
      filtered = filtered.filter(asset => filters.condition!.includes(asset.condition))
    }

    // Apply location filter
    if (filters.location && filters.location.length > 0) {
      filtered = filtered.filter(asset => filters.location!.includes(asset.location))
    }

    // Apply date range filter
    if (filters.dateRange) {
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end)
      filtered = filtered.filter(asset => {
        const purchaseDate = new Date(asset.purchaseDate)
        return purchaseDate >= startDate && purchaseDate <= endDate
      })
    }

    // Apply cost range filter
    if (filters.costRange) {
      filtered = filtered.filter(asset => 
        asset.purchaseCost >= filters.costRange!.min && 
        asset.purchaseCost <= filters.costRange!.max
      )
    }

    return filtered
  }, [])

  const loadAssets = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const filteredAssets = filterAssets(mockAssets, filters, searchQuery)
      setAssets(filteredAssets)
      setStats(mockStats)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assets')
    } finally {
      setLoading(false)
    }
  }, [filters, searchQuery, filterAssets])

  const refreshAssets = useCallback(async () => {
    await loadAssets()
  }, [loadAssets])

  const addAsset = useCallback(async (assetData: Partial<Asset>) => {
    try {
      setLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newAsset: Asset = {
        id: `ASTD${Date.now()}`,
        name: assetData.name || '',
        type: assetData.type || 'other',
        serialNumber: assetData.serialNumber,
        model: assetData.model,
        manufacturer: assetData.manufacturer,
        purchaseDate: assetData.purchaseDate || new Date().toISOString().split('T')[0],
        purchaseCost: assetData.purchaseCost || 0,
        currentValue: assetData.currentValue || assetData.purchaseCost || 0,
        condition: assetData.condition || 'good',
        status: assetData.status || 'available',
        location: assetData.location || '',
        assignedTo: assetData.assignedTo,
        assignedToName: assetData.assignedToName,
        assignedDate: assetData.assignedDate,
        warrantyExpiry: assetData.warrantyExpiry,
        description: assetData.description,
        vendor: assetData.vendor,
        category: assetData.category,
        subcategory: assetData.subcategory,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'current-user',
        updatedBy: 'current-user'
      }
      
      mockAssets.push(newAsset)
      await loadAssets()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add asset')
    } finally {
      setLoading(false)
    }
  }, [loadAssets])

  const updateAsset = useCallback(async (asset: Asset) => {
    try {
      setLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = mockAssets.findIndex(a => a.id === asset.id)
      if (index !== -1) {
        mockAssets[index] = { ...asset, updatedAt: new Date().toISOString() }
        await loadAssets()
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update asset')
    } finally {
      setLoading(false)
    }
  }, [loadAssets])

  const deleteAsset = useCallback(async (assetId: string) => {
    try {
      setLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = mockAssets.findIndex(a => a.id === assetId)
      if (index !== -1) {
        mockAssets.splice(index, 1)
        await loadAssets()
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete asset')
    } finally {
      setLoading(false)
    }
  }, [loadAssets])

  useEffect(() => {
    loadAssets()
  }, [loadAssets])

  return {
    assets,
    stats,
    loading,
    error,
    refreshAssets,
    addAsset,
    updateAsset,
    deleteAsset
  }
}
