export interface Asset {
  id: string
  name: string
  type: AssetType
  serialNumber?: string
  model?: string
  manufacturer?: string
  purchaseDate: string
  purchaseCost: number
  currentValue?: number
  condition: AssetCondition
  status: AssetStatus
  location: string
  assignedTo?: string
  assignedToName?: string
  assignedDate?: string
  warrantyExpiry?: string
  description?: string
  tags?: string[]
  lastMaintenanceDate?: string
  nextMaintenanceDate?: string
  vendor?: string
  invoiceNumber?: string
  depreciationRate?: number
  category?: string
  subcategory?: string
  barcode?: string
  qrCode?: string
  images?: string[]
  documents?: AssetDocument[]
  maintenanceHistory?: MaintenanceRecord[]
  assignmentHistory?: AssignmentRecord[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface AssetDocument {
  id: string
  name: string
  type: string
  url: string
  uploadedAt: string
  uploadedBy: string
  size: number
}

export interface MaintenanceRecord {
  id: string
  assetId: string
  type: MaintenanceType
  description: string
  scheduledDate: string
  completedDate?: string
  technician?: string
  cost?: number
  status: MaintenanceStatus
  priority: Priority
  notes?: string
  attachments?: string[]
  createdAt: string
  createdBy: string
}

export interface AssignmentRecord {
  id: string
  assetId: string
  assignedTo: string
  assignedToName: string
  assignedBy: string
  assignedByName: string
  assignedDate: string
  returnedDate?: string
  returnedBy?: string
  returnedByName?: string
  reason: string
  condition: AssetCondition
  notes?: string
  status: AssignmentStatus
}

export interface AssetFilter {
  search?: string
  type?: AssetType[]
  status?: AssetStatus[]
  condition?: AssetCondition[]
  location?: string[]
  assignedTo?: string[]
  category?: string[]
  dateRange?: {
    start: string
    end: string
  }
  costRange?: {
    min: number
    max: number
  }
  warrantyStatus?: WarrantyStatus[]
  maintenanceStatus?: MaintenanceStatus[]
}

export interface AssetStats {
  totalAssets: number
  totalValue: number
  activeAssets: number
  availableAssets: number
  assignedAssets: number
  maintenanceRequired: number
  warrantyExpiring: number
  depreciation: number
  newAssetsThisMonth: number
  assetsByType: Record<AssetType, number>
  assetsByStatus: Record<AssetStatus, number>
  assetsByCondition: Record<AssetCondition, number>
  topCategories: Array<{ name: string; count: number; value: number }>
  maintenanceOverdue: number
  averageAssetAge: number
}

export type AssetType = 
  | 'laptop' 
  | 'desktop' 
  | 'monitor' 
  | 'printer' 
  | 'phone' 
  | 'tablet' 
  | 'server' 
  | 'network' 
  | 'furniture' 
  | 'vehicle' 
  | 'software' 
  | 'license' 
  | 'other'

export type AssetStatus = 
  | 'active' 
  | 'inactive' 
  | 'maintenance' 
  | 'retired' 
  | 'lost' 
  | 'stolen' 
  | 'disposed' 
  | 'available'

export type AssetCondition = 
  | 'excellent' 
  | 'good' 
  | 'fair' 
  | 'poor' 
  | 'damaged'

export type MaintenanceType = 
  | 'preventive' 
  | 'corrective' 
  | 'emergency' 
  | 'upgrade' 
  | 'inspection'

export type MaintenanceStatus = 
  | 'scheduled' 
  | 'in-progress' 
  | 'completed' 
  | 'cancelled' 
  | 'overdue'

export type Priority = 
  | 'low' 
  | 'normal' 
  | 'high' 
  | 'critical'

export type AssignmentStatus = 
  | 'active' 
  | 'returned' 
  | 'overdue' 
  | 'lost'

export type WarrantyStatus = 
  | 'active' 
  | 'expired' 
  | 'expiring-soon' 
  | 'not-applicable'

export interface AssetFormData {
  name: string
  type: AssetType
  serialNumber?: string
  model?: string
  manufacturer?: string
  purchaseDate: string
  purchaseCost: number
  condition: AssetCondition
  location: string
  warrantyExpiry?: string
  description?: string
  vendor?: string
  invoiceNumber?: string
  category?: string
  subcategory?: string
  tags?: string[]
}

export interface MaintenanceFormData {
  assetId: string
  type: MaintenanceType
  description: string
  scheduledDate: string
  technician?: string
  priority: Priority
  estimatedCost?: number
  notes?: string
}

export interface AssignmentFormData {
  assetId: string
  assignedTo: string
  reason: string
  notes?: string
  expectedReturnDate?: string
}


export interface BaseComponentProps {
  className?: string
  'data-testid'?: string
}

// Define RepairRequestFormData type locally
export interface RepairRequestFormData {
  assetId: string
  requestedBy: string
  priority: PriorityLevel
  estimatedCost: number
  problemDescription: string
  attachments: File[]
  urgency: UrgencyLevel
}

// Define UrgencyLevel type locally if not exported from '../../types'
export type UrgencyLevel = 'normal' | 'urgent' | 'critical'

// Define PriorityLevel type locally if not exported from '../../types'
export type PriorityLevel = 'low' | 'normal' | 'high' | 'critical'

// Re-export types from the main types file to avoid duplicates
export type { TabItem, TabType, DashboardStats } from './types/index'