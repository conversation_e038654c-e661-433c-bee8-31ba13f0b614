/**
 * Core Asset Management Types and Interfaces
 * Comprehensive TypeScript definitions for the Asset Management System
 */

import { ReactNode } from 'react'

// ============================================================================
// CORE ENTITY TYPES
// ============================================================================

/**
 * Asset entity representing a physical or digital asset
 */
export interface Asset {
  /** Unique asset identifier */
  id: string
  /** Human-readable asset name */
  name: string
  /** Asset type/category */
  type: AssetType
  /** Current status of the asset */
  status: AssetStatus
  /** Physical condition of the asset */
  condition: AssetCondition
  /** Current location of the asset */
  location: string
  /** Name of person/department assigned to */
  assignedToName?: string
  /** ID of person/department assigned to */
  assignedToId?: string
  /** Original purchase cost */
  purchaseCost: number
  /** Current depreciated value */
  currentValue: number
  /** Date of purchase */
  purchaseDate: string
  /** Warranty expiration date */
  warrantyExpiry?: string
  /** Vendor/supplier name */
  vendor: string
  /** Serial number */
  serialNumber: string
  /** Asset model */
  model?: string
  /** Asset category */
  category: string
  /** Additional metadata */
  metadata?: Record<string, any>
  /** Creation timestamp */
  createdAt: string
  /** Last update timestamp */
  updatedAt: string
}

/**
 * Asset assignment record
 */
export interface Assignment {
  /** Unique assignment identifier */
  id: string
  /** Asset being assigned */
  assetId: string
  /** Asset details (populated) */
  asset?: Asset
  /** Person/department receiving assignment */
  assigneeId: string
  /** Assignee name */
  assigneeName: string
  /** Assignment type */
  type: AssignmentType
  /** Assignment purpose/reason */
  purpose?: string
  /** Assignment date */
  assignmentDate: string
  /** Expected return date */
  returnDate?: string
  /** Actual return date */
  actualReturnDate?: string
  /** Assignment status */
  status: AssignmentStatus
  /** Assignment notes */
  notes?: string
  /** Creation timestamp */
  createdAt: string
  /** Last update timestamp */
  updatedAt: string
}

/**
 * Maintenance record
 */
export interface MaintenanceRecord {
  /** Unique maintenance identifier */
  id: string
  /** Asset being maintained */
  assetId: string
  /** Asset details (populated) */
  asset?: Asset
  /** Maintenance type */
  type: MaintenanceType
  /** Maintenance frequency */
  frequency?: MaintenanceFrequency
  /** Scheduled date */
  scheduledDate: string
  /** Actual completion date */
  completedDate?: string
  /** Assigned technician */
  technicianId?: string
  /** Technician name */
  technicianName?: string
  /** Maintenance status */
  status: MaintenanceStatus
  /** Priority level */
  priority: PriorityLevel
  /** Estimated cost */
  estimatedCost?: number
  /** Actual cost */
  actualCost?: number
  /** Estimated downtime in hours */
  estimatedDowntime?: number
  /** Actual downtime in hours */
  actualDowntime?: number
  /** Maintenance description */
  description?: string
  /** Maintenance notes */
  notes?: string
  /** Warranty status */
  warrantyStatus: WarrantyStatus
  /** Requires temporary replacement */
  requiresReplacement?: boolean
  /** Attachments */
  attachments?: FileAttachment[]
  /** Creation timestamp */
  createdAt: string
  /** Last update timestamp */
  updatedAt: string
}

/**
 * File attachment
 */
export interface FileAttachment {
  /** File identifier */
  id: string
  /** Original filename */
  filename: string
  /** File size in bytes */
  size: number
  /** MIME type */
  mimeType: string
  /** File URL */
  url: string
  /** Upload timestamp */
  uploadedAt: string
}

// ============================================================================
// ENUM TYPES
// ============================================================================

export type AssetType = 
  | 'laptop' 
  | 'desktop' 
  | 'mobile-device' 
  | 'tablet'
  | 'furniture' 
  | 'vehicle' 
  | 'equipment' 
  | 'software'
  | 'other'

export type AssetStatus = 
  | 'available' 
  | 'assigned' 
  | 'maintenance' 
  | 'retired' 
  | 'lost' 
  | 'stolen'

export type AssetCondition = 
  | 'excellent' 
  | 'good' 
  | 'fair' 
  | 'poor' 
  | 'damaged'

export type AssignmentType = 
  | 'employee' 
  | 'department' 
  | 'project' 
  | 'temporary'

export type AssignmentStatus = 
  | 'active' 
  | 'returned' 
  | 'overdue' 
  | 'pending'

export type MaintenanceType = 
  | 'preventive' 
  | 'corrective' 
  | 'emergency' 
  | 'upgrade'

export type MaintenanceFrequency = 
  | 'one-time' 
  | 'weekly' 
  | 'monthly' 
  | 'quarterly' 
  | 'annually'

export type MaintenanceStatus = 
  | 'scheduled' 
  | 'in-progress' 
  | 'completed' 
  | 'cancelled' 
  | 'overdue'

export type PriorityLevel = 
  | 'low' 
  | 'normal' 
  | 'high' 
  | 'critical'

export type WarrantyStatus = 
  | 'covered' 
  | 'not-covered' 
  | 'expired' 
  | 'unknown'

export type UrgencyLevel = 
  | 'normal' 
  | 'urgent' 
  | 'critical'

// ============================================================================
// FORM DATA TYPES
// ============================================================================

/**
 * Form data for creating/updating assets
 */
export interface AssetFormData {
  name: string
  type: AssetType
  purchaseCost: number
  vendor: string
  serialNumber: string
  location: string
  model?: string
  category?: string
  warrantyExpiry?: string
  condition?: AssetCondition
  notes?: string
}

/**
 * Form data for creating assignments
 */
export interface AssignmentFormData {
  assetId: string
  assigneeId: string
  assigneeName: string
  type: AssignmentType
  purpose?: string
  returnDate?: string
  notes?: string
}

/**
 * Form data for scheduling maintenance
 */
export interface MaintenanceFormData {
  assetId: string
  type: MaintenanceType
  frequency?: MaintenanceFrequency
  scheduledDate: string
  technicianId?: string
  technicianName?: string
  priority: PriorityLevel
  estimatedCost?: number
  estimatedDowntime?: number
  description?: string
  notes?: string
  warrantyStatus: WarrantyStatus
  requiresReplacement?: boolean
}

/**
 * Form data for repair requests
 */
export interface RepairRequestFormData {
  assetId: string
  requestedBy: string
  priority: PriorityLevel
  estimatedCost?: number
  problemDescription: string
  attachments?: File[]
  urgency: UrgencyLevel
}

// ============================================================================
// FILTER AND SEARCH TYPES
// ============================================================================

/**
 * Asset filtering options
 */
export interface AssetFilter {
  types?: AssetType[]
  statuses?: AssetStatus[]
  conditions?: AssetCondition[]
  locations?: string[]
  assignees?: string[]
  vendors?: string[]
  categories?: string[]
  dateRange?: {
    start: string
    end: string
  }
  valueRange?: {
    min: number
    max: number
  }
}

/**
 * Search and pagination parameters
 */
export interface SearchParams {
  query?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: AssetFilter
}

// ============================================================================
// STATISTICS AND ANALYTICS TYPES
// ============================================================================

/**
 * Dashboard statistics
 */
export interface DashboardStats {
  totalAssets: number
  activeAssignments: number
  pendingMaintenance: number
  totalValue: number
  utilizationRate: number
  overdueItems: number
  monthlyGrowth: number
  maintenanceCosts: number
}

/**
 * Chart data point
 */
export interface ChartDataPoint {
  label: string
  value: number
  color?: string
  metadata?: Record<string, any>
}

/**
 * Time series data point
 */
export interface TimeSeriesDataPoint {
  date: string
  value: number
  category?: string
}

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

/**
 * Base component props
 */
export interface BaseComponentProps {
  className?: string
  children?: ReactNode
  'data-testid'?: string
}

/**
 * Loading state props
 */
export interface LoadingProps {
  loading?: boolean
  error?: string | null
  retry?: () => void
}

/**
 * Pagination props
 */
export interface PaginationProps {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onItemsPerPageChange?: (items: number) => void
}

/**
 * Table column definition
 */
export interface TableColumn<T = any> {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: T) => ReactNode
  className?: string
}

/**
 * Action button configuration
 */
export interface ActionButton {
  label: string
  icon?: ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  onClick: () => void
  disabled?: boolean
  loading?: boolean
  className?: string
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T = any> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  error?: string
  message?: string
  timestamp: string
}

// ============================================================================
// STATE MANAGEMENT TYPES
// ============================================================================

/**
 * Application state
 */
export interface AppState {
  assets: AssetState
  assignments: AssignmentState
  maintenance: MaintenanceState
  ui: UIState
  user: UserState
}

/**
 * Asset state slice
 */
export interface AssetState {
  items: Asset[]
  selectedAsset: Asset | null
  filters: AssetFilter
  searchQuery: string
  loading: boolean
  error: string | null
  stats: DashboardStats | null
}

/**
 * Assignment state slice
 */
export interface AssignmentState {
  items: Assignment[]
  selectedAssignment: Assignment | null
  loading: boolean
  error: string | null
}

/**
 * Maintenance state slice
 */
export interface MaintenanceState {
  items: MaintenanceRecord[]
  selectedRecord: MaintenanceRecord | null
  loading: boolean
  error: string | null
}

/**
 * UI state slice
 */
export interface UIState {
  activeTab: TabType
  sidebarOpen: boolean
  theme: 'light' | 'dark'
  notifications: Notification[]
  dialogs: {
    addAsset: boolean
    createAssignment: boolean
    scheduleMaintenance: boolean
    requestRepair: boolean
  }
}

/**
 * User state slice
 */
export interface UserState {
  id: string
  name: string
  email: string
  role: UserRole
  permissions: Permission[]
  preferences: UserPreferences
}

/**
 * Notification
 */
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: NotificationAction[]
}

/**
 * Notification action
 */
export interface NotificationAction {
  label: string
  action: () => void
  variant?: 'primary' | 'secondary'
}

/**
 * User role
 */
export type UserRole = 'admin' | 'manager' | 'employee' | 'viewer'

/**
 * Permission
 */
export type Permission =
  | 'assets:read'
  | 'assets:write'
  | 'assets:delete'
  | 'assignments:read'
  | 'assignments:write'
  | 'assignments:delete'
  | 'maintenance:read'
  | 'maintenance:write'
  | 'maintenance:delete'
  | 'reports:read'
  | 'reports:export'
  | 'users:read'
  | 'users:write'

/**
 * User preferences
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  dateFormat: string
  currency: string
  notifications: {
    email: boolean
    push: boolean
    inApp: boolean
  }
}

/**
 * Tab type
 */
export type TabType =
  | 'dashboard'
  | 'inventory'
  | 'assignments'
  | 'maintenance'
  | 'reports'
  | 'employeeportal'

// ============================================================================
// VALIDATION SCHEMAS (for Zod)
// ============================================================================

/**
 * Validation error
 */
export interface ValidationError {
  field: string
  message: string
  code?: string
}

/**
 * Form validation result
 */
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  data?: any
}

// ============================================================================
// EXPORT UTILITIES
// ============================================================================

/**
 * Export format
 */
export type ExportFormat = 'csv' | 'xlsx' | 'pdf' | 'json'

/**
 * Export options
 */
export interface ExportOptions {
  format: ExportFormat
  filename?: string
  includeHeaders?: boolean
  dateRange?: {
    start: string
    end: string
  }
  filters?: AssetFilter
  columns?: string[]
}

// ============================================================================
// CHART TYPES
// ============================================================================

/**
 * Chart type
 */
export type ChartType =
  | 'line'
  | 'bar'
  | 'pie'
  | 'doughnut'
  | 'area'
  | 'scatter'

/**
 * Chart configuration
 */
export interface ChartConfig {
  type: ChartType
  title: string
  data: ChartDataPoint[] | TimeSeriesDataPoint[]
  options?: {
    responsive?: boolean
    maintainAspectRatio?: boolean
    legend?: boolean
    tooltip?: boolean
    colors?: string[]
    height?: number
  }
}

// ============================================================================
// MISSING TYPES - ADDED TO FIX ERRORS
// ============================================================================

/**
 * Tab item configuration
 */
export interface TabItem {
  /** Unique tab identifier */
  id: TabType
  /** Tab label */
  label: string
  /** Tab icon */
  icon?: React.ReactNode
  /** Whether tab is disabled */
  disabled?: boolean
  /** Badge count */
  badge?: number
  /** Badge color */
  badgeColor?: 'default' | 'success' | 'warning' | 'error'
}


