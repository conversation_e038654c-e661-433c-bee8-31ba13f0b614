"use client"

import React from 'react';
import { X, ChevronDown } from 'lucide-react';

interface CreateShareModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateShareModal: React.FC<CreateShareModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg shadow-xl w-full max-w-lg mx-4">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-foreground">Create Share Link</h3>
              <p className="text-sm text-muted-foreground">Generate a secure link to share a document</p>
            </div>
            <button 
              onClick={onClose}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Select Document</label>
              <div className="relative">
                <select className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background appearance-none">
                  <option>Choose a Document</option>
                  <option>Employee Handbook.pdf</option>
                  <option>Project proposal.docx</option>
                  <option>Financial Report.xlsx</option>
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Recipients (email addresses)</label>
              <textarea
                placeholder="Enter email addresses one per line"
                rows={3}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Permission Level</label>
              <div className="relative">
                <select className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background appearance-none">
                  <option>View Only</option>
                  <option>View & Download</option>
                  <option>View, Download & Edit</option>
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Link Expiry</label>
              <div className="relative">
                <select className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background appearance-none">
                  <option>7 days</option>
                  <option>30 days</option>
                  <option>60 days</option>
                  <option>90 days</option>
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Maximum Access Count</label>
              <input
                type="number"
                placeholder="0"
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="password" className="rounded" />
                <label htmlFor="password" className="text-sm text-foreground">Require password for access</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="notifications" className="rounded" defaultChecked />
                <label htmlFor="notifications" className="text-sm text-foreground">Send email notifications to recipients</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="tracking" className="rounded" defaultChecked />
                <label htmlFor="tracking" className="text-sm text-foreground">Enable access tracking</label>
              </div>
            </div>
          </div>

          <button 
            onClick={onClose}
            className="w-full mt-6 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
          >
            Create share link
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateShareModal;