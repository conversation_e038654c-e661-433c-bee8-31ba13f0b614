"use client"

import React from 'react';
import { X, Users } from 'lucide-react';

interface SignatureRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SignatureRequestModal: React.FC<SignatureRequestModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-foreground">New Signature Request</h3>
              <p className="text-sm text-muted-foreground">Create a new electronic signature workflow</p>
            </div>
            <button 
              onClick={onClose}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Select Document</label>
              <select className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background">
                <option>Choose a document</option>
                <option>Employee Contract - John Doe.pdf</option>
                <option>NDA Agreement - Vendor XYZ.pdf</option>
                <option>Company Policy Update.docx</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Workflow Type</label>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-muted">
                  <input type="radio" name="workflow" value="sequential" className="text-blue-600" />
                  <div>
                    <div className="font-medium text-sm">Sequential</div>
                    <div className="text-xs text-muted-foreground">Sign in order</div>
                  </div>
                </label>
                <label className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-muted">
                  <input type="radio" name="workflow" value="parallel" className="text-blue-600" />
                  <div>
                    <div className="font-medium text-sm">Parallel</div>
                    <div className="text-xs text-muted-foreground">Sign simultaneously</div>
                  </div>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Signers</label>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <input
                    type="email"
                    placeholder="Enter email address"
                    className="flex-1 bg-transparent focus:outline-none"
                  />
                  <button className="text-red-600 hover:text-red-800">
                    <X className="w-4 h-4" />
                  </button>
                </div>
                <button className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-sm text-muted-foreground hover:border-blue-400 hover:text-blue-600 transition-colors">
                  + Add another signer
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Due Date</label>
              <input
                type="date"
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Message (Optional)</label>
              <textarea
                placeholder="Add a message for signers..."
                rows={3}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <button 
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button 
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              Send for Signature
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignatureRequestModal;