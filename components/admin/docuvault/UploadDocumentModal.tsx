"use client"

import React from 'react';
import { X, Upload } from 'lucide-react';

interface UploadDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const UploadDocumentModal: React.FC<UploadDocumentModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg shadow-xl w-full max-w-lg mx-4">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-foreground">Upload Document</h3>
              <p className="text-sm text-muted-foreground">Upload a new document to the repository. Max file size: 100MB</p>
            </div>
            <button 
              onClick={onClose}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Select File</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                <input type="file" className="hidden" id="file-upload" />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Choose File</p>
                  <p className="text-xs text-gray-400">No file chosen</p>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Folder</label>
              <input
                type="text"
                placeholder="e.g. HR/Contracts/2025"
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Tags (comma separated)</label>
              <input
                type="text"
                placeholder="e.g. Confidential, Contract/2025"
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Description</label>
              <textarea
                placeholder="Optional Description..."
                rows={3}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input type="checkbox" id="encrypt" className="rounded" />
              <label htmlFor="encrypt" className="text-sm text-foreground">Encrypt document (AES-256)</label>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">Upload Progress</label>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-black h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
          </div>

          <button 
            onClick={onClose}
            className="w-full mt-6 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
          >
            Upload Document
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadDocumentModal;