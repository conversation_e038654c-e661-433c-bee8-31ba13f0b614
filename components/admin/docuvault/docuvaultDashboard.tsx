"use client"

import React, { useState } from 'react';
import { Search, ChevronDown, MoreHorizontal, File, Download, Share, Edit, Clock, Trash2, Eye, Mail, ExternalLink, RotateCcw, Database, Upload, FileText, FolderPlus, Filter } from 'lucide-react';
import UploadDocumentModal from './UploadDocumentModal';
import NewFolderModal from './NewFolderModal';
import SignatureRequestModal from './SignatureRequestModal';
import CreateShareModal from './CreateShareModal';
import AdvancedFilterModal from './AdvancedFilterModal';

const DocuVaultDashboard = () => {
  const [activeTab, setActiveTab] = useState('Documents');
  const [openDropdown, setOpenDropdown] = useState<number | null>(null);
  const [openShareDropdown, setOpenShareDropdown] = useState<number | null>(null);
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);
  const [showSignatureDialog, setShowSignatureDialog] = useState(false);
  const [showCreateShareDialog, setShowCreateShareDialog] = useState(false);

  const tabs = ['Documents', 'E-Signatures', 'Sharing', 'Audit Logs', 'Settings'];

  const getSummaryCards = () => {
    switch (activeTab) {
      case 'E-Signatures':
        return [
          { title: 'Pending Signatures', value: '1', subtext: 'Awaiting signatures', color: 'text-foreground' },
          { title: 'Completed', value: '1', subtext: 'This month', color: 'text-foreground' },
          { title: 'Rejected', value: '0', subtext: 'Requires attention', color: 'text-foreground' },
          { title: 'Compliance', value: '100%', subtext: 'eIDAS & ESIGN compliant', color: 'text-foreground' }
        ];
      case 'Sharing':
        return [
          { title: 'Active Shares', value: '3', subtext: 'Currently active', color: 'text-foreground' },
          { title: 'Total Access', value: '56', subtext: 'Document views', color: 'text-foreground' },
          { title: 'Expiring Soon', value: '3', subtext: 'Within 3 days', color: 'text-foreground' },
          { title: 'Security', value: '100%', subtext: 'Encrypted shares', color: 'text-foreground' }
        ];
      case 'Audit Logs':
        return [
          { title: 'Total Events', value: '6', subtext: 'Last 30 days', color: 'text-foreground' },
          { title: 'High Risk Events', value: '2', subtext: 'Require attention', color: 'text-foreground' },
          { title: 'Failed Actions', value: '1', subtext: 'Security incidents', color: 'text-foreground' },
          { title: 'Active Users', value: '5', subtext: 'Unique users', color: 'text-foreground' }
        ];
      default:
        return [
          { title: 'Total Documents', value: '1,247', subtext: '+12% from last month', color: 'text-foreground' },
          { title: 'Storage Used', value: '847 GB', subtext: '84.7% of 1TB limit', color: 'text-foreground' },
          { title: 'Pending Signatures', value: '23', subtext: '5 urgent', color: 'text-foreground' },
          { title: 'Shared Documents', value: '156', subtext: 'Active shares', color: 'text-foreground' }
        ];
    }
  };

  const documents = [
    {
      name: 'Employee Contract - John Doe.pdf',
      size: '2.4 MB',
      type: 'PDF',
      tags: ['Confidential', 'Contract', 'EMP-001'],
      owner: 'Sarah Wilson',
      folder: 'HR/Contracts/2025',
      status: 'Pending Signature',
      statusColor: 'bg-red-500',
      version: 'v3.1',
      shared: '< 3',
      lastAccessed: '2025-01-20'
    },
    {
      name: 'Q4 Financial Report.xlsx',
      size: '5.3 MB',
      type: 'Excel',
      tags: ['Financial', 'Q4', 'Report'],
      owner: 'Michael Chen',
      folder: 'Finance/Reports/2024',
      status: 'Active',
      statusColor: 'bg-green-500',
      version: 'v1.3',
      shared: '< 8',
      lastAccessed: '2025-01-19'
    },
    {
      name: 'Company Policy Update.docx',
      size: '1.2 MB',
      type: 'Word',
      tags: ['Policy', 'Legal', 'Update'],
      owner: 'Lisa Rodriguez',
      folder: 'Legal/Policies',
      status: 'Active',
      statusColor: 'bg-green-500',
      version: 'v2.1',
      shared: '< 15',
      lastAccessed: '2025-01-18'
    },
    {
      name: 'Project Proposal - Alpha.pdf',
      size: '3.1 MB',
      type: 'PDF',
      tags: ['Proposal', 'Alpha', 'Draft'],
      owner: 'David Kim',
      folder: 'Projects/Alpha',
      status: 'Active',
      statusColor: 'bg-green-500',
      version: 'v4.0',
      shared: '< 5',
      lastAccessed: '2025-01-17'
    }
  ];

  const signatureRequests = [
    {
      document: 'Employee Contract - John Doe.pdf',
      requester: 'Sarah Wilson',
      workflow: 'Sequential',
      signers: [
        { name: 'John Doe', completed: true },
        { name: 'Sarah Wilson', completed: false }
      ],
      progress: 50,
      status: 'In Progress',
      statusColor: 'bg-blue-500',
      dueDate: '2025-01-22'
    },
    {
      document: 'NDA Agreement - Vendor XYZ.pdf',
      requester: 'Michael Chen',
      workflow: 'Parallel',
      signers: [
        { name: 'Michael Chen', completed: true },
        { name: 'Alex Johnson', completed: true }
      ],
      progress: 100,
      status: 'Completed',
      statusColor: 'bg-green-500',
      dueDate: '2025-01-17'
    }
  ];

  const shareLinks = [
    {
      document: 'Employee Contract - John Doe.pdf',
      sharedBy: 'Sarah Wilson',
      recipients: ['<EMAIL>', '<EMAIL>'],
      permission: 'View only',
      permissionColor: 'bg-blue-500',
      access: '3/10',
      expires: '2025-01-25',
      status: 'Active',
      statusColor: 'bg-green-500',
      created: '2025-01-15'
    },
    {
      document: 'Q4 Financial Report.xlsx',
      sharedBy: 'Michael Chen',
      recipients: ['<EMAIL>', '<EMAIL>'],
      permission: 'Download',
      permissionColor: 'bg-orange-500',
      access: '6/5',
      expires: '2025-02-15',
      status: 'Restricted',
      statusColor: 'bg-red-500',
      created: '2025-01-10'
    },
    {
      document: 'Company Policy Update.docx',
      sharedBy: 'Lisa Rodriguez',
      recipients: ['<EMAIL>'],
      permission: 'View only',
      permissionColor: 'bg-blue-500',
      access: '45/100',
      expires: '2025-04-15',
      status: 'Active',
      statusColor: 'bg-green-500',
      created: '2025-01-08'
    }
  ];

  const actionItems = [
    { icon: Eye, label: 'View' },
    { icon: Download, label: 'Download' },
    { icon: Share, label: 'Share' },
    { icon: Edit, label: 'Edit' },
    { icon: Clock, label: 'Version History' },
    { icon: Trash2, label: 'Delete', danger: true }
  ];

  const shareActionItems = [
    { icon: Eye, label: 'View Details' },
    { icon: Edit, label: 'Edit Permissions' },
    { icon: Mail, label: 'Send Reminder' },
    { icon: RotateCcw, label: 'Regenerate Link' },
    { icon: Trash2, label: 'Revoke Access', danger: true }
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <File className="w-4 h-4 text-red-600" />;
      case 'Excel':
        return <File className="w-4 h-4 text-green-600" />;
      case 'Word':
        return <File className="w-4 h-4 text-blue-600" />;
      default:
        return <File className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case 'E-Signatures':
        return { title: 'E-Signatures', subtitle: 'Manage document signatures and workflows' };
      case 'Sharing':
        return { title: 'Document Sharing', subtitle: 'Manage secure document shares and access controls' };
      case 'Audit Logs':
        return { title: 'Audit Logs', subtitle: 'Track document access and modifications' };
      case 'Settings':
        return { title: 'Settings', subtitle: 'Configure document management preferences' };
      default:
        return { title: 'Document Management', subtitle: 'Centralized, secure document repository' };
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Documents':
        return (
          <div className="px-6 pb-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-base font-semibold text-foreground">Search & Filter</h3>
              </div>
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search Documents, Logs or folders..."
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
                  />
                </div>
                <div className="flex gap-3">
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Folders</option>
                      <option>HR</option>
                      <option>Finance</option>
                      <option>Legal</option>
                      <option>Projects</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Types</option>
                      <option>PDF</option>
                      <option>Word</option>
                      <option>Excel</option>
                      <option>PowerPoint</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Owners</option>
                      <option>Sarah Wilson</option>
                      <option>Michael Chen</option>
                      <option>Lisa Rodriguez</option>
                      <option>David Kim</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-base font-semibold text-foreground mb-1">Documents</h3>
              <p className="text-sm text-muted-foreground">Manage your organization's documents with version control and secure sharing</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Document</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Owner</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Folder</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Status</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Version</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Shared</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Last Accessed</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((doc, index) => (
                    <tr key={index} className="border-b hover:bg-muted/50 transition-colors">
                      <td className="py-4">
                        <div className="flex items-start space-x-3">
                          {getFileIcon(doc.type)}
                          <div>
                            <div className="font-medium text-foreground text-sm">{doc.name}</div>
                            <div className="text-xs text-muted-foreground mb-1">{doc.size} • {doc.type}</div>
                            <div className="flex flex-wrap gap-1">
                              {doc.tags.map((tag, tagIndex) => (
                                <span 
                                  key={tagIndex}
                                  className="inline-block px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 text-sm text-foreground">{doc.owner}</td>
                      <td className="py-4 text-sm text-muted-foreground">{doc.folder}</td>
                      <td className="py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${doc.statusColor} text-white`}>
                          {doc.status}
                        </span>
                      </td>
                      <td className="py-4 text-sm text-foreground">{doc.version}</td>
                      <td className="py-4 text-sm text-muted-foreground">{doc.shared}</td>
                      <td className="py-4 text-sm text-muted-foreground">{doc.lastAccessed}</td>
                      <td className="py-4">
                        <div className="relative">
                          <button
                            onClick={() => setOpenDropdown(openDropdown === index ? null : index)}
                            className="p-2 hover:bg-muted rounded-lg transition-colors"
                          >
                            <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
                          </button>
                          
                          {openDropdown === index && (
                            <div className="absolute right-0 top-full mt-1 w-48 bg-popover border rounded-lg shadow-lg z-50">
                              <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b">
                                Actions
                              </div>
                              {actionItems.map((action, actionIndex) => (
                                <button
                                  key={actionIndex}
                                  className={`w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-muted transition-colors ${
                                    action.danger ? 'text-destructive' : 'text-foreground'
                                  } ${actionIndex === actionItems.length - 1 ? 'rounded-b-lg' : ''}`}
                                  onClick={() => setOpenDropdown(null)}
                                >
                                  <action.icon className="w-4 h-4" />
                                  <span>{action.label}</span>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'E-Signatures':
        return (
          <div className="px-6 pb-6">
            <div className="mb-4">
              <h3 className="text-base font-semibold text-foreground mb-1">Signature Requests</h3>
              <p className="text-sm text-muted-foreground">Track and manage electronic signature workflows</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Document</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Requester</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Workflow</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Progress</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Status</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Due Date</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {signatureRequests.map((request, index) => (
                    <tr key={index} className="border-b hover:bg-muted/50 transition-colors">
                      <td className="py-4">
                        <div className="flex items-start space-x-3">
                          <FileText className="w-4 h-4 text-blue-600 mt-0.5" />
                          <div>
                            <div className="font-medium text-foreground text-sm">{request.document}</div>
                            <div className="text-xs text-muted-foreground">{request.workflow} workflow</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 text-sm text-foreground">{request.requester}</td>
                      <td className="py-4">
                        <div className="space-y-1">
                          {request.signers.map((signer, signerIndex) => (
                            <div key={signerIndex} className="flex items-center space-x-2 text-xs">
                              <div className={`w-2 h-2 rounded-full ${signer.completed ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                              <span className={signer.completed ? 'text-green-600' : 'text-muted-foreground'}>
                                {signer.name}
                              </span>
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${request.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-muted-foreground">{request.progress}%</span>
                        </div>
                      </td>
                      <td className="py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${request.statusColor} text-white`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="py-4 text-sm text-muted-foreground">{request.dueDate}</td>
                      <td className="py-4">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Sharing':
        return (
          <div className="px-6 pb-6">
            <div className="mb-4">
              <h3 className="text-base font-semibold text-foreground mb-1">Share Links</h3>
              <p className="text-sm text-muted-foreground">Manage document sharing links and access permissions</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Document</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Shared By</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Recipients</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Permission</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Access</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Expires</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Status</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {shareLinks.map((link, index) => (
                    <tr key={index} className="border-b hover:bg-muted/50 transition-colors">
                      <td className="py-4">
                        <div className="flex items-start space-x-3">
                          <File className="w-4 h-4 text-red-600 mt-0.5" />
                          <div>
                            <div className="font-medium text-foreground text-sm">{link.document}</div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <ExternalLink className="w-3 h-3" />
                              Created {link.created}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 text-sm text-foreground">{link.sharedBy}</td>
                      <td className="py-4">
                        <div className="text-xs text-muted-foreground space-y-1">
                          {link.recipients.map((recipient, recipientIndex) => (
                            <div key={recipientIndex}>{recipient}</div>
                          ))}
                        </div>
                      </td>
                      <td className="py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${link.permissionColor} text-white`}>
                          {link.permission}
                        </span>
                      </td>
                      <td className="py-4 text-sm text-foreground">{link.access}</td>
                      <td className="py-4 text-sm text-muted-foreground">{link.expires}</td>
                      <td className="py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${link.statusColor} text-white`}>
                          {link.status}
                        </span>
                      </td>
                      <td className="py-4">
                        <div className="relative">
                          <button
                            onClick={() => setOpenShareDropdown(openShareDropdown === index ? null : index)}
                            className="p-2 hover:bg-muted rounded-lg transition-colors"
                          >
                            <Share className="w-4 h-4 text-muted-foreground" />
                          </button>
                          
                          {openShareDropdown === index && (
                            <div className="absolute right-0 top-full mt-1 w-48 bg-popover border rounded-lg shadow-lg z-50">
                              <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b">
                                Actions
                              </div>
                              {shareActionItems.map((action, actionIndex) => (
                                <button
                                  key={actionIndex}
                                  className={`w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-muted transition-colors ${
                                    action.danger ? 'text-destructive' : 'text-foreground'
                                  } ${actionIndex === shareActionItems.length - 1 ? 'rounded-b-lg' : ''}`}
                                  onClick={() => setOpenShareDropdown(null)}
                                >
                                  <action.icon className="w-4 h-4" />
                                  <span>{action.label}</span>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Audit Logs':
        return (
          <div className="px-6 pb-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-base font-semibold text-foreground mb-1">Search & Filter Audit Logs</h3>
                </div>
              </div>

              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search by actions, entity, user, email..."
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
                  />
                </div>
                
                <div className="flex gap-3">
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Entities</option>
                      <option>Document</option>
                      <option>Signature</option>
                      <option>Share</option>
                      <option>User</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                  
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Actions</option>
                      <option>Read</option>
                      <option>Sign</option>
                      <option>Share</option>
                      <option>Create</option>
                      <option>Access</option>
                      <option>Update</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                  
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Risk</option>
                      <option>Low Risk</option>
                      <option>Medium Risk</option>
                      <option>High Risk</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                  
                  <div className="relative">
                    <select className="appearance-none bg-background border rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                      <option>All Users</option>
                      <option>John Doe</option>
                      <option>Michael Chen</option>
                      <option>David Kim</option>
                      <option>Unknown User</option>
                      <option>Sarah Wilson</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-base font-semibold text-foreground mb-1">Audit Trail (0 events)</h3>
              <p className="text-sm text-muted-foreground">Detailed log of all system activities for compliance and security monitoring</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Document</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Owner</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Folder</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Status</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Version</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Shared</th>
                    <th className="pb-3 text-sm font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colSpan={7} className="py-12 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Database className="w-12 h-12 mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No audit events found</p>
                        <p className="text-sm">Try adjusting your search criteria or date range</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Settings':
        return (
          <div className="px-6 pb-6">
            <div className="space-y-6">
              <div className="bg-card border rounded-lg p-6">
                <h4 className="text-sm font-medium text-foreground mb-4">Notifications</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-foreground">Document Expiry Notification</div>
                      <div className="text-xs text-muted-foreground">Get notified when documents are about to expire</div>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6" />
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-foreground">Signature Request Updates</div>
                      <div className="text-xs text-muted-foreground">Receive updates on signature request status</div>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-card border rounded-lg p-6">
                <h4 className="text-sm font-medium text-foreground mb-4">Compliance</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-foreground">Compliance Mode</div>
                      <div className="text-xs text-muted-foreground">eIDAS/ESIGN compliance enabled</div>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="min-h-screen space-y-6">
        {/* Section 1: Dashboard Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getSummaryCards().map((card, index) => (
            <div key={index} className="bg-card rounded-lg shadow-sm border p-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">{card.title}</h3>
              <div className={`text-2xl font-bold ${card.color} mb-1`}>{card.value}</div>
              <p className="text-xs text-muted-foreground">{card.subtext}</p>
            </div>
          ))}
        </div>

        {/* Section 2: Document Management Content */}
        <div className="bg-card rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-foreground mb-1">{getTabTitle().title}</h2>
                <p className="text-sm text-muted-foreground">{getTabTitle().subtitle}</p>
              </div>

              {/* Tab-specific action buttons */}
              <div className="flex items-center gap-3">
                {activeTab === 'Documents' && (
                  <>
                    <button
                      onClick={() => setShowUploadDialog(true)}
                      className="flex items-center gap-2 px-4 py-2 text-sm bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                    >
                      <Upload className="w-4 h-4" />
                      Upload Document
                    </button>
                    <button 
                      onClick={() => setShowNewFolderDialog(true)}
                      className="flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FolderPlus className="w-4 h-4" />
                      New Folder
                    </button>
                  </>
                )}
                
                {activeTab === 'E-Signatures' && (
                  <button 
                    onClick={() => setShowSignatureDialog(true)}
                    className="flex items-center gap-2 px-4 py-2 text-sm bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                  >
                    <FileText className="w-4 h-4" />
                    New Signature Request
                  </button>
                )}
                
                {activeTab === 'Sharing' && (
                  <button 
                    onClick={() => setShowCreateShareDialog(true)}
                    className="flex items-center gap-2 px-4 py-2 text-sm bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                  >
                    <Share className="w-4 h-4" />
                    Create share link
                  </button>
                )}
                
                {activeTab === 'Audit Logs' && (
                  <>
                    <button className="flex items-center gap-2 px-4 py-2 text-sm bg-card border rounded-lg hover:bg-muted transition-colors">
                      <Upload className="w-4 h-4" />
                      Export CSV
                    </button>
                    <button 
                      onClick={() => setShowAdvancedFilter(true)}
                      className="flex items-center gap-2 px-4 py-2 text-sm bg-card border rounded-lg hover:bg-muted transition-colors"
                    >
                      <Filter className="w-4 h-4" />
                      Advanced Filter
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === tab ? 'border-blue-600 text-blue-600' : 'border-transparent text-muted-foreground hover:border-gray-300'}`}
                >
                  {tab}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          {renderTabContent()}
        </div>
      </div>

      {/* Modals */}
      <UploadDocumentModal 
        isOpen={showUploadDialog} 
        onClose={() => setShowUploadDialog(false)} 
      />
      <NewFolderModal 
        isOpen={showNewFolderDialog} 
        onClose={() => setShowNewFolderDialog(false)} 
      />
      <SignatureRequestModal 
        isOpen={showSignatureDialog} 
        onClose={() => setShowSignatureDialog(false)} 
      />
      <CreateShareModal 
        isOpen={showCreateShareDialog} 
        onClose={() => setShowCreateShareDialog(false)} 
      />
      <AdvancedFilterModal 
        isOpen={showAdvancedFilter} 
        onClose={() => setShowAdvancedFilter(false)} 
      />
    </>
  );
};

export default DocuVaultDashboard;