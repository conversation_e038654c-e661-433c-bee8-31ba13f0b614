import React, { useState } from 'react';

interface SummaryCardProps {
  title: string;
  value: string;
  subtitle: string;
}

interface CandidateData {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  startDate: string;
  assignedTo: string;
  tasksCompleted: number;
  totalTasks: number;
  progress: number;
  isCompleted: boolean;
}

interface WorkflowTemplate {
  id: string;
  title: string;
  description: string;
  tasks: number;
  duration: string;
  usage: string;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, subtitle }) => (
  <div className="bg-card p-6 rounded-lg shadow-sm border border-border hover:shadow-md transition-shadow">
    <h3 className="text-sm font-medium text-muted-foreground mb-2">{title}</h3>
    <div className="text-2xl font-semibold text-foreground mb-1">{value}</div>
    <p className="text-sm text-muted-foreground">{subtitle}</p>
  </div>
);

const ProgressBar: React.FC<{ progress: number; isCompleted: boolean }> = ({ progress, isCompleted }) => (
  <div className="w-full bg-muted rounded-full h-2">
    <div
      className={`h-2 rounded-full transition-all duration-300 ${
        isCompleted ? 'bg-success' : 'bg-primary'
      }`}
      style={{ width: `${progress}%` }}
    />
  </div>
);

const CandidateCard: React.FC<{ candidate: CandidateData }> = ({ candidate }) => (
  <div className="bg-card p-6 rounded-lg shadow-sm border border-border hover:shadow-md transition-shadow">
    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
      <div className="flex-1">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-4">
          <div className="flex-1">
            <h3 className="font-semibold text-foreground text-xl my-2">{candidate.name}</h3>
            <p className="text-sm text-muted-foreground">{candidate.email}</p>
            <p className="text-sm text-muted-foreground">
              {candidate.role} • {candidate.department}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                candidate.isCompleted
                  ? 'bg-success/10 text-success'
                  : 'bg-primary/10 text-primary'
              }`}
            >
              In Progress
            </span>
            <button className="px-4 py-2 text-sm font-medium border border-border rounded-md hover:bg-accent transition-colors">
              View Details
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-xs text-muted-foreground">Start Date</p>
            <p className="text-sm font-medium">{candidate.startDate}</p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Assigned To</p>
            <p className="text-sm font-medium">{candidate.assignedTo}</p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Tasks Progress</p>
            <p className="text-sm font-medium">
              {candidate.tasksCompleted}/{candidate.totalTasks} Completed
            </p>
          </div>
        </div>
        
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Progress</span>
            <span className="text-sm font-medium">{candidate.progress}%</span>
          </div>
          <ProgressBar progress={candidate.progress} isCompleted={candidate.isCompleted} />
        </div>
      </div>
    </div>
  </div>
);

const WorkflowTemplateCard: React.FC<{ template: WorkflowTemplate }> = ({ template }) => (
  <div className="bg-card p-6 rounded-lg shadow-sm border border-border hover:shadow-md transition-shadow">
    <h3 className="font-semibold text-foreground mb-2">{template.title}</h3>
    <p className="text-sm text-muted-foreground mb-4">{template.description}</p>
    
    <div className="space-y-2 mb-6">
      <div className="flex justify-between">
        <span className="text-sm text-muted-foreground">Tasks:</span>
        <span className="text-sm font-medium">{template.tasks}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-sm text-muted-foreground">Duration</span>
        <span className="text-sm font-medium">{template.duration}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-sm text-muted-foreground">Usage:</span>
        <span className="text-sm font-medium">{template.usage}</span>
      </div>
    </div>
    
    <div className="flex gap-2">
      <button className="flex-1 px-4 py-2 text-sm font-medium border border-border rounded-md hover:bg-accent transition-colors">
        Edit
      </button>
      <button className="flex-1 px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
        View Details
      </button>
    </div>
  </div>
);

interface OnboardingDashboardProps {
  onNavigateToWorkflowBuilder?: () => void;
  onNavigateToStartOnboarding?: () => void;
}

export const OnboardingDashboard: React.FC<OnboardingDashboardProps> = ({ onNavigateToWorkflowBuilder, onNavigateToStartOnboarding }) => {
  const [activeTab, setActiveTab] = useState<'candidates' | 'templates' | 'tasks'>('candidates');

  const summaryData = [
    { title: 'Active Onboarding', value: '23', subtitle: 'new hires in progress' },
    { title: 'Completed this month', value: '47', subtitle: '+10% from last month' },
    { title: 'Average duration', value: '4.2', subtitle: 'Days to complete' },
    { title: 'Pending Tasks', value: '12', subtitle: 'Require attention' },
  ];

  const candidatesData: CandidateData[] = [
    {
      id: '1',
      name: 'Alexb Thompson',
      email: '<EMAIL>',
      role: 'Software Engineer',
      department: 'Engineering',
      startDate: '2024-01-15',
      assignedTo: 'Saraha Jhonson',
      tasksCompleted: 6,
      totalTasks: 8,
      progress: 75,
      isCompleted: false,
    },
    {
      id: '2',
      name: 'Maria Garcia',
      email: '<EMAIL>',
      role: 'Marketing Manager',
      department: 'Marketing',
      startDate: '2024-01-20',
      assignedTo: 'David Taylor',
      tasksCompleted: 4,
      totalTasks: 9,
      progress: 45,
      isCompleted: false,
    },
    {
      id: '3',
      name: 'James Wilson',
      email: '<EMAIL>',
      role: 'Sales Representative',
      department: 'Sales',
      startDate: '2024-01-10',
      assignedTo: 'Lisa Wilson',
      tasksCompleted: 7,
      totalTasks: 7,
      progress: 100,
      isCompleted: true,
    },
  ];

  const workflowTemplates: WorkflowTemplate[] = [
    {
      id: '1',
      title: 'Standard Onboarding',
      description: 'Default workflow for full-time employee',
      tasks: 8,
      duration: '5 days',
      usage: 'Most common',
    },
    {
      id: '2',
      title: 'Contractor Onboarding',
      description: 'Simplified workflow for contractors',
      tasks: 5,
      duration: '2 days',
      usage: 'Contract Workers',
    },
    {
      id: '3',
      title: 'Executive Onboarding',
      description: 'Comprehensive workflow for leadership',
      tasks: 12,
      duration: '10 days',
      usage: 'C-level & VPs',
    },
  ];

  const tabs = [
    { id: 'candidates' as const, label: 'Active Candidates' },
    { id: 'templates' as const, label: 'Workflow Templates' },
    { id: 'tasks' as const, label: 'Recent Tasks' },
  ];

  return (
    <div className="">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold text-foreground ml-3">Onboarding Management</h1>
          <p className="text-muted-foreground mb-8 ml-3">Onboarding Management</p>
        </div>
        <div className="flex gap-3">
          <button 
            onClick={onNavigateToWorkflowBuilder}
            className="px-4 py-2 text-sm font-medium border border-border rounded-md hover:bg-accent transition-colors"
          >
            Workflow Builder
          </button>
          <button 
            onClick={onNavigateToStartOnboarding}
            className="px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Start Onboarding
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryData.map((card, index) => (
          <SummaryCard key={index} {...card} />
        ))}
      </div>

      {/* Navigation Tabs */}
      <div className="flex flex-wrap gap-5 p-1 bg-muted rounded-lg w-fit mx-3 my-5">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-card text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      {activeTab === 'candidates' && (
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold text-foreground mb-2 ml-2">Active Onboarding Candidates</h2>
            <p className="text-muted-foreground ml-2">Track progress and manage onboarding workflows for new hires</p>
          </div>
          <div className="space-y-4">
            {candidatesData.map((candidate) => (
              <CandidateCard key={candidate.id} candidate={candidate} />
            ))}
          </div>
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold text-foreground mb-2 ml-2">Onboarding Workflow Templates</h2>
            <p className="text-muted-foreground ml-2">Pre-configured workflows for different employee types</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workflowTemplates.map((template) => (
              <WorkflowTemplateCard key={template.id} template={template} />
            ))}
          </div>
        </div>
      )}

      {activeTab === 'tasks' && (
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold text-foreground mb-2 ml-2">Recent Tasks</h2>
            <p className="text-muted-foreground ml-2">Latest onboarding tasks and activities</p>
          </div>
          <div className="bg-card p-8 rounded-lg shadow-sm border border-border text-center">
            <p className="text-muted-foreground">No recent tasks to display</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OnboardingDashboard;