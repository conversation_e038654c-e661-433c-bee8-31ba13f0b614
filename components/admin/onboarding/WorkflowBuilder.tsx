import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Plus, ArrowLeft } from 'lucide-react';

const WorkflowBuilder = () => {
  const [activeTab, setActiveTab] = useState('Workflow Builder');

  const tabs = ['Workflow Templates', 'Workflow Builder', 'Preview & Test'];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Workflow Templates':
        return <WorkflowTemplatesContent />;
      case 'Workflow Builder':
        return <WorkflowBuilderContent />;
      case 'Preview & Test':
        return <PreviewTestContent />;
      default:
        return <WorkflowBuilderContent />;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Workflow Builder</h1>
            <p className="text-muted-foreground">Design and customize onboarding workflows</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <Settings className="h-4 w-4" />
              Save Workflow
            </Button>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              New Workflow
            </Button>
          </div>
        </div>

        {/* Tabbed Navigation */}
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

// Workflow Templates Content
const WorkflowTemplatesContent = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-foreground">Existing Workflow Templates</h2>
        <p className="text-muted-foreground">Select a template to edit or create a new workflow from scratch</p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Standard Onboarding</CardTitle>
          <p className="text-muted-foreground">Default workflow for full-time employee</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="text-sm font-medium text-foreground">Category:</p>
              <p className="text-muted-foreground">Full-Time</p>
            </div>
            <div>
              <p className="text-sm font-medium text-foreground">Tasks:</p>
              <p className="text-muted-foreground">3</p>
            </div>
          </div>
          <div>
            <p className="text-sm font-medium text-foreground">Last Modified</p>
            <p className="text-muted-foreground">2024-01-20</p>
          </div>
          <div className="flex gap-3 pt-4">
            <Button variant="outline">Edit</Button>
            <Button>Clone</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Workflow Builder Content
const WorkflowBuilderContent = () => {
  const router = useRouter();
  
  const handleAddTask = () => {
    router.push('/admin/onboarding/add-task');
  };
  
  return (
    <div className="space-y-6">
      {/* Workflow Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Settings</CardTitle>
          <p className="text-muted-foreground">Configure basic workflow information</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Workflow Name</label>
              <Input placeholder="Enter workflow name" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Category</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full-time">Full-Time</SelectItem>
                  <SelectItem value="contractor">Contractor</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Description</label>
            <Textarea 
              placeholder="Describe the purpose and scope of this workflow"
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Workflow Tasks */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Workflow Tasks</CardTitle>
            <p className="text-muted-foreground">Add and configure tasks for your onboarding workflow</p>
          </div>
          <Button 
            className="gap-2"
            onClick={handleAddTask}
          >
            <Plus className="h-4 w-4" />
            Add Task
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Settings className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No tasks added yet</h3>
            <p className="text-muted-foreground mb-6">Start building your workflow by adding tasks</p>
            <Button 
              className="gap-2"
              onClick={handleAddTask}
            >
              <Plus className="h-4 w-4" />
              Add First Task
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Preview & Test Content
const PreviewTestContent = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Workflow Preview</CardTitle>
          <p className="text-muted-foreground">Review your workflow before saving and activation</p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <span className="text-2xl text-muted-foreground">!</span>
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">No workflow to preview</h3>
            <p className="text-muted-foreground mb-6">Create or load a workflow to see the preview</p>
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Go to Builder
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


export default WorkflowBuilder;