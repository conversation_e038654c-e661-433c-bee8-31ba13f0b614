"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  <PERSON>, 
  Bell, 
  Settings, 
  Download, 
  Play,
  User
} from "lucide-react"
import { SalaryStreamNavigation } from "./SalaryStreamNavigation"
import { DashboardView } from "./views/DashboardView"
import { EmployeeView } from "./views/EmployeeView"
import { TaxView } from "./views/TaxView"
import { ClaimsView } from "./views/ClaimsView"
import { LoansView } from "./views/LoansView"
import { PayrollView } from "./views/PayrollView"
import { SupportView } from "./views/SupportView"

export type ViewType = 'dashboard' | 'payroll' | 'employee' | 'tax' | 'claims' | 'loans' | 'support'

export function SalaryStreamDashboard() {
  const [activeView, setActiveView] = useState<ViewType>('dashboard')

  const renderView = () => {
    switch (activeView) {
      case 'dashboard':
        return <DashboardView />
      case 'payroll':
        return <PayrollView />
      case 'employee':
        return <EmployeeView />
      case 'tax':
        return <TaxView />
      case 'claims':
        return <ClaimsView />
      case 'loans':
        return <LoansView />
      case 'support':
        return <SupportView />
      default:
        return <DashboardView />
    }
  }

  return (
    <div className="min-h-screen ">
      
      
      {/* Main Content */}
      <div className="">
        {/* Page Title and Description */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-gray-900">Payroll Management System</h1>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                System online
              </span>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
          <p className="text-gray-600">Comprehensive payroll and HR management</p>
        </div>

        {/* Navigation */}
        <SalaryStreamNavigation activeView={activeView} onViewChange={setActiveView} />

        {/* Content */}
        <motion.div
          key={activeView}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-6"
        >
          {renderView()}
        </motion.div>
      </div>
    </div>
  )
}
