"use client"

import { <PERSON>, Bell } from "lucide-react"

export function SalaryStreamHeader() {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Title */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="h-10 w-10 bg-blue-600 rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <span className="ml-3 text-blue-600 font-semibold text-sm">SRNR IT SOLUTIONS</span>
            </div>
            <div className="ml-10 pl-10 border-l border-gray-200">
              <h1 className="text-xl font-medium text-gray-900">Salary Stream</h1>
            </div>
          </div>

          {/* Search and User Profile */}
          <div className="flex items-center">
            {/* Search */}
            <div className="relative mx-4">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Quick Search..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            {/* Notifications */}
            <button className="p-2 text-gray-400 hover:text-gray-600 relative">
              <Bell className="h-5 w-5" />
            </button>

            {/* User Profile */}
            <div className="ml-4 flex items-center">
              <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">B</span>
              </div>
              <div className="ml-2">
                <p className="text-sm font-medium text-gray-900">balu</p>
                <p className="text-xs text-gray-500">@domain.in</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
