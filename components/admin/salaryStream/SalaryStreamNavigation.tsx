"use client"

import { ViewType } from "./SalaryStreamDashboard"

interface SalaryStreamNavigationProps {
  activeView: ViewType
  onViewChange: (view: ViewType) => void
}

const navigationItems = [
  { id: 'dashboard' as ViewType, label: 'Dashboard' },
  { id: 'payroll' as ViewType, label: 'Payroll' },
  { id: 'employee' as ViewType, label: 'Employee' },
  { id: 'tax' as ViewType, label: 'Tax' },
  { id: 'claims' as ViewType, label: 'Claims' },
  { id: 'loans' as ViewType, label: 'Loans' },
  { id: 'support' as ViewType, label: 'Support' },
]

export function SalaryStreamNavigation({ activeView, onViewChange }: SalaryStreamNavigationProps) {
  return (
    <nav className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex overflow-x-auto">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onViewChange(item.id)}
            className={`
              flex-shrink-0 px-6 py-3 text-sm font-medium border-b-2 transition-colors
              ${activeView === item.id
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            {item.label}
          </button>
        ))}
      </div>
    </nav>
  )
}
