"use client"

import { Check<PERSON>ir<PERSON>, AlertTriangle, Clock } from "lucide-react"

interface ComplianceItem {
  name: string
  status: "compliant" | "review" | "pending"
}

export function ComplianceStatus() {
  const complianceItems: ComplianceItem[] = [
    { name: "Tax Filings", status: "compliant" },
    { name: "Labor Laws", status: "compliant" },
    { name: "Minimum Wage", status: "review" }
  ]

  const getStatusIcon = (status: ComplianceItem["status"]) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "review":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case "pending":
        return <Clock className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (status: ComplianceItem["status"]) => {
    switch (status) {
      case "compliant":
        return "Up to date"
      case "review":
        return "Review needed"
      case "pending":
        return "Action required"
    }
  }

  const getStatusColor = (status: ComplianceItem["status"]) => {
    switch (status) {
      case "compliant":
        return "text-green-600"
      case "review":
        return "text-orange-600"
      case "pending":
        return "text-red-600"
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Compliance Status</h3>
      
      <div className="space-y-4">
        {complianceItems.map((item) => (
          <div key={item.name} className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">{item.name}</span>
            <div className="flex items-center">
              {getStatusIcon(item.status)}
              <span className={`ml-2 text-sm font-medium ${getStatusColor(item.status)}`}>
                {getStatusText(item.status)}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
