"use client"

import { useState } from "react"
import { Download } from "lucide-react"
import { SubmitDeclarationModal } from "../modals/SubmitDeclarationModal"

export function EmployeePortal() {
  const [submitDeclarationOpen, setSubmitDeclarationOpen] = useState(false)

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Employee Self-Service Portal</h3>
            <p className="mt-1 text-sm text-gray-500">Access payslips, tax forms, and manage your information</p>
          </div>
          <button
            onClick={() => setSubmitDeclarationOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
          >
            <Download className="h-4 w-4 mr-2" />
            Submit Declaration
          </button>
        </div>

        <div className="text-center py-12">
          <p className="text-gray-500">Employee portal content will be displayed here.</p>
          <p className="text-sm text-gray-400 mt-2">
            This includes payslip access, tax forms, and personal information management.
          </p>
        </div>
      </div>

      <SubmitDeclarationModal 
        open={submitDeclarationOpen} 
        onClose={() => setSubmitDeclarationOpen(false)} 
      />
    </>
  )
}
