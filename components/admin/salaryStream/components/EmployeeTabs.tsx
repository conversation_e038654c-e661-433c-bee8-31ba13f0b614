"use client"

interface EmployeeTabsProps {
  activeTab: 'payslips' | 'tax-forms' | 'ytd-earnings' | 'tax-declaration' | 'profile'
  onTabChange: (tab: 'payslips' | 'tax-forms' | 'ytd-earnings' | 'tax-declaration' | 'profile') => void
}

export function EmployeeTabs({ activeTab, onTabChange }: EmployeeTabsProps) {
  const tabs = [
    { id: 'payslips' as const, label: 'Payslips' },
    { id: 'tax-forms' as const, label: 'Tax Forms' },
    { id: 'ytd-earnings' as const, label: 'YTD Earnings' },
    { id: 'tax-declaration' as const, label: 'Tax Declaration' },
    { id: 'profile' as const, label: 'Profile' }
  ]

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              ${activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  )
}
