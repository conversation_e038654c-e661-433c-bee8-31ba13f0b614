"use client"

import { Users, DollarSign, Calculator, CreditCard } from "lucide-react"

export function PayrollSummary() {
  const summaryData = [
    {
      title: "Total Employees",
      value: "1,247",
      subtitle: "+3 new hires",
      icon: Users,
      color: "blue"
    },
    {
      title: "Gross Payroll",
      value: "$3,245,800",
      subtitle: "Before deductions",
      icon: DollarSign,
      color: "green"
    },
    {
      title: "Total Deductions",
      value: "$398,300",
      subtitle: "Taxes & benefits",
      icon: Calculator,
      color: "orange"
    },
    {
      title: "Net Payroll",
      value: "$2,847,500",
      subtitle: "Ready for payment",
      icon: CreditCard,
      color: "purple"
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue":
        return "bg-blue-50 text-blue-600"
      case "green":
        return "bg-green-50 text-green-600"
      case "orange":
        return "bg-orange-50 text-orange-600"
      case "purple":
        return "bg-purple-50 text-purple-600"
      default:
        return "bg-gray-50 text-gray-600"
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {summaryData.map((item) => (
        <div key={item.title} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{item.title}</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{item.value}</p>
              <p className="text-sm text-gray-500 mt-1">{item.subtitle}</p>
            </div>
            <div className={`p-3 rounded-lg ${getColorClasses(item.color)}`}>
              <item.icon className="h-6 w-6" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
