"use client"

interface PayrollTabsProps {
  activeTab: 'current' | 'history' | 'structures' | 'compliance'
  onTabChange: (tab: 'current' | 'history' | 'structures' | 'compliance') => void
}

export function PayrollTabs({ activeTab, onTabChange }: PayrollTabsProps) {
  const tabs = [
    { id: 'current' as const, label: 'Current Payroll' },
    { id: 'history' as const, label: 'Payroll History' },
    { id: 'structures' as const, label: 'Salary Structures' },
    { id: 'compliance' as const, label: 'Compliance' }
  ]

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              ${activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  )
}
