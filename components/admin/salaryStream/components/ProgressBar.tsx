"use client"

import { motion } from "framer-motion"

interface ProgressBarProps {
  progress: number
  color?: string
}

export function ProgressBar({ progress, color = "blue" }: ProgressBarProps) {
  const getColorClass = () => {
    switch (color) {
      case "green":
        return "bg-green-500"
      case "orange":
        return "bg-orange-500"
      case "red":
        return "bg-red-500"
      default:
        return "bg-blue-500"
    }
  }

  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <motion.div
        className={`h-2 rounded-full ${getColorClass()}`}
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ duration: 1, ease: "easeOut" }}
      />
    </div>
  )
}
