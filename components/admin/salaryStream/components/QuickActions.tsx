"use client"

import { useState } from "react"
import { Play, FileText, UserPlus } from "lucide-react"
import { RunPayrollModal } from "../modals/RunPayrollModal"
import { GenerateReportsModal } from "../modals/GenerateReportsModal"
import { AddEmployeeModal } from "../modals/AddEmployeeModal"

export function QuickActions() {
  const [runPayrollOpen, setRunPayrollOpen] = useState(false)
  const [generateReportsOpen, setGenerateReportsOpen] = useState(false)
  const [addEmployeeOpen, setAddEmployeeOpen] = useState(false)

  const actions = [
    {
      title: "Run Payroll",
      icon: Play,
      onClick: () => setRunPayrollOpen(true),
      color: "bg-blue-500 hover:bg-blue-600"
    },
    {
      title: "Generate Reports",
      icon: FileText,
      onClick: () => setGenerateReportsOpen(true),
      color: "bg-green-500 hover:bg-green-600"
    },
    {
      title: "Add Employee",
      icon: UserPlus,
      onClick: () => setAddEmployeeOpen(true),
      color: "bg-purple-500 hover:bg-purple-600"
    }
  ]

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
        
        <div className="space-y-3">
          {actions.map((action) => (
            <button
              key={action.title}
              onClick={action.onClick}
              className={`w-full flex items-center justify-center px-4 py-3 text-white font-medium rounded-lg transition-colors ${action.color}`}
            >
              <action.icon className="h-5 w-5 mr-2" />
              {action.title}
            </button>
          ))}
        </div>
      </div>

      {/* Modals */}
      <RunPayrollModal open={runPayrollOpen} onClose={() => setRunPayrollOpen(false)} />
      <GenerateReportsModal open={generateReportsOpen} onClose={() => setGenerateReportsOpen(false)} />
      <AddEmployeeModal open={addEmployeeOpen} onClose={() => setAddEmployeeOpen(false)} />
    </>
  )
}
