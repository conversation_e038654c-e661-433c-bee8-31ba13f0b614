"use client"

import { motion } from "framer-motion"
import { Circle } from "lucide-react"

interface Activity {
  id: number
  title: string
  time: string
}

export function RecentActivities() {
  const activities: Activity[] = [
    {
      id: 1,
      title: "Payroll processed for December 2024",
      time: "2 hours ago"
    },
    {
      id: 2,
      title: "Tax filing submitted for Q4",
      time: "1 day ago"
    },
    {
      id: 3,
      title: "15 reimbursement claims approved",
      time: "3 days ago"
    },
    {
      id: 4,
      title: "Salary structure updated for Engineering",
      time: "3 days ago"
    },
    {
      id: 5,
      title: "New employee onboarded - <PERSON>",
      time: "5 days ago"
    }
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Recent Activities</h3>
      <p className="text-sm text-gray-600 mb-6">Latest system activities and changes</p>
      
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-start"
          >
            <div className="flex-shrink-0 mt-1">
              <Circle className="h-3 w-3 text-blue-500 fill-current" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{activity.title}</p>
              <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
