"use client"

import { useState } from "react"
import { Edit, Eye, Plus } from "lucide-react"
import { AddStructureModal } from "../modals/AddStructureModal"

interface SalaryStructure {
  id: string
  title: string
  type: "Salaried" | "Commission"
  basicPay: number
  allowances: number
  bonuses: number
  employees: number
  minWage: string
}

export function SalaryStructuresGrid() {
  const [addStructureOpen, setAddStructureOpen] = useState(false)

  const structures: SalaryStructure[] = [
    {
      id: "struct-1",
      title: "Engineering - Senior",
      type: "Salaried",
      basicPay: 70,
      allowances: 20,
      bonuses: 10,
      employees: 45,
      minWage: "$15/hr"
    },
    {
      id: "struct-2",
      title: "Marketing - Manager",
      type: "Salaried",
      basicPay: 65,
      allowances: 25,
      bonuses: 10,
      employees: 12,
      minWage: "$15/hr"
    },
    {
      id: "struct-3",
      title: "Sales - Commission",
      type: "Commission",
      basicPay: 50,
      allowances: 15,
      bonuses: 35,
      employees: 28,
      minWage: "$15/hr"
    }
  ]

  return (
    <>
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Salary Structures</h3>
            <p className="mt-1 text-sm text-gray-500">Manage job structures and components</p>
          </div>
          <button
            onClick={() => setAddStructureOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add structure
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {structures.map((structure) => (
            <div key={structure.id} className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">{structure.title}</h3>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    USD
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-500">{structure.type}</p>
              </div>
              <div className="px-4 py-5 sm:p-6">
                <dl className="grid grid-cols-2 gap-x-4 gap-y-6">
                  <div className="col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Basic Pay</dt>
                    <dd className="mt-1 text-sm text-gray-900">{structure.basicPay}%</dd>
                  </div>
                  <div className="col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Allowances</dt>
                    <dd className="mt-1 text-sm text-gray-900">{structure.allowances}%</dd>
                  </div>
                  <div className="col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Bonuses</dt>
                    <dd className="mt-1 text-sm text-gray-900">{structure.bonuses}%</dd>
                  </div>
                  <div className="col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Employees</dt>
                    <dd className="mt-1 text-sm text-gray-900">{structure.employees}</dd>
                  </div>
                  <div className="col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Min Wage</dt>
                    <dd className="mt-1 text-sm text-gray-900">{structure.minWage}</dd>
                  </div>
                </dl>
              </div>
              <div className="px-4 py-4 sm:px-6 bg-gray-50 border-t border-gray-200">
                <div className="flex justify-between">
                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <AddStructureModal open={addStructureOpen} onClose={() => setAddStructureOpen(false)} />
    </>
  )
}
