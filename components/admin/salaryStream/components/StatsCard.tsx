"use client"

import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react"

interface StatsCardProps {
  title: string
  value: string
  change: string
  trend: "up" | "down"
  icon: LucideIcon
  color: "green" | "blue" | "orange" | "red"
}

const colorClasses = {
  green: {
    bg: "bg-green-50",
    icon: "text-green-600",
    trend: "text-green-600"
  },
  blue: {
    bg: "bg-blue-50",
    icon: "text-blue-600",
    trend: "text-blue-600"
  },
  orange: {
    bg: "bg-orange-50",
    icon: "text-orange-600",
    trend: "text-orange-600"
  },
  red: {
    bg: "bg-red-50",
    icon: "text-red-600",
    trend: "text-red-600"
  }
}

export function StatsCard({ title, value, change, trend, icon: Icon, color }: StatsCardProps) {
  const colors = colorClasses[color]
  const TrendIcon = trend === "up" ? TrendingUp : TrendingDown
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        </div>
        <div className={`p-3 rounded-lg ${colors.bg}`}>
          <Icon className={`h-6 w-6 ${colors.icon}`} />
        </div>
      </div>
      
      <div className="mt-4 flex items-center">
        <TrendIcon className={`h-4 w-4 ${colors.trend} mr-1`} />
        <span className={`text-sm font-medium ${colors.trend}`}>
          {change}
        </span>
      </div>
    </div>
  )
}
