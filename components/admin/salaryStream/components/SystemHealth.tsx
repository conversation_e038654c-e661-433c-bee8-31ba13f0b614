"use client"

import { Database, CreditCard, HardDrive } from "lucide-react"

interface SystemComponent {
  name: string
  status: "healthy" | "warning" | "error" | "connected" | "current"
  icon: React.ElementType
}

export function SystemHealth() {
  const components: SystemComponent[] = [
    { name: "Database", status: "healthy", icon: Database },
    { name: "Payment Gateway", status: "connected", icon: CreditCard },
    { name: "Backup Status", status: "current", icon: HardDrive }
  ]

  const getStatusColor = (status: SystemComponent["status"]) => {
    switch (status) {
      case "healthy":
      case "connected":
      case "current":
        return "text-green-600"
      case "warning":
        return "text-orange-600"
      case "error":
        return "text-red-600"
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">System Health</h3>
      
      <div className="space-y-4">
        {components.map((component) => (
          <div key={component.name} className="flex items-center justify-between">
            <div className="flex items-center">
              <component.icon className="h-5 w-5 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700">{component.name}</span>
            </div>
            <span className={`text-sm font-medium ${getStatusColor(component.status)}`}>
              {component.status.charAt(0).toUpperCase() + component.status.slice(1)}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}
