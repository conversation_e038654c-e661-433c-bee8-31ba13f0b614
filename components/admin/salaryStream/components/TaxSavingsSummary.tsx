"use client"

export function TaxSavingsSummary() {
  const summaryData = [
    { label: "Total Declared Amount", value: "$14,860", color: "text-gray-900" },
    { label: "Maximum Allowed", value: "$22,500", color: "text-gray-600" },
    { label: "Remaining Limit", value: "$7,640", color: "text-blue-600" },
    { label: "Estimated Tax Savings", value: "$3,566", color: "text-green-600" },
    { label: "Tax Rate", value: "24%", color: "text-gray-600" },
    { label: "Potential Additional Savings", value: "$1,834", color: "text-blue-600" }
  ]

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-2">Tax Savings Summary</h3>
      <p className="text-sm text-gray-500 mb-6">Your potential tax savings for the current year</p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {summaryData.map((item, index) => (
          <div key={index} className="text-center">
            <dt className="text-sm font-medium text-gray-500">{item.label}</dt>
            <dd className={`mt-1 text-2xl font-semibold ${item.color}`}>{item.value}</dd>
          </div>
        ))}
      </div>
    </div>
  )
}
