"use client"

import { motion } from "framer-motion"
import { 
  DollarSign, 
  Users, 
  Clock, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  UserPlus
} from "lucide-react"
import { StatsCard } from "../components/StatsCard"
import { ProgressBar } from "../components/ProgressBar"
import { RecentActivities } from "../components/RecentActivities"
import { QuickActions } from "../components/QuickActions"
import { ComplianceStatus } from "../components/ComplianceStatus"
import { SystemHealth } from "../components/SystemHealth"

export function DashboardView() {
  const stats: {
    title: string;
    value: string;
    change: string;
    trend: "up" | "down";
    icon: typeof DollarSign;
    color: "green" | "blue" | "orange" | "red";
  }[] = [
    {
      title: "Total Payroll",
      value: "$2,847,500",
      change: "+12.5% from last month",
      trend: "up",
      icon: DollarSign,
      color: "green"
    },
    {
      title: "Active Employees",
      value: "1,247",
      change: "+3.2% from last month",
      trend: "up",
      icon: Users,
      color: "blue"
    },
    {
      title: "Pending Approvals",
      value: "23",
      change: "-15% from last month",
      trend: "down",
      icon: Clock,
      color: "orange"
    },
    {
      title: "Compliance Issues",
      value: "2",
      change: "-50% from last month",
      trend: "down",
      icon: AlertTriangle,
      color: "red"
    }
  ]

  const processingStatus = [
    { name: "Salary Calculations", progress: 100 },
    { name: "Tax Calculations", progress: 95 },
    { name: "Payment Processing", progress: 78 },
    { name: "Payslip Generation", progress: 85 }
  ]

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <StatsCard {...stat} />
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payroll Processing Status */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Payroll Processing Status</h3>
          <p className="text-sm text-gray-600 mb-6">Current month progress</p>
          
          <div className="space-y-4">
            {processingStatus.map((item) => (
              <div key={item.name}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-700">{item.name}</span>
                  <span className="text-gray-500">{item.progress}%</span>
                </div>
                <ProgressBar progress={item.progress} />
              </div>
            ))}
          </div>
        </motion.div>

        {/* Recent Activities */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <RecentActivities />
        </motion.div>
      </div>

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <QuickActions />
        </motion.div>

        {/* Compliance Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <ComplianceStatus />
        </motion.div>

        {/* System Health */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <SystemHealth />
        </motion.div>
      </div>
    </div>
  )
}
