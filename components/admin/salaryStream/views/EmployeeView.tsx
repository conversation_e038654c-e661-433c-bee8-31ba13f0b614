"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Download, Shield } from "lucide-react"
import { EmployeeTabs } from "../components/EmployeeTabs"
import { EmployeePortal } from "../components/EmployeePortal"
import { TaxDeclarationHistory } from "../components/TaxDeclarationHistory"
import { TaxSavingsSummary } from "../components/TaxSavingsSummary"
import { PersonalInformation } from "../components/PersonalInformation"

type EmployeeTab = 'payslips' | 'tax-forms' | 'ytd-earnings' | 'tax-declaration' | 'profile'

export function EmployeeView() {
  const [activeTab, setActiveTab] = useState<EmployeeTab>('payslips')

  const renderTabContent = () => {
    switch (activeTab) {
      case 'payslips':
        return <EmployeePortal />
      case 'tax-forms':
        return <div className="text-center py-12 text-gray-500">Tax Forms content coming soon...</div>
      case 'ytd-earnings':
        return <div className="text-center py-12 text-gray-500">YTD Earnings content coming soon...</div>
      case 'tax-declaration':
        return (
          <>
            <TaxDeclarationHistory />
            <div className="mt-8">
              <TaxSavingsSummary />
            </div>
          </>
        )
      case 'profile':
        return <PersonalInformation />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Employee Self-Service Portal</h2>
          <p className="text-sm text-gray-600 mt-1">
            Access payslips, tax forms, and manage your information
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-700 mr-2">John Doe (EMP001)</span>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
            <Shield className="h-4 w-4 mr-2" />
            Security
          </button>
        </div>
      </div>

      {/* Tabs */}
      <EmployeeTabs activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderTabContent()}
      </motion.div>
    </div>
  )
}
