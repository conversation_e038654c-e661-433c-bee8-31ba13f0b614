"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Download, Play, Eye, Download as DownloadIcon } from "lucide-react"
import { PayrollTabs } from "../components/PayrollTabs"
import { PayrollSummary } from "../components/PayrollSummary"
import { EmployeePayrollTable } from "../components/EmployeePayrollTable"
import { PayrollHistoryTable } from "../components/PayrollHistoryTable"
import { SalaryStructuresGrid } from "../components/SalaryStructuresGrid"
import { ComplianceChecklist } from "../components/ComplianceChecklist"

type PayrollTab = 'current' | 'history' | 'structures' | 'compliance'

export function PayrollView() {
  const [activeTab, setActiveTab] = useState<PayrollTab>('current')

  const renderTabContent = () => {
    switch (activeTab) {
      case 'current':
        return (
          <>
            <PayrollSummary />
            <div className="mt-8">
              <EmployeePayrollTable />
            </div>
          </>
        )
      case 'history':
        return <PayrollHistoryTable />
      case 'structures':
        return <SalaryStructuresGrid />
      case 'compliance':
        return <ComplianceChecklist />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Payroll Processing</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage salary calculations, payments, and compliance Export/Run Payroll
          </p>
        </div>
        <div className="flex space-x-3 mt-4 sm:mt-0">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none">
            <Play className="h-4 w-4 mr-2" />
            Run Payroll
          </button>
        </div>
      </div>

      {/* Tabs */}
      <PayrollTabs activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderTabContent()}
      </motion.div>
    </div>
  )
}
