/**
 * Tab Navigation Example Component
 * Shows how to use the shared TabNavigation component consistently across admin modules
 */

import React, { useState } from 'react'
import { TabNavigation, Card, CardHeader, CardContent } from '../ui'
import { 
  LayoutDashboard, 
  Users, 
  FileText, 
  Settings,
  BarChart3,
  Calendar,
  Bell,
  Package
} from 'lucide-react'

// ============================================================================
// EXAMPLE COMPONENTS
// ============================================================================

/**
 * Example showing different tab navigation variants
 */
export const TabNavigationExample: React.FC = () => {
  const [activeTab1, setActiveTab1] = useState('dashboard')
  const [activeTab2, setActiveTab2] = useState('overview')
  const [activeTab3, setActiveTab3] = useState('users')

  // Example tabs for different modules
  const dashboardTabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-4 h-4" />,
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <BarChart3 className="w-4 h-4" />,
      badge: 3
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <FileText className="w-4 h-4" />,
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-4 h-4" />,
    }
  ]

  const hrTabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <LayoutDashboard className="w-4 h-4" />,
    },
    {
      id: 'employees',
      label: 'Employees',
      icon: <Users className="w-4 h-4" />,
      badge: 12
    },
    {
      id: 'onboarding',
      label: 'Onboarding',
      icon: <Package className="w-4 h-4" />,
      badge: 5,
      badgeColor: 'warning' as const
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: <Calendar className="w-4 h-4" />,
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <Bell className="w-4 h-4" />,
      badge: 8,
      badgeColor: 'error' as const
    }
  ]

  const simpleTabs = [
    { id: 'users', label: 'Users' },
    { id: 'roles', label: 'Roles' },
    { id: 'permissions', label: 'Permissions' },
    { id: 'audit', label: 'Audit Log' }
  ]

  return (
    <div className="space-y-8 p-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Tab Navigation Examples</h1>
        <p className="text-gray-600">Consistent tab navigation across all admin modules</p>
      </div>

      {/* Default Variant */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Default Variant (Pills)</h2>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Dashboard Module</h3>
          </CardHeader>
          <CardContent>
            <TabNavigation
              tabs={dashboardTabs}
              activeTab={activeTab1}
              onTabChange={setActiveTab1}
              variant="default"
              size="md"
            />
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600">
                Current tab: <span className="font-semibold">{activeTab1}</span>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                This is the content for the {activeTab1} tab.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Pills Variant */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Pills Variant</h2>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">HR Management Module</h3>
          </CardHeader>
          <CardContent>
            <TabNavigation
              tabs={hrTabs}
              activeTab={activeTab2}
              onTabChange={setActiveTab2}
              variant="pills"
              size="md"
              scrollable={true}
            />
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600">
                Current tab: <span className="font-semibold">{activeTab2}</span>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                This variant uses a pills style with badges for notifications.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Underline Variant */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Underline Variant</h2>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">User Management Module</h3>
          </CardHeader>
          <CardContent>
            <TabNavigation
              tabs={simpleTabs}
              activeTab={activeTab3}
              onTabChange={setActiveTab3}
              variant="underline"
              size="md"
              fullWidth={true}
            />
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600">
                Current tab: <span className="font-semibold">{activeTab3}</span>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                This variant uses underlines and is full width.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Different Sizes */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Different Sizes</h2>
        <div className="space-y-4">
          {/* Small */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Small Size</h3>
            </CardHeader>
            <CardContent>
              <TabNavigation
                tabs={simpleTabs.slice(0, 3)}
                activeTab={activeTab3}
                onTabChange={setActiveTab3}
                variant="pills"
                size="sm"
              />
            </CardContent>
          </Card>

          {/* Large */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Large Size</h3>
            </CardHeader>
            <CardContent>
              <TabNavigation
                tabs={simpleTabs.slice(0, 3)}
                activeTab={activeTab3}
                onTabChange={setActiveTab3}
                variant="pills"
                size="lg"
              />
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Loading State */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Loading State</h2>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Loading Example</h3>
          </CardHeader>
          <CardContent>
            <TabNavigation
              tabs={dashboardTabs}
              activeTab={activeTab1}
              onTabChange={setActiveTab1}
              variant="default"
              size="md"
              loading={true}
            />
          </CardContent>
        </Card>
      </section>

      {/* Usage Instructions */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Instructions</h2>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">How to Use in Your Admin Components</h3>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <h4>1. Import the TabNavigation component:</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm">
{`import { TabNavigation } from '@/components/admin/shared/ui'`}
              </pre>

              <h4>2. Define your tabs:</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm">
{`const tabs = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <LayoutDashboard className="w-4 h-4" />,
  },
  {
    id: 'users',
    label: 'Users',
    icon: <Users className="w-4 h-4" />,
    badge: 5,
    badgeColor: 'warning'
  }
]`}
              </pre>

              <h4>3. Use the component:</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm">
{`<TabNavigation
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={setActiveTab}
  variant="default"
  size="md"
  scrollable={true}
/>`}
              </pre>

              <h4>Available Props:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><strong>variant:</strong> 'default' | 'pills' | 'underline'</li>
                <li><strong>size:</strong> 'sm' | 'md' | 'lg'</li>
                <li><strong>fullWidth:</strong> boolean - makes tabs take full width</li>
                <li><strong>scrollable:</strong> boolean - enables horizontal scrolling</li>
                <li><strong>loading:</strong> boolean - shows loading skeleton</li>
              </ul>

              <h4>Tab Item Properties:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><strong>id:</strong> unique identifier</li>
                <li><strong>label:</strong> display text</li>
                <li><strong>icon:</strong> optional React node</li>
                <li><strong>badge:</strong> optional number for notifications</li>
                <li><strong>badgeColor:</strong> 'default' | 'success' | 'warning' | 'error'</li>
                <li><strong>disabled:</strong> boolean</li>
                <li><strong>hidden:</strong> boolean</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}

export default TabNavigationExample
