/**
 * UI Showcase Component
 * Demonstrates all shared UI components with consistent styling
 */

import React from 'react'
import { <PERSON>, Card<PERSON>eader, Card<PERSON>ontent, Card<PERSON>ooter, <PERSON><PERSON>, StatsCard, StatsGrid } from '../ui'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Download,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * UI Showcase component demonstrating all shared UI components
 */
export const UIShowcase: React.FC = () => {
  return (
    <div className="space-y-8 p-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin UI Components Showcase</h1>
        <p className="text-gray-600">Consistent styling across all admin components</p>
      </div>

      {/* Stats Cards Section */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Statistics Cards</h2>
        <StatsGrid columns={4} gap="lg">
          <StatsCard
            title="Total Users"
            value={1247}
            subtitle="Active users in system"
            icon={<Users />}
            variant="info"
            trend={{
              value: 12.5,
              label: "from last month",
              direction: "up"
            }}
          />
          
          <StatsCard
            title="Revenue"
            value="$89,240"
            subtitle="Monthly revenue"
            icon={<DollarSign />}
            variant="success"
            trend={{
              value: 8.2,
              label: "from last month",
              direction: "up"
            }}
          />
          
          <StatsCard
            title="Growth Rate"
            value="15.3%"
            subtitle="Year over year"
            icon={<TrendingUp />}
            variant="warning"
            trend={{
              value: -2.1,
              label: "from last quarter",
              direction: "down"
            }}
          />
          
          <StatsCard
            title="Events"
            value={42}
            subtitle="Scheduled this month"
            icon={<Calendar />}
            variant="default"
            trend={{
              value: 5.7,
              label: "from last month",
              direction: "up"
            }}
          />
        </StatsGrid>
      </section>

      {/* Cards Section */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Card Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Default Card */}
          <Card variant="default">
            <CardHeader>
              <h3 className="text-lg font-semibold">Default Card</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">This is a default card with subtle shadow and hover effects.</p>
            </CardContent>
            <CardFooter>
              <Button variant="primary" size="sm">Action</Button>
            </CardFooter>
          </Card>

          {/* Elevated Card */}
          <Card variant="elevated">
            <CardHeader>
              <h3 className="text-lg font-semibold">Elevated Card</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">This card has more prominent shadow and elevation.</p>
            </CardContent>
            <CardFooter>
              <Button variant="secondary" size="sm">Action</Button>
            </CardFooter>
          </Card>

          {/* Bordered Card */}
          <Card variant="bordered">
            <CardHeader>
              <h3 className="text-lg font-semibold">Bordered Card</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">This card emphasizes borders over shadows.</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm">Action</Button>
            </CardFooter>
          </Card>

          {/* Ghost Card */}
          <Card variant="ghost">
            <CardHeader>
              <h3 className="text-lg font-semibold">Ghost Card</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">This card has minimal styling for subtle content.</p>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" size="sm">Action</Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Buttons Section */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Button Variants</h2>
        <div className="space-y-4">
          {/* Primary Buttons */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Primary Buttons</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary" size="xs">Extra Small</Button>
              <Button variant="primary" size="sm">Small</Button>
              <Button variant="primary" size="md">Medium</Button>
              <Button variant="primary" size="lg">Large</Button>
              <Button variant="primary" size="xl">Extra Large</Button>
            </div>
          </div>

          {/* Button Variants */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Button Variants</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="success">Success</Button>
              <Button variant="warning">Warning</Button>
            </div>
          </div>

          {/* Buttons with Icons */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Buttons with Icons</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary" icon={<Plus />}>Add New</Button>
              <Button variant="outline" icon={<Download />}>Download</Button>
              <Button variant="secondary" icon={<Edit />} iconPosition="right">Edit</Button>
              <Button variant="destructive" icon={<Trash2 />}>Delete</Button>
            </div>
          </div>

          {/* Loading States */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading States</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="primary" loading>Loading...</Button>
              <Button variant="outline" loading>Processing</Button>
              <Button variant="secondary" loading>Saving</Button>
            </div>
          </div>

          {/* Full Width */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Full Width</h3>
            <Button variant="primary" fullWidth>Full Width Button</Button>
          </div>
        </div>
      </section>

      {/* Interactive Cards */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Interactive Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card 
            variant="default" 
            clickable 
            onClick={() => alert('Card clicked!')}
          >
            <CardContent>
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Clickable Card</h3>
                <p className="text-gray-600">Click me to see the interaction!</p>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Analytics</h3>
                <p className="text-gray-600">View detailed analytics and reports</p>
              </div>
            </CardContent>
          </Card>

          <Card variant="bordered">
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Schedule</h3>
                <p className="text-gray-600">Manage your schedule and events</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Loading States */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Loading States</h2>
        <StatsGrid columns={4} loading skeletonCount={4} />
      </section>
    </div>
  )
}

export default UIShowcase
