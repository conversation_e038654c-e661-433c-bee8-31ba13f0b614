/**
 * Shared Button Component for Admin Pages
 * Consistent button styling across all admin components
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Button content */
  children?: React.ReactNode
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'success' | 'warning'
  /** Button size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** Icon to display */
  icon?: React.ReactNode
  /** Icon position */
  iconPosition?: 'left' | 'right'
  /** Loading state */
  loading?: boolean
  /** Full width button */
  fullWidth?: boolean
  /** Whether button should be rounded */
  rounded?: boolean
  /** Custom loading spinner */
  loadingSpinner?: React.ReactNode
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Shared Button component with consistent styling
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  fullWidth = false,
  rounded = false,
  loadingSpinner,
  type = 'button',
  ...props
}) => {
  const buttonClasses = cn(
    // Base styles
    'relative inline-flex items-center justify-center font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    'transform hover:scale-105 active:scale-95',
    
    // Variant styles
    {
      // Primary
      'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md': 
        variant === 'primary',
      
      // Secondary
      'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-sm hover:shadow-md': 
        variant === 'secondary',
      
      // Outline
      'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500 shadow-sm hover:shadow-md': 
        variant === 'outline',
      
      // Ghost
      'text-gray-700 hover:bg-gray-100 focus:ring-gray-500': 
        variant === 'ghost',
      
      // Destructive
      'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md': 
        variant === 'destructive',
      
      // Success
      'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow-md': 
        variant === 'success',
      
      // Warning
      'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm hover:shadow-md': 
        variant === 'warning',
    },
    
    // Size styles
    {
      'px-2 py-1 text-xs': size === 'xs',
      'px-3 py-1.5 text-sm': size === 'sm',
      'px-4 py-2 text-sm': size === 'md',
      'px-6 py-3 text-base': size === 'lg',
      'px-8 py-4 text-lg': size === 'xl',
    },
    
    // Shape styles
    {
      'rounded-md': !rounded,
      'rounded-full': rounded,
    },
    
    // Width styles
    {
      'w-full': fullWidth,
    },
    
    className
  )

  const iconClasses = cn(
    'flex-shrink-0',
    {
      'w-3 h-3': size === 'xs',
      'w-4 h-4': size === 'sm' || size === 'md',
      'w-5 h-5': size === 'lg',
      'w-6 h-6': size === 'xl',
    }
  )

  const renderIcon = (iconElement: React.ReactNode) => {
    if (!iconElement) return null
    
    return React.cloneElement(iconElement as React.ReactElement, {
      className: iconClasses
    })
  }

  const renderContent = () => {
    if (loading) {
      return (
        <>
          {loadingSpinner || <Loader2 className={cn(iconClasses, 'animate-spin')} />}
          {children && <span className="ml-2">Loading...</span>}
        </>
      )
    }

    return (
      <>
        {icon && iconPosition === 'left' && (
          <span className={cn(children && 'mr-2')}>
            {renderIcon(icon)}
          </span>
        )}
        {children}
        {icon && iconPosition === 'right' && (
          <span className={cn(children && 'ml-2')}>
            {renderIcon(icon)}
          </span>
        )}
      </>
    )
  }

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      {...props}
    >
      {renderContent()}
    </button>
  )
}

export default Button
