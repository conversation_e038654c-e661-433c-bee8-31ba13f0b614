/**
 * Shared Card Component for Admin Pages
 * Consistent card styling across all admin components
 */

import React from 'react'
import { cn } from '@/lib/utils'

// ============================================================================
// TYPES
// ============================================================================

export interface CardProps {
  /** Card content */
  children: React.ReactNode
  /** Additional CSS classes */
  className?: string
  /** Card variant */
  variant?: 'default' | 'elevated' | 'bordered' | 'ghost'
  /** Card size */
  size?: 'sm' | 'md' | 'lg' | 'xl'
  /** Whether card is clickable */
  clickable?: boolean
  /** Click handler */
  onClick?: () => void
  /** Loading state */
  loading?: boolean
  /** Test ID */
  'data-testid'?: string
}

export interface CardHeaderProps {
  children: React.ReactNode
  className?: string
}

export interface CardContentProps {
  children: React.ReactNode
  className?: string
}

export interface CardFooterProps {
  children: React.ReactNode
  className?: string
}

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Main Card component with consistent styling
 */
export const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  clickable = false,
  onClick,
  loading = false,
  'data-testid': testId,
}) => {
  const cardClasses = cn(
    // Base styles
    'bg-white rounded-lg border transition-all duration-200',
    
    // Variant styles
    {
      'border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1': variant === 'default',
      'border-gray-200 shadow-lg hover:shadow-xl hover:-translate-y-2': variant === 'elevated',
      'border-2 border-gray-300 shadow-none hover:border-gray-400': variant === 'bordered',
      'border-transparent shadow-none bg-gray-50 hover:bg-gray-100': variant === 'ghost',
    },
    
    // Size styles
    {
      'p-3': size === 'sm',
      'p-4': size === 'md',
      'p-6': size === 'lg',
      'p-8': size === 'xl',
    },
    
    // Interactive styles
    {
      'cursor-pointer transform hover:scale-105 active:scale-95': clickable,
      'animate-pulse': loading,
    },
    
    className
  )

  const Component = clickable ? 'button' : 'div'

  return (
    <Component
      className={cardClasses}
      onClick={onClick}
      data-testid={testId}
      disabled={loading}
    >
      {children}
    </Component>
  )
}

/**
 * Card Header component
 */
export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn('mb-4 pb-2 border-b border-gray-100', className)}>
      {children}
    </div>
  )
}

/**
 * Card Content component
 */
export const CardContent: React.FC<CardContentProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn('flex-1', className)}>
      {children}
    </div>
  )
}

/**
 * Card Footer component
 */
export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn('mt-4 pt-2 border-t border-gray-100', className)}>
      {children}
    </div>
  )
}

/**
 * Card Skeleton for loading states
 */
export const CardSkeleton: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Card loading className={className}>
      <div className="space-y-3">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-3/4 animate-pulse" />
        <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-1/2 animate-pulse" />
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-2/3 animate-pulse" />
      </div>
    </Card>
  )
}

// ============================================================================
// COMPOUND COMPONENT
// ============================================================================

Card.Header = CardHeader
Card.Content = CardContent
Card.Footer = CardFooter
Card.Skeleton = CardSkeleton

export default Card
