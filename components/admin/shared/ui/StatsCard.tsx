/**
 * Shared StatsCard Component for Admin Pages
 * Consistent statistics card styling across all admin components
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { TrendingUp, TrendingDown } from 'lucide-react'
import { Card } from './Card'

// ============================================================================
// TYPES
// ============================================================================

export interface StatsCardProps {
  /** Card title */
  title: string
  /** Main value to display */
  value: string | number
  /** Subtitle or description */
  subtitle?: string
  /** Icon to display */
  icon?: React.ReactNode
  /** Trend information */
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down'
  }
  /** Card variant */
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'
  /** Loading state */
  loading?: boolean
  /** Click handler */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test ID */
  'data-testid'?: string
}

export interface StatsGridProps {
  /** Grid columns configuration */
  columns?: 1 | 2 | 3 | 4 | 5 | 6
  /** Gap between cards */
  gap?: 'sm' | 'md' | 'lg'
  /** Loading state */
  loading?: boolean
  /** Number of skeleton cards to show when loading */
  skeletonCount?: number
  /** Child components */
  children?: React.ReactNode
  /** Additional CSS classes */
  className?: string
}

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Statistics Card component
 */
export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  variant = 'default',
  loading = false,
  onClick,
  className,
  'data-testid': testId,
}) => {
  if (loading) {
    return <StatsCardSkeleton className={className} />
  }

  const cardVariant = variant === 'default' ? 'default' : 'elevated'

  const iconClasses = cn(
    'w-5 h-5',
    {
      'text-gray-600': variant === 'default',
      'text-green-600': variant === 'success',
      'text-yellow-600': variant === 'warning',
      'text-red-600': variant === 'error',
      'text-blue-600': variant === 'info',
    }
  )

  const valueClasses = cn(
    'text-2xl font-bold mb-1',
    {
      'text-gray-900': variant === 'default',
      'text-green-700': variant === 'success',
      'text-yellow-700': variant === 'warning',
      'text-red-700': variant === 'error',
      'text-blue-700': variant === 'info',
    }
  )

  const trendClasses = cn(
    'inline-flex items-center gap-1 text-sm font-medium',
    {
      'text-green-600': trend?.direction === 'up',
      'text-red-600': trend?.direction === 'down',
    }
  )

  return (
    <Card
      variant={cardVariant}
      clickable={!!onClick}
      onClick={onClick}
      className={cn(
        {
          'border-green-200 bg-green-50': variant === 'success',
          'border-yellow-200 bg-yellow-50': variant === 'warning',
          'border-red-200 bg-red-50': variant === 'error',
          'border-blue-200 bg-blue-50': variant === 'info',
        },
        className
      )}
      data-testid={testId}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={valueClasses}>{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
          {trend && (
            <div className={trendClasses}>
              {trend.direction === 'up' ? (
                <TrendingUp className="w-4 h-4" />
              ) : (
                <TrendingDown className="w-4 h-4" />
              )}
              <span>{trend.value}% {trend.label}</span>
            </div>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            {React.cloneElement(icon as React.ReactElement, {
              className: iconClasses
            })}
          </div>
        )}
      </div>
    </Card>
  )
}

/**
 * Statistics Grid component
 */
export const StatsGrid: React.FC<StatsGridProps> = ({
  columns = 4,
  gap = 'md',
  loading = false,
  skeletonCount = 4,
  children,
  className,
}) => {
  const gridClasses = cn(
    'grid',
    {
      'grid-cols-1': columns === 1,
      'grid-cols-1 sm:grid-cols-2': columns === 2,
      'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': columns === 3,
      'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4': columns === 4,
      'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5': columns === 5,
      'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6': columns === 6,
    },
    {
      'gap-3': gap === 'sm',
      'gap-4': gap === 'md',
      'gap-6': gap === 'lg',
    },
    className
  )

  if (loading) {
    return (
      <div className={gridClasses}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <StatsCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}

/**
 * Statistics Card Skeleton
 */
export const StatsCardSkeleton: React.FC<{ className?: string }> = ({ 
  className 
}) => (
  <Card
    loading
    className={cn(
      'hover:shadow-lg hover:-translate-y-1 transition-all duration-200',
      className
    )}
  >
    <div className="flex items-start justify-between">
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24 animate-pulse" />
        <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-20 animate-pulse" />
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-32 animate-pulse" />
      </div>
      <div className="h-5 w-5 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded animate-pulse" />
    </div>
  </Card>
)

export default StatsCard
