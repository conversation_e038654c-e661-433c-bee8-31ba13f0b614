/**
 * Shared Tab Navigation Component for Admin Pages
 * Consistent tab navigation styling across all admin components
 */

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { ChevronLeft, ChevronRight } from 'lucide-react'

// ============================================================================
// TYPES
// ============================================================================

export interface TabItem {
  /** Unique tab identifier */
  id: string
  /** Tab label */
  label: string
  /** Tab icon */
  icon?: React.ReactNode
  /** Whether tab is disabled */
  disabled?: boolean
  /** Badge count */
  badge?: number
  /** Badge color */
  badgeColor?: 'default' | 'success' | 'warning' | 'error'
  /** Whether tab is hidden */
  hidden?: boolean
}

export interface TabNavigationProps {
  /** Array of tab items */
  tabs: TabItem[]
  /** Currently active tab ID */
  activeTab: string
  /** Tab change handler */
  onTabChange: (tabId: string) => void
  /** Tab size */
  size?: 'sm' | 'md' | 'lg'
  /** Tab variant */
  variant?: 'default' | 'pills' | 'underline'
  /** Whether tabs should be full width */
  fullWidth?: boolean
  /** Whether tabs should be scrollable */
  scrollable?: boolean
  /** Loading state */
  loading?: boolean
  /** Additional CSS classes */
  className?: string
  /** Test ID */
  'data-testid'?: string
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Shared Tab Navigation component with consistent styling
 */
export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  size = 'md',
  variant = 'default',
  fullWidth = false,
  scrollable = false,
  loading = false,
  className,
  'data-testid': testId,
}) => {
  const tabsRef = useRef<HTMLDivElement>(null)
  const [showScrollButtons, setShowScrollButtons] = useState(false)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  // Filter visible tabs
  const visibleTabs = tabs.filter(tab => !tab.hidden)

  // Check if scrolling is needed
  useEffect(() => {
    const checkScroll = () => {
      if (!tabsRef.current || !scrollable) return

      const { scrollLeft, scrollWidth, clientWidth } = tabsRef.current
      const needsScroll = scrollWidth > clientWidth

      setShowScrollButtons(needsScroll)
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
    }

    checkScroll()
    window.addEventListener('resize', checkScroll)
    return () => window.removeEventListener('resize', checkScroll)
  }, [visibleTabs, scrollable])

  // Scroll functions
  const scrollLeft = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, tabId: string) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      onTabChange(tabId)
    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      e.preventDefault()
      const currentIndex = visibleTabs.findIndex(tab => tab.id === tabId)
      const nextIndex = e.key === 'ArrowLeft' ? currentIndex - 1 : currentIndex + 1
      
      if (nextIndex >= 0 && nextIndex < visibleTabs.length) {
        const nextTab = visibleTabs[nextIndex]
        if (!nextTab.disabled) {
          onTabChange(nextTab.id)
        }
      }
    }
  }

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm'
      case 'lg':
        return 'px-6 py-3 text-base'
      default:
        return 'px-4 py-2 text-sm'
    }
  }

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'pills':
        return {
          container: 'bg-gray-100 p-1 rounded-lg',
          tab: 'rounded-md',
          active: 'bg-white text-gray-900 shadow-sm',
          inactive: 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }
      case 'underline':
        return {
          container: 'border-b border-gray-200',
          tab: 'border-b-2 border-transparent',
          active: 'border-red-500 text-blue-600',
          inactive: 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
        }
      default:
        return {
          container: 'bg-gray-50 rounded-lg p-1',
          tab: 'rounded-md',
          active: 'bg-white text-gray-900 shadow-md scale-105',
          inactive: 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }
    }
  }

  const styles = getVariantStyles()

  // Container classes
  const containerClasses = cn(
    'flex items-center',
    styles.container,
    fullWidth && 'flex-1',
    scrollable && 'overflow-x-auto scrollbar-hide',
    className
  )

  // Tab classes
  const tabClasses = (tab: TabItem, isActive: boolean) => cn(
    'flex items-center gap-2 font-medium transition-all duration-200',
    '',
    'whitespace-nowrap transform hover:scale-105 active:scale-95',
    getSizeStyles(),
    styles.tab,
    isActive ? cn(styles.active, 'scale-105 shadow-md') : styles.inactive,
    tab.disabled && 'opacity-50 cursor-not-allowed',
    loading && 'pointer-events-none',
    fullWidth && 'flex-1 justify-center'
  )

  // Badge component
  const Badge: React.FC<{ count?: number; color?: string }> = ({ count, color = 'default' }) => {
    if (!count || count <= 0) return null

    const badgeClasses = cn(
      'inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full',
      {
        'bg-gray-100 text-gray-800': color === 'default',
        'bg-green-100 text-green-800': color === 'success',
        'bg-yellow-100 text-yellow-800': color === 'warning',
        'bg-red-100 text-red-800': color === 'error',
      }
    )

    return (
      <span className={badgeClasses}>
        {count > 99 ? '99+' : count}
      </span>
    )
  }

  if (loading) {
    return (
      <div className={containerClasses} data-testid={testId}>
        <div className="flex space-x-1">
          {Array.from({ length: 4 }).map((_, index) => (
            <div
              key={index}
              className="h-8 bg-gray-200 rounded animate-pulse"
              style={{ width: `${80 + Math.random() * 40}px` }}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={containerClasses} data-testid={testId}>
      {/* Left scroll button */}
      {showScrollButtons && canScrollLeft && (
        <button
          type="button"
          onClick={scrollLeft}
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600  rounded-md mr-2"
          aria-label="Scroll tabs left"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
      )}

      {/* Tabs container */}
      <div
        ref={tabsRef}
        className={cn(
          'flex',
          fullWidth ? 'flex-1' : 'flex-shrink-0',
          scrollable && 'overflow-x-auto scrollbar-hide',
          variant === 'pills' || variant === 'default' ? 'space-x-1' : 'space-x-0'
        )}
      >
        {visibleTabs.map((tab) => {
          const isActive = tab.id === activeTab

          return (
            <button
              key={tab.id}
              type="button"
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              tabIndex={isActive ? 0 : -1}
              disabled={tab.disabled}
              className={tabClasses(tab, isActive)}
              onClick={() => !tab.disabled && onTabChange(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
            >
              {/* Icon */}
              {tab.icon && (
                <span className="flex-shrink-0" aria-hidden="true">
                  {tab.icon}
                </span>
              )}

              {/* Label */}
              <span className={cn(
                size === 'sm' && 'hidden sm:inline',
                'truncate'
              )}>
                {tab.label}
              </span>

              {/* Badge */}
              {tab.badge && (
                <Badge 
                  count={tab.badge} 
                  color={tab.badgeColor || 'default'} 
                />
              )}
            </button>
          )
        })}
      </div>

      {/* Right scroll button */}
      {showScrollButtons && canScrollRight && (
        <button
          type="button"
          onClick={scrollRight}
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600  rounded-md ml-2"
          aria-label="Scroll tabs right"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      )}
    </div>
  )
}

export default TabNavigation
