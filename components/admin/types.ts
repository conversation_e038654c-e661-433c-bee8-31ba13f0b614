export interface AdminStats {
  totalEmployees: number
  activeEmployees: number
  pendingApprovals: number
  monthlyExpenses: number
  leaveRequests: number
  newHires: number
  upcomingReviews: number
  documentsToReview: number
}

export interface Employee {
  id: string
  name: string
  email: string
  department: string
  position: string
  joinDate: string
  status: string
  avatar: string
}

export interface PendingApproval {
  id: string
  type: string
  employee: string
  department: string
  details: string
  requestDate: string
  urgency: string
}

export interface UpcomingEvent {
  date: string
  time: string
  title: string
  type: string
}

export interface DepartmentStat {
  name: string
  employees: number
  growth: string
  color: string
}

export interface QuickAction {
  icon: React.ElementType
  label: string
  onClick?: () => void
}