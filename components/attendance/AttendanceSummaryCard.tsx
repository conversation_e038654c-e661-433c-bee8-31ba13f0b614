"use client"

import { Card, CardContent } from "@/components/ui/card"
import { LucideIcon } from "lucide-react"

interface AttendanceSummaryCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  color: "green" | "red" | "amber" | "blue" | "purple"
  gradient?: boolean
}

const colorClasses = {
  green: {
    bg: "bg-gradient-to-br from-green-50 to-emerald-50",
    text: "text-green-600",
    valueText: "text-green-700",
    iconBg: "bg-green-500"
  },
  red: {
    bg: "bg-gradient-to-br from-red-50 to-rose-50",
    text: "text-red-600",
    valueText: "text-red-700",
    iconBg: "bg-red-500"
  },
  amber: {
    bg: "bg-gradient-to-br from-amber-50 to-yellow-50",
    text: "text-amber-600",
    valueText: "text-amber-700",
    iconBg: "bg-amber-500"
  },
  blue: {
    bg: "bg-gradient-to-br from-blue-50 to-cyan-50",
    text: "text-blue-600",
    valueText: "text-blue-700",
    iconBg: "bg-blue-500"
  },
  purple: {
    bg: "bg-gradient-to-br from-purple-50 to-indigo-50",
    text: "text-purple-600",
    valueText: "text-purple-700",
    iconBg: "bg-purple-500"
  }
}

export function AttendanceSummaryCard({
  title,
  value,
  icon: Icon,
  color,
  gradient = true
}: AttendanceSummaryCardProps) {
  const colors = colorClasses[color]

  return (
    <Card className={`shadow-lg border-0 ${gradient ? colors.bg : ""}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className={`text-sm font-medium ${colors.text}`}>{title}</p>
            <p className={`text-3xl font-bold ${colors.valueText}`}>{value}</p>
          </div>
          <div className={`w-12 h-12 ${colors.iconBg} rounded-xl flex items-center justify-center`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}