"use client"

import { Badge } from "@/components/ui/badge"

interface SessionFlagBadgeProps {
  flags: string[]
}

export function SessionFlagBadge({ flags }: SessionFlagBadgeProps) {
  if (flags.includes("absent")) {
    return <Badge variant="destructive" className="text-xs">Absent</Badge>
  }
  if (flags.includes("late")) {
    return <Badge variant="destructive" className="text-xs">Late</Badge>
  }
  if (flags.includes("early-logout")) {
    return <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-800">Early Out</Badge>
  }
  if (flags.includes("half-day")) {
    return <Badge variant="outline" className="text-xs">Half Day</Badge>
  }
  if (flags.includes("missed-swipe")) {
    return <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">Missed Swipe</Badge>
  }
  return <Badge variant="default" className="text-xs bg-green-100 text-green-800">On Time</Badge>
}