"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"

interface SwipeLogModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  employeeName: string
  swipes: string[]
}

export function SwipeLogModal({ open, onOpenChange, employeeName, swipes }: SwipeLogModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Swipe Records - {employeeName}</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          {swipes.length > 0 ? (
            swipes.map((swipe, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Swipe {index + 1}</span>
                <span className="text-muted-foreground">{swipe}</span>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No swipe records found for this employee
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}