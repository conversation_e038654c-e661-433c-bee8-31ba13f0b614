import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertTriangle, Phone, Mail, MoreHorizontal, Eye, AlertCircle } from "lucide-react"

interface Absentee {
  id: string
  name: string
  department: string
  role: string
  lastActiveDate: string
  daysAbsent: number
  status: string
  contactAttempts: number
  emergencyContact: string
  manager: string
  reason?: string | null
}

interface AbsenteeCardProps {
  absentee: Absentee
  onStatusUpdate?: (id: string, status: string) => void
  onContactAttempt?: (id: string, type: "email" | "phone") => void
}

function getStatusBadge(status: string) {
  switch (status) {
    case "unnotified":
      return <Badge className="bg-red-100 text-red-700">Unnotified</Badge>
    case "notified":
      return <Badge className="bg-yellow-100 text-yellow-700">Notified</Badge>
    case "investigating":
      return <Badge className="bg-orange-100 text-orange-700">Investigating</Badge>
    case "emergency":
      return <Badge className="bg-purple-100 text-purple-700">Emergency</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getSeverityColor(daysAbsent: number) {
  if (daysAbsent >= 5) return "text-red-600"
  if (daysAbsent >= 3) return "text-orange-600"
  return "text-yellow-600"
}

export function AbsenteeCard({ absentee, onStatusUpdate, onContactAttempt }: AbsenteeCardProps) {
  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {absentee.daysAbsent >= 5 && (
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
            )}
            <div>
              <CardTitle className="text-lg">{absentee.name}</CardTitle>
              <p className="text-sm text-gray-500">{absentee.id} • {absentee.department}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(absentee.status)}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onStatusUpdate?.(absentee.id, "notified")}>
                  <Mail className="mr-2 h-4 w-4" />
                  Mark as Notified
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusUpdate?.(absentee.id, "investigating")}>
                  <Eye className="mr-2 h-4 w-4" />
                  Start Investigation
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusUpdate?.(absentee.id, "emergency")}>
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Mark as Emergency
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onContactAttempt?.(absentee.id, "phone")}>
                  <Phone className="mr-2 h-4 w-4" />
                  Call Emergency Contact
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600">Last Active</p>
            <p className="text-sm text-gray-900">{new Date(absentee.lastActiveDate).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Days Absent</p>
            <p className={`text-lg font-bold ${getSeverityColor(absentee.daysAbsent)}`}>
              {absentee.daysAbsent}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Contact Attempts</p>
            <p className="text-sm text-gray-900">{absentee.contactAttempts}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Manager</p>
            <p className="text-sm text-gray-900">{absentee.manager}</p>
          </div>
        </div>

        {absentee.reason && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600 mb-1">Notes</p>
            <p className="text-sm text-gray-900">{absentee.reason}</p>
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1 gap-2"
            onClick={() => onContactAttempt?.(absentee.id, "email")}
          >
            <Mail className="h-4 w-4" />
            Email
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1 gap-2"
            onClick={() => onContactAttempt?.(absentee.id, "phone")}
          >
            <Phone className="h-4 w-4" />
            Call
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}