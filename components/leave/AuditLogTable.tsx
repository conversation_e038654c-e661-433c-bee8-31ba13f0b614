import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye } from "lucide-react"

interface AuditLog {
  id: string
  employeeName: string
  employeeId: string
  action: string
  actionType: string
  performedBy: string
  performedById: string
  timestamp: string
  remarks: string
  leaveRequestId?: string | null
  previousStatus: string
  newStatus: string
  ipAddress: string
  userAgent: string
}

interface AuditLogTableProps {
  logs: AuditLog[]
  onViewDetails?: (log: AuditLog) => void
  title?: string
}

function getActionBadge(actionType: string) {
  switch (actionType) {
    case "approval":
      return <Badge className="bg-green-100 text-green-700">Approval</Badge>
    case "rejection":
      return <Badge className="bg-red-100 text-red-700">Rejection</Badge>
    case "balance_edit":
      return <Badge className="bg-blue-100 text-blue-700">Balance Edit</Badge>
    case "policy_change":
      return <Badge className="bg-purple-100 text-purple-700">Policy Change</Badge>
    case "cancellation":
      return <Badge className="bg-orange-100 text-orange-700">Cancellation</Badge>
    default:
      return <Badge variant="secondary">{actionType}</Badge>
  }
}

function formatTimestamp(timestamp: string) {
  const date = new Date(timestamp)
  return {
    date: date.toLocaleDateString(),
    time: date.toLocaleTimeString(),
    relative: getRelativeTime(date)
  }
}

function getRelativeTime(date: Date) {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return "Just now"
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  return `${diffInWeeks}w ago`
}

export function AuditLogTable({ logs, onViewDetails, title = "Audit Trail" }: AuditLogTableProps) {
  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-b">
              <TableHead className="font-semibold">Employee</TableHead>
              <TableHead className="font-semibold">Action</TableHead>
              <TableHead className="font-semibold">Performed By</TableHead>
              <TableHead className="font-semibold">Timestamp</TableHead>
              <TableHead className="font-semibold">Status Change</TableHead>
              <TableHead className="font-semibold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {logs.map((log) => {
              const timestamp = formatTimestamp(log.timestamp)
              return (
                <TableRow key={log.id} className="hover:bg-gray-50">
                  <TableCell>
                    <div>
                      <div className="font-medium text-gray-900">{log.employeeName}</div>
                      <div className="text-sm text-gray-500">{log.employeeId}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getActionBadge(log.actionType)}
                      <div className="text-sm text-gray-600">{log.action}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-gray-900">{log.performedBy}</div>
                      <div className="text-sm text-gray-500">{log.performedById}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="font-medium">{timestamp.date}</div>
                      <div className="text-gray-500">{timestamp.time}</div>
                      <div className="text-xs text-gray-400">{timestamp.relative}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="text-gray-500">From: {log.previousStatus}</div>
                      <div className="text-gray-900">To: {log.newStatus}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-8 w-8 p-0"
                      onClick={() => onViewDetails?.(log)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}