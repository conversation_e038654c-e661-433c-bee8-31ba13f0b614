import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { FileUp, Download, Upload, CheckCircle, AlertCircle, X } from "lucide-react"
import { useState, useRef } from "react"

interface CSVUploadProps {
  isOpen: boolean
  onClose: () => void
  onUpload: (file: File) => Promise<void>
  title?: string
  description?: string
  sampleData?: string[][]
}

interface UploadState {
  file: File | null
  uploading: boolean
  progress: number
  error: string | null
  success: boolean
}

export function CSVUpload({ 
  isOpen, 
  onClose, 
  onUpload, 
  title = "Upload CSV File",
  description = "Upload a CSV file to bulk import data",
  sampleData
}: CSVUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    uploading: false,
    progress: 0,
    error: null,
    success: false
  })
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setUploadState(prev => ({ 
          ...prev, 
          error: 'Please select a valid CSV file',
          file: null 
        }))
        return
      }
      
      setUploadState(prev => ({ 
        ...prev, 
        file, 
        error: null,
        success: false 
      }))
    }
  }

  const handleUpload = async () => {
    if (!uploadState.file) return

    setUploadState(prev => ({ 
      ...prev, 
      uploading: true, 
      progress: 0, 
      error: null 
    }))

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadState(prev => {
          if (prev.progress >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return { ...prev, progress: prev.progress + 10 }
        })
      }, 200)

      await onUpload(uploadState.file)
      
      clearInterval(progressInterval)
      setUploadState(prev => ({ 
        ...prev, 
        uploading: false, 
        progress: 100, 
        success: true 
      }))

      // Auto close after success
      setTimeout(() => {
        handleClose()
      }, 2000)

    } catch (error) {
      setUploadState(prev => ({ 
        ...prev, 
        uploading: false, 
        progress: 0,
        error: error instanceof Error ? error.message : 'Upload failed' 
      }))
    }
  }

  const handleClose = () => {
    setUploadState({
      file: null,
      uploading: false,
      progress: 0,
      error: null,
      success: false
    })
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    onClose()
  }

  const downloadSample = () => {
    if (!sampleData) return

    const csvContent = sampleData.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sample_template.csv'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-600">{description}</p>

          {/* Sample Template Download */}
          {sampleData && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-900">Need a template?</p>
                  <p className="text-xs text-blue-700">Download our sample CSV format</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={downloadSample}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  Sample
                </Button>
              </div>
            </div>
          )}

          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Drop your CSV file here, or click to browse
              </p>
              <Input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                className="hidden"
                id="csv-upload"
                disabled={uploadState.uploading}
              />
              <Label htmlFor="csv-upload" className="cursor-pointer">
                <Button 
                  variant="outline" 
                  className="mt-2" 
                  disabled={uploadState.uploading}
                  asChild
                >
                  <span>Choose File</span>
                </Button>
              </Label>
            </div>
          </div>

          {/* Selected File Info */}
          {uploadState.file && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <FileUp className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">{uploadState.file.name}</span>
                <span className="text-xs text-gray-500">
                  ({(uploadState.file.size / 1024).toFixed(1)} KB)
                </span>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {uploadState.uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadState.progress}%</span>
              </div>
              <Progress value={uploadState.progress} className="w-full" />
            </div>
          )}

          {/* Success Message */}
          {uploadState.success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                File uploaded successfully! Processing data...
              </AlertDescription>
            </Alert>
          )}

          {/* Error Message */}
          {uploadState.error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {uploadState.error}
              </AlertDescription>
            </Alert>
          )}

          {/* CSV Format Info */}
          <div className="text-xs text-gray-500 space-y-1">
            <p className="font-medium">CSV Format Requirements:</p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>First row should contain column headers</li>
              <li>Use comma (,) as delimiter</li>
              <li>Ensure all required fields are included</li>
              <li>Maximum file size: 5MB</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button 
              onClick={handleUpload} 
              disabled={!uploadState.file || uploadState.uploading || uploadState.success}
              className="flex-1 gap-2"
            >
              {uploadState.uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Upload & Process
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleClose} 
              disabled={uploadState.uploading}
              className="flex-1 gap-2"
            >
              <X className="h-4 w-4" />
              {uploadState.success ? 'Close' : 'Cancel'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}