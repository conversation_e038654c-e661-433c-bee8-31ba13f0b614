import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Edit } from "lucide-react"

interface LeaveBalance {
  used: number
  total: number
  remaining: number
}

interface Employee {
  id: string
  name: string
  department: string
  role: string
  sickLeave: LeaveBalance
  casualLeave: LeaveBalance
  emergencyLeave: LeaveBalance
  totalPaidLeaves: number
  totalUnpaidLeaves: number
  lastUpdated: string
}

interface LeaveBalanceTableProps {
  employees: Employee[]
  onEditBalance?: (employee: Employee) => void
}

function getUsageColor(used: number, total: number) {
  const percentage = (used / total) * 100
  if (percentage >= 80) return "text-red-600"
  if (percentage >= 60) return "text-yellow-600"
  return "text-green-600"
}

function LeaveProgressBar({ used, total, color }: { used: number, total: number, color: string }) {
  const percentage = (used / total) * 100
  
  return (
    <div className="space-y-1">
      <div className={`text-sm font-medium ${getUsageColor(used, total)}`}>
        {used} / {total}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${color}`}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
      <div className="text-xs text-gray-500">{total - used} remaining</div>
    </div>
  )
}

export function LeaveBalanceTable({ employees, onEditBalance }: LeaveBalanceTableProps) {
  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader>
        <CardTitle>Employee Leave Balances</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-b">
              <TableHead className="font-semibold">Employee</TableHead>
              <TableHead className="font-semibold">Sick Leave</TableHead>
              <TableHead className="font-semibold">Casual Leave</TableHead>
              <TableHead className="font-semibold">Emergency Leave</TableHead>
              <TableHead className="font-semibold">Total Leaves</TableHead>
              <TableHead className="font-semibold">Last Updated</TableHead>
              <TableHead className="font-semibold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {employees.map((employee) => (
              <TableRow key={employee.id} className="hover:bg-gray-50">
                <TableCell>
                  <div>
                    <div className="font-medium text-gray-900">{employee.name}</div>
                    <div className="text-sm text-gray-500">{employee.id}</div>
                    <div className="text-xs text-gray-400">{employee.department} • {employee.role}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <LeaveProgressBar 
                    used={employee.sickLeave.used} 
                    total={employee.sickLeave.total} 
                    color="bg-blue-600" 
                  />
                </TableCell>
                <TableCell>
                  <LeaveProgressBar 
                    used={employee.casualLeave.used} 
                    total={employee.casualLeave.total} 
                    color="bg-green-600" 
                  />
                </TableCell>
                <TableCell>
                  <LeaveProgressBar 
                    used={employee.emergencyLeave.used} 
                    total={employee.emergencyLeave.total} 
                    color="bg-red-600" 
                  />
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-gray-900">
                      Paid: {employee.totalPaidLeaves}
                    </div>
                    <div className="text-sm text-gray-600">
                      Unpaid: {employee.totalUnpaidLeaves}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-600">
                    {new Date(employee.lastUpdated).toLocaleDateString()}
                  </div>
                </TableCell>
                <TableCell>
                  {onEditBalance && (
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-8 w-8 p-0"
                      onClick={() => onEditBalance(employee)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}