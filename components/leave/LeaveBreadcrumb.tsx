import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"

const breadcrumbMap: Record<string, string> = {
  "/admin/leave-management": "Dashboard",
  "/admin/leave-management/requests": "Leave Requests",
  "/admin/leave-management/calendar": "Leave Calendar",
  "/admin/leave-management/balance": "Balance Management",
  "/admin/leave-management/reports": "Reports & Analytics",
  "/admin/leave-management/absentees": "Absentees Tracker",
  "/admin/leave-management/audit": "Audit Logs",
  "/admin/leave-management/policies": "Leave Policies",
  "/admin/leave-management/settings": "Settings"
}

export function LeaveBreadcrumb() {
  const pathname = usePathname()
  
  // Handle dynamic routes like /admin/leave-management/requests/[id]
  const getPageTitle = (path: string) => {
    if (path.includes("/requests/") && path !== "/admin/leave-management/requests") {
      return "Request Details"
    }
    return breadcrumbMap[path] || "Unknown Page"
  }

  const isDetailPage = pathname.includes("/requests/") && pathname !== "/admin/leave-management/requests"
  
  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
      <Link href="/admin" className="hover:text-gray-900 transition-colors">
        <Home className="h-4 w-4" />
      </Link>
      <ChevronRight className="h-4 w-4" />
      <Link href="/admin/leave-management" className="hover:text-gray-900 transition-colors">
        Leave Management
      </Link>
      
      {pathname !== "/admin/leave-management" && (
        <>
          <ChevronRight className="h-4 w-4" />
          {isDetailPage ? (
            <>
              <Link href="/admin/leave-management/requests" className="hover:text-gray-900 transition-colors">
                Leave Requests
              </Link>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-900 font-medium">Request Details</span>
            </>
          ) : (
            <span className="text-gray-900 font-medium">{getPageTitle(pathname)}</span>
          )}
        </>
      )}
    </nav>
  )
}