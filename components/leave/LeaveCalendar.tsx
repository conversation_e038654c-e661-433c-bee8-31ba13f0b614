import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface LeaveRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  leaveType: string
  startDate: string
  endDate: string
  status: string
}

interface LeaveCalendarProps {
  leaves: LeaveRecord[]
  currentDate: Date
  onDateChange: (date: Date) => void
  viewMode: "weekly" | "monthly"
  departmentFilter?: string
}

const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
const months = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
]

function getLeaveTypeColor(leaveType: string) {
  switch (leaveType) {
    case "Sick Leave":
      return "bg-blue-100 text-blue-700 border-blue-200"
    case "Casual Leave":
      return "bg-green-100 text-green-700 border-green-200"
    case "Emergency Leave":
      return "bg-red-100 text-red-700 border-red-200"
    case "Holiday Leave":
      return "bg-yellow-100 text-yellow-700 border-yellow-200"
    default:
      return "bg-gray-100 text-gray-700 border-gray-200"
  }
}

function isDateInRange(date: Date, startDate: string, endDate: string) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  return date >= start && date <= end
}

function getEmployeesOnLeave(date: Date, leaves: LeaveRecord[], departmentFilter?: string) {
  return leaves.filter(leave => {
    const matchesDepartment = !departmentFilter || departmentFilter === "all" || leave.department === departmentFilter
    return matchesDepartment && isDateInRange(date, leave.startDate, leave.endDate)
  })
}

function CalendarDay({ 
  date, 
  leaves, 
  departmentFilter 
}: { 
  date: Date
  leaves: LeaveRecord[]
  departmentFilter?: string 
}) {
  const employeesOnLeave = getEmployeesOnLeave(date, leaves, departmentFilter)
  const isToday = date.toDateString() === new Date().toDateString()
  const isCurrentMonth = date.getMonth() === new Date().getMonth()

  return (
    <div className={`min-h-24 p-2 border border-gray-200 ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'} ${isToday ? 'ring-2 ring-blue-500' : ''}`}>
      <div className={`text-sm font-medium mb-1 ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}`}>
        {date.getDate()}
      </div>
      <div className="space-y-1">
        {employeesOnLeave.slice(0, 2).map((leave, index) => (
          <Popover key={index}>
            <PopoverTrigger asChild>
              <div className={`text-xs px-1 py-0.5 rounded cursor-pointer truncate ${getLeaveTypeColor(leave.leaveType)}`}>
                {leave.employeeName}
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3">
              <div className="space-y-2">
                <div className="font-medium">{leave.employeeName}</div>
                <div className="text-sm text-gray-600">ID: {leave.employeeId}</div>
                <div className="text-sm text-gray-600">Department: {leave.department}</div>
                <Badge className={getLeaveTypeColor(leave.leaveType)}>
                  {leave.leaveType}
                </Badge>
                <div className="text-sm text-gray-600">
                  {new Date(leave.startDate).toLocaleDateString()} - {new Date(leave.endDate).toLocaleDateString()}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        ))}
        {employeesOnLeave.length > 2 && (
          <div className="text-xs text-gray-500 px-1">
            +{employeesOnLeave.length - 2} more
          </div>
        )}
      </div>
    </div>
  )
}

function WeeklyView({ currentDate, leaves, departmentFilter }: { 
  currentDate: Date
  leaves: LeaveRecord[]
  departmentFilter?: string 
}) {
  const startOfWeek = new Date(currentDate)
  startOfWeek.setDate(currentDate.getDate() - currentDate.getDay())

  const weekDates = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)
    return date
  })

  return (
    <div className="grid grid-cols-7 gap-1">
      {weekDays.map(day => (
        <div key={day} className="p-3 text-center font-medium text-gray-700 bg-gray-50 border border-gray-200">
          {day}
        </div>
      ))}
      {weekDates.map((date, index) => (
        <CalendarDay key={index} date={date} leaves={leaves} departmentFilter={departmentFilter} />
      ))}
    </div>
  )
}

function MonthlyView({ currentDate, leaves, departmentFilter }: { 
  currentDate: Date
  leaves: LeaveRecord[]
  departmentFilter?: string 
}) {
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  
  const firstDayOfMonth = new Date(year, month, 1)
  const lastDayOfMonth = new Date(year, month + 1, 0)
  const startDate = new Date(firstDayOfMonth)
  startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay())
  
  const endDate = new Date(lastDayOfMonth)
  endDate.setDate(endDate.getDate() + (6 - lastDayOfMonth.getDay()))

  const calendarDates = []
  const currentDateIter = new Date(startDate)
  
  while (currentDateIter <= endDate) {
    calendarDates.push(new Date(currentDateIter))
    currentDateIter.setDate(currentDateIter.getDate() + 1)
  }

  return (
    <div className="grid grid-cols-7 gap-1">
      {weekDays.map(day => (
        <div key={day} className="p-3 text-center font-medium text-gray-700 bg-gray-50 border border-gray-200">
          {day}
        </div>
      ))}
      {calendarDates.map((date, index) => (
        <CalendarDay key={index} date={date} leaves={leaves} departmentFilter={departmentFilter} />
      ))}
    </div>
  )
}

export function LeaveCalendar({ 
  leaves, 
  currentDate, 
  onDateChange, 
  viewMode, 
  departmentFilter 
}: LeaveCalendarProps) {
  const navigate = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (viewMode === "monthly") {
      if (direction === "prev") {
        newDate.setMonth(currentDate.getMonth() - 1)
      } else {
        newDate.setMonth(currentDate.getMonth() + 1)
      }
    } else {
      if (direction === "prev") {
        newDate.setDate(currentDate.getDate() - 7)
      } else {
        newDate.setDate(currentDate.getDate() + 7)
      }
    }
    onDateChange(newDate)
  }

  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => navigate("prev")}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold">
              {viewMode === "monthly" 
                ? `${months[currentDate.getMonth()]} ${currentDate.getFullYear()}`
                : `Week of ${currentDate.toLocaleDateString()}`
              }
            </h2>
            <Button variant="outline" size="sm" onClick={() => navigate("next")}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <Button variant="outline" size="sm" onClick={() => onDateChange(new Date())}>
            Today
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === "monthly" ? (
          <MonthlyView 
            currentDate={currentDate} 
            leaves={leaves} 
            departmentFilter={departmentFilter}
          />
        ) : (
          <WeeklyView 
            currentDate={currentDate} 
            leaves={leaves} 
            departmentFilter={departmentFilter}
          />
        )}
      </CardContent>
    </Card>
  )
}