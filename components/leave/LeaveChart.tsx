import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts"

interface LeaveChartProps {
  type: "pie" | "bar"
  title: string
  subtitle?: string
  data: any[]
  colors?: string[]
  height?: number
}

const DEFAULT_COLORS = ['#ef4444', '#3b82f6', '#f59e0b', '#10b981', '#8b5cf6', '#f97316']

export function LeaveChart({ 
  type, 
  title, 
  subtitle, 
  data, 
  colors = DEFAULT_COLORS, 
  height = 300 
}: LeaveChartProps) {
  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  )

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="sick" stackId="a" fill="#ef4444" name="Sick" />
        <Bar dataKey="casual" stackId="a" fill="#3b82f6" name="Casual" />
        <Bar dataKey="emergency" stackId="a" fill="#f59e0b" name="Emergency" />
        <Bar dataKey="holiday" stackId="a" fill="#10b981" name="Holiday" />
      </BarChart>
    </ResponsiveContainer>
  )

  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-gray-900">{title}</CardTitle>
        {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        {type === "pie" ? renderPieChart() : renderBarChart()}
      </CardContent>
    </Card>
  )
}