"use client"

import { ReactNode } from "react"
import { usePathname } from "next/navigation"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { LeaveQuickNav } from "./LeaveQuickNav"

interface LeaveManagementLayoutProps {
  children: ReactNode
}

export function LeaveManagementLayout({ children }: LeaveManagementLayoutProps) {
  const pathname = usePathname()
  const isLeaveManagementPage = pathname.startsWith("/admin/leave-management")

  return (
    <AdminLayout>
      {children}
      {isLeaveManagementPage && <LeaveQuickNav />}
    </AdminLayout>
  )
}