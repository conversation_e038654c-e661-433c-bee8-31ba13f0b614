import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  LayoutDashboard, 
  FileText, 
  Calendar, 
  Users, 
  BarChart3, 
  UserX, 
  Shield, 
  Settings,
  ChevronRight
} from "lucide-react"

const navigationItems = [
  {
    title: "Dashboard",
    href: "/admin/leave-management",
    icon: LayoutDashboard,
    description: "Overview and analytics"
  },
  {
    title: "Leave Requests",
    href: "/admin/leave-management/requests",
    icon: FileText,
    description: "Manage leave applications",
    badge: "12" // Example pending count
  },
  {
    title: "Leave Calendar",
    href: "/admin/leave-management/calendar",
    icon: Calendar,
    description: "Visual leave schedule"
  },
  {
    title: "Balance Management",
    href: "/admin/leave-management/balance",
    icon: Users,
    description: "Employee leave balances"
  },
  {
    title: "Reports & Analytics",
    href: "/admin/leave-management/reports",
    icon: BarChart3,
    description: "Generate leave reports"
  },
  {
    title: "Absentees Tracker",
    href: "/admin/leave-management/absentees",
    icon: UserX,
    description: "Track unnotified absences",
    badge: "3", // Example absentee count
    badgeVariant: "destructive" as const
  },
  {
    title: "Audit Logs",
    href: "/admin/leave-management/audit",
    icon: Shield,
    description: "System activity logs"
  },
  {
    title: "Leave Policies",
    href: "/admin/leave-management/policies",
    icon: FileText,
    description: "Configure leave policies"
  },
  {
    title: "Settings",
    href: "/admin/leave-management/settings",
    icon: Settings,
    description: "System configuration"
  }
]

interface LeaveNavigationProps {
  variant?: "sidebar" | "grid" | "list"
  showDescriptions?: boolean
}

export function LeaveNavigation({ variant = "grid", showDescriptions = true }: LeaveNavigationProps) {
  const pathname = usePathname()

  if (variant === "sidebar") {
    return (
      <div className="space-y-1">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.href} href={item.href}>
              <Button
                variant={isActive ? "default" : "ghost"}
                className={`w-full justify-start gap-3 ${isActive ? "bg-blue-600 text-white" : "text-gray-700 hover:bg-gray-100"}`}
              >
                <item.icon className="h-4 w-4" />
                <span className="flex-1 text-left">{item.title}</span>
                {item.badge && (
                  <Badge 
                    variant={item.badgeVariant || "secondary"} 
                    className="ml-auto"
                  >
                    {item.badge}
                  </Badge>
                )}
                <ChevronRight className="h-4 w-4 opacity-50" />
              </Button>
            </Link>
          )
        })}
      </div>
    )
  }

  if (variant === "list") {
    return (
      <div className="space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.href} href={item.href}>
              <div className={`flex items-center gap-4 p-4 rounded-xl border transition-all hover:shadow-md ${
                isActive 
                  ? "bg-blue-50 border-blue-200 shadow-sm" 
                  : "bg-white border-gray-200 hover:border-gray-300"
              }`}>
                <div className={`p-2 rounded-lg ${
                  isActive ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"
                }`}>
                  <item.icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className={`font-medium ${isActive ? "text-blue-900" : "text-gray-900"}`}>
                      {item.title}
                    </h3>
                    {item.badge && (
                      <Badge variant={item.badgeVariant || "secondary"}>
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                  {showDescriptions && (
                    <p className="text-sm text-gray-500 mt-1">{item.description}</p>
                  )}
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>
            </Link>
          )
        })}
      </div>
    )
  }

  // Grid variant (default)
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {navigationItems.map((item) => {
        const isActive = pathname === item.href
        return (
          <Link key={item.href} href={item.href}>
            <div className={`p-6 rounded-2xl border transition-all hover:shadow-lg cursor-pointer ${
              isActive 
                ? "bg-blue-50 border-blue-200 shadow-md" 
                : "bg-white border-gray-200 hover:border-gray-300"
            }`}>
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-xl ${
                  isActive ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"
                }`}>
                  <item.icon className="h-6 w-6" />
                </div>
                {item.badge && (
                  <Badge variant={item.badgeVariant || "secondary"}>
                    {item.badge}
                  </Badge>
                )}
              </div>
              <h3 className={`text-lg font-semibold mb-2 ${
                isActive ? "text-blue-900" : "text-gray-900"
              }`}>
                {item.title}
              </h3>
              {showDescriptions && (
                <p className="text-sm text-gray-600">{item.description}</p>
              )}
            </div>
          </Link>
        )
      })}
    </div>
  )
}