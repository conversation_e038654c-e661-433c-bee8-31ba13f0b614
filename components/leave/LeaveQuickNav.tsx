import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>over, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { 
  Menu, 
  LayoutDashboard, 
  FileText, 
  Calendar, 
  Users, 
  BarChart3, 
  UserX, 
  Shield, 
  Settings 
} from "lucide-react"

const quickNavItems = [
  {
    title: "Dashboard",
    href: "/admin/leave-management",
    icon: LayoutDashboard,
    color: "text-blue-600"
  },
  {
    title: "Requests",
    href: "/admin/leave-management/requests",
    icon: FileText,
    color: "text-green-600",
    badge: "12"
  },
  {
    title: "Calendar",
    href: "/admin/leave-management/calendar",
    icon: Calendar,
    color: "text-purple-600"
  },
  {
    title: "Balances",
    href: "/admin/leave-management/balance",
    icon: Users,
    color: "text-indigo-600"
  },
  {
    title: "Reports",
    href: "/admin/leave-management/reports",
    icon: BarChart3,
    color: "text-orange-600"
  },
  {
    title: "Absentees",
    href: "/admin/leave-management/absentees",
    icon: UserX,
    color: "text-red-600",
    badge: "3"
  },
  {
    title: "Audit",
    href: "/admin/leave-management/audit",
    icon: Shield,
    color: "text-gray-600"
  },
  {
    title: "Settings",
    href: "/admin/leave-management/settings",
    icon: Settings,
    color: "text-slate-600"
  }
]

export function LeaveQuickNav() {
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Popover>
        <PopoverTrigger asChild>
          <Button 
            size="lg" 
            className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-200 bg-blue-600 hover:bg-blue-700"
          >
            <Menu className="h-6 w-6" />
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          side="top" 
          align="end" 
          className="w-64 p-2 mb-2"
        >
          <div className="space-y-1">
            <div className="px-3 py-2 text-sm font-medium text-gray-700 border-b border-gray-100 mb-2">
              Quick Navigation
            </div>
            {quickNavItems.map((item) => (
              <Link key={item.href} href={item.href}>
                <Button
                  variant="ghost"
                  className="w-full justify-start gap-3 h-10 px-3"
                >
                  <item.icon className={`h-4 w-4 ${item.color}`} />
                  <span className="flex-1 text-left">{item.title}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="ml-auto text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              </Link>
            ))}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}