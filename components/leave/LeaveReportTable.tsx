import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface LeaveRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  leaveType: string
  totalDays: number
  isPaid: boolean
  status: string
  startDate: string
  endDate: string
  appliedDate: string
}

interface LeaveReportTableProps {
  data: LeaveRecord[]
  title?: string
}

function getStatusBadge(status: string) {
  switch (status) {
    case "approved":
      return <Badge className="bg-green-100 text-green-700">Approved</Badge>
    case "rejected":
      return <Badge className="bg-red-100 text-red-700">Rejected</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export function LeaveReportTable({ data, title = "Leave Records" }: LeaveReportTableProps) {
  return (
    <Card className="rounded-2xl shadow-md">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Leave Type</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Days</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{record.employeeName}</div>
                    <div className="text-sm text-gray-500">{record.employeeId}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{record.department}</Badge>
                </TableCell>
                <TableCell>{record.leaveType}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{new Date(record.startDate).toLocaleDateString()}</div>
                    <div className="text-gray-500">to {new Date(record.endDate).toLocaleDateString()}</div>
                  </div>
                </TableCell>
                <TableCell className="font-medium">{record.totalDays}</TableCell>
                <TableCell>
                  <Badge variant={record.isPaid ? "default" : "secondary"}>
                    {record.isPaid ? "Paid" : "Unpaid"}
                  </Badge>
                </TableCell>
                <TableCell>{getStatusBadge(record.status)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}