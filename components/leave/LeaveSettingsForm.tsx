import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { Save, RotateCcw } from "lucide-react"

interface SettingsSection {
  [key: string]: any
}

interface LeaveSettings {
  notifications: SettingsSection
  rules: SettingsSection
  automation: SettingsSection
  security: SettingsSection
}

interface LeaveSettingsFormProps {
  settings: LeaveSettings
  onSettingChange: (category: string, key: string, value: any) => void
  onSave: () => void
  onReset: () => void
  hasChanges: boolean
  saving?: boolean
}

interface SettingItemProps {
  label: string
  description: string
  children: React.ReactNode
}

function SettingItem({ label, description, children }: SettingItemProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <Label className="text-base font-medium">{label}</Label>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      {children}
    </div>
  )
}

interface SliderSettingProps {
  label: string
  description: string
  value: number
  onChange: (value: number) => void
  min: number
  max: number
  step?: number
  unit: string
}

function SliderSetting({ label, description, value, onChange, min, max, step = 1, unit }: SliderSettingProps) {
  return (
    <div className="space-y-4">
      <SettingItem label={label} description={description}>
        <Switch checked={true} />
      </SettingItem>
      <div className="ml-6 space-y-2">
        <Label>{label.toLowerCase()} ({unit})</Label>
        <div className="flex items-center space-x-4">
          <Slider
            value={[value]}
            onValueChange={(values) => onChange(values[0])}
            max={max}
            min={min}
            step={step}
            className="flex-1"
          />
          <span className="w-16 text-sm font-medium">{value} {unit}</span>
        </div>
      </div>
    </div>
  )
}

export function LeaveSettingsForm({ 
  settings, 
  onSettingChange, 
  onSave, 
  onReset, 
  hasChanges, 
  saving = false 
}: LeaveSettingsFormProps) {
  return (
    <div className="space-y-6">
      {/* Notification Settings */}
      <Card className="rounded-2xl shadow-md">
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <SettingItem
            label="Email on Approval"
            description="Send confirmation email when leave is approved"
          >
            <Switch
              checked={settings.notifications.emailOnApproval}
              onCheckedChange={(checked) => onSettingChange("notifications", "emailOnApproval", checked)}
            />
          </SettingItem>

          <Separator />

          <SettingItem
            label="Email on Rejection"
            description="Send notification email when leave is rejected"
          >
            <Switch
              checked={settings.notifications.emailOnRejection}
              onCheckedChange={(checked) => onSettingChange("notifications", "emailOnRejection", checked)}
            />
          </SettingItem>

          <Separator />

          <SliderSetting
            label="Manager Reminder"
            description="Remind managers about pending leave requests"
            value={settings.notifications.managerReminderDays}
            onChange={(value) => onSettingChange("notifications", "managerReminderDays", value)}
            min={1}
            max={10}
            unit="days"
          />
        </CardContent>
      </Card>

      {/* Rules Settings */}
      <Card className="rounded-2xl shadow-md">
        <CardHeader>
          <CardTitle>Leave Rules</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Max Backdated Days</Label>
              <Input
                type="number"
                value={settings.rules.maxBackdatedDays}
                onChange={(e) => onSettingChange("rules", "maxBackdatedDays", parseInt(e.target.value) || 0)}
                min="0"
                max="30"
              />
            </div>

            <div className="space-y-2">
              <Label>Min Notice Hours</Label>
              <Input
                type="number"
                value={settings.rules.minNoticeHours}
                onChange={(e) => onSettingChange("rules", "minNoticeHours", parseInt(e.target.value) || 0)}
                min="0"
                max="168"
              />
            </div>
          </div>

          <Separator />

          <SettingItem
            label="Require Manager Approval"
            description="All leave requests must be approved by direct manager"
          >
            <Switch
              checked={settings.rules.requireManagerApproval}
              onCheckedChange={(checked) => onSettingChange("rules", "requireManagerApproval", checked)}
            />
          </SettingItem>

          <SettingItem
            label="Require HR Approval"
            description="Additional HR approval required for all requests"
          >
            <Switch
              checked={settings.rules.requireHRApproval}
              onCheckedChange={(checked) => onSettingChange("rules", "requireHRApproval", checked)}
            />
          </SettingItem>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2">
        <Button 
          variant="outline" 
          onClick={onReset}
          disabled={!hasChanges}
          className="gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          Reset
        </Button>
        <Button 
          onClick={onSave}
          disabled={!hasChanges || saving}
          className="gap-2"
        >
          <Save className="h-4 w-4" />
          {saving ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  )
}