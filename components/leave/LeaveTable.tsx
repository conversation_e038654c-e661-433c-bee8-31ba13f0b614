import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, Check, X } from "lucide-react"
import Link from "next/link"

interface LeaveRequest {
  id: string
  employeeName: string
  employeeId: string
  department: string
  leaveType: string
  fromDate: string
  toDate: string
  totalDays: number
  isPaid: boolean
  status: "pending" | "approved" | "rejected"
  appliedDate: string
  reason: string
}

interface LeaveTableProps {
  requests: LeaveRequest[]
  onApprove?: (id: string) => void
  onReject?: (id: string) => void
}

function getStatusBadge(status: string) {
  switch (status) {
    case "approved":
      return <Badge className="bg-green-100 text-green-700 hover:bg-green-100">Approved</Badge>
    case "rejected":
      return <Badge className="bg-red-100 text-red-700 hover:bg-red-100">Rejected</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-700 hover:bg-yellow-100">Pending</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getLeaveTypeBadge(leaveType: string) {
  switch (leaveType) {
    case "Sick Leave":
      return <Badge variant="outline" className="border-red-200 text-red-700">Sick</Badge>
    case "Casual Leave":
      return <Badge variant="outline" className="border-blue-200 text-blue-700">Casual</Badge>
    case "Emergency Leave":
      return <Badge variant="outline" className="border-orange-200 text-orange-700">Emergency</Badge>
    case "Holiday Leave":
      return <Badge variant="outline" className="border-green-200 text-green-700">Holiday</Badge>
    default:
      return <Badge variant="outline">{leaveType}</Badge>
  }
}

export function LeaveTable({ requests, onApprove, onReject }: LeaveTableProps) {
  return (
    <Card className="rounded-2xl shadow-md">
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-b">
              <TableHead className="font-semibold">Employee</TableHead>
              <TableHead className="font-semibold">Department</TableHead>
              <TableHead className="font-semibold">Leave Type</TableHead>
              <TableHead className="font-semibold">Duration</TableHead>
              <TableHead className="font-semibold">Days</TableHead>
              <TableHead className="font-semibold">Type</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {requests.map((request) => (
              <TableRow key={request.id} className="hover:bg-gray-50">
                <TableCell>
                  <div>
                    <div className="font-medium text-gray-900">{request.employeeName}</div>
                    <div className="text-sm text-gray-500">{request.employeeId}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{request.department}</Badge>
                </TableCell>
                <TableCell>
                  {getLeaveTypeBadge(request.leaveType)}
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{new Date(request.fromDate).toLocaleDateString()}</div>
                    <div className="text-gray-500">to {new Date(request.toDate).toLocaleDateString()}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-medium">{request.totalDays}</span>
                </TableCell>
                <TableCell>
                  <Badge variant={request.isPaid ? "default" : "secondary"}>
                    {request.isPaid ? "Paid" : "Unpaid"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {getStatusBadge(request.status)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Link href={`/admin/leave-management/requests/${request.id}`}>
                      <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    {request.status === "pending" && (
                      <>
                        {onApprove && (
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                            onClick={() => onApprove(request.id)}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        )}
                        {onReject && (
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            onClick={() => onReject(request.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}