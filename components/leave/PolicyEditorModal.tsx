import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Save, X } from "lucide-react"
import { useState, useEffect } from "react"

interface LeavePolicy {
  id?: string
  leaveType: string
  maxLeaves: number
  accrualType: "monthly" | "quarterly" | "yearly"
  carryForward: boolean
  isPaid: boolean
  department: string
  description: string
  createdAt?: string
  updatedAt?: string
}

interface PolicyEditorModalProps {
  policy?: LeavePolicy | null
  isOpen: boolean
  onClose: () => void
  onSave: (policy: Partial<LeavePolicy>) => void
}

export function PolicyEditorModal({ 
  policy, 
  isOpen, 
  onClose, 
  onSave 
}: PolicyEditorModalProps) {
  const [formData, setFormData] = useState<Partial<LeavePolicy>>({
    leaveType: "",
    maxLeaves: 0,
    accrualType: "yearly",
    carryForward: false,
    isPaid: true,
    department: "all",
    description: ""
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (policy) {
      setFormData(policy)
    } else {
      setFormData({
        leaveType: "",
        maxLeaves: 0,
        accrualType: "yearly",
        carryForward: false,
        isPaid: true,
        department: "all",
        description: ""
      })
    }
    setErrors({})
  }, [policy, isOpen])

  const handleInputChange = (field: keyof LeavePolicy, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.leaveType?.trim()) {
      newErrors.leaveType = "Leave type is required"
    }

    if (!formData.maxLeaves || formData.maxLeaves <= 0) {
      newErrors.maxLeaves = "Max leaves must be greater than 0"
    }

    if (!formData.description?.trim()) {
      newErrors.description = "Description is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm()) {
      return
    }

    onSave({
      ...formData,
      updatedAt: new Date().toISOString().split('T')[0]
    })
    onClose()
  }

  const handleCancel = () => {
    setFormData({
      leaveType: "",
      maxLeaves: 0,
      accrualType: "yearly",
      carryForward: false,
      isPaid: true,
      department: "all",
      description: ""
    })
    setErrors({})
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {policy ? "Edit Leave Policy" : "Add New Leave Policy"}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="leaveType">Leave Type *</Label>
              <Input
                id="leaveType"
                value={formData.leaveType || ""}
                onChange={(e) => handleInputChange("leaveType", e.target.value)}
                placeholder="e.g., Sick Leave"
                className={`mt-1 ${errors.leaveType ? 'border-red-500' : ''}`}
              />
              {errors.leaveType && (
                <p className="text-sm text-red-500 mt-1">{errors.leaveType}</p>
              )}
            </div>
            <div>
              <Label htmlFor="maxLeaves">Max Leaves *</Label>
              <Input
                id="maxLeaves"
                type="number"
                min="1"
                value={formData.maxLeaves || ""}
                onChange={(e) => handleInputChange("maxLeaves", parseInt(e.target.value) || 0)}
                className={`mt-1 ${errors.maxLeaves ? 'border-red-500' : ''}`}
              />
              {errors.maxLeaves && (
                <p className="text-sm text-red-500 mt-1">{errors.maxLeaves}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="accrualType">Accrual Type</Label>
              <Select 
                value={formData.accrualType} 
                onValueChange={(value: "monthly" | "quarterly" | "yearly") => handleInputChange("accrualType", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="department">Department</Label>
              <Select 
                value={formData.department} 
                onValueChange={(value) => handleInputChange("department", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <Label htmlFor="carryForward" className="font-medium">Carry Forward Allowed</Label>
                <p className="text-sm text-gray-500">Allow unused leaves to carry over to next year</p>
              </div>
              <Switch
                id="carryForward"
                checked={formData.carryForward || false}
                onCheckedChange={(checked) => handleInputChange("carryForward", checked)}
              />
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <Label htmlFor="isPaid" className="font-medium">Paid Leave</Label>
                <p className="text-sm text-gray-500">Employee receives salary during this leave</p>
              </div>
              <Switch
                id="isPaid"
                checked={formData.isPaid || false}
                onCheckedChange={(checked) => handleInputChange("isPaid", checked)}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description || ""}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Brief description of this leave policy..."
              className={`mt-1 ${errors.description ? 'border-red-500' : ''}`}
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-500 mt-1">{errors.description}</p>
            )}
          </div>

          <div className="flex gap-2 pt-4">
            <Button onClick={handleSave} className="flex-1 gap-2">
              <Save className="h-4 w-4" />
              Save Policy
            </Button>
            <Button variant="outline" onClick={handleCancel} className="flex-1 gap-2">
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}