-- Fix for existing user profile
-- Run this in your Supabase SQL Editor to create the missing profile

-- Insert profile for the existing user
INSERT INTO public.profiles (id, email, full_name, role, created_at, updated_at)
VALUES (
    '8a4a5270-0aa4-413c-b451-a2c2bfc3df4c',
    '<EMAIL>',
    'Praneeth Devarasetty',
    'admin',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    updated_at = NOW();

-- Verify the profile was created
SELECT * FROM public.profiles WHERE id = '8a4a5270-0aa4-413c-b451-a2c2bfc3df4c';